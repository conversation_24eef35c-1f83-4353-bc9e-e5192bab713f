#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح جميع أخطاء Terminal
Fix All Terminal Errors
"""

import re

def fix_all_database_field_mismatches():
    """إصلاح جميع عدم تطابق حقول قاعدة البيانات"""
    
    print("🔧 إصلاح عدم تطابق حقول قاعدة البيانات...")
    
    try:
        # قراءة الملف الرئيسي
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح مشاكل المستخلصات
        extract_fixes = [
            # إصلاح حقول المستخلصات
            ("result['contractor_name']", "result.get('contractor_name', '')"),
            ("result['extract_number']", "result.get('extract_number', '')"),
            ("result['extract_date']", "result.get('extract_date', '')"),
            ("result['created_by']", "result.get('created_by', 1)"),
        ]
        
        for old_field, new_field in extract_fixes:
            content = content.replace(old_field, new_field)
        
        # إصلاح مشاكل المقاولين
        contractor_fixes = [
            ("result['license_number']", "result.get('license_number', '')"),
            ("result['rating']", "result.get('rating', 0.0)"),
            ("result['notes']", "result.get('notes', '')"),
        ]
        
        for old_field, new_field in contractor_fixes:
            content = content.replace(old_field, new_field)
        
        # إصلاح مشاكل الموردين
        supplier_fixes = [
            ("result['contact_person']", "result.get('contact_person', '')"),
            ("result['tax_number']", "result.get('tax_number', '')"),
        ]
        
        for old_field, new_field in supplier_fixes:
            content = content.replace(old_field, new_field)
        
        # إصلاح مشاكل الوحدات
        unit_fixes = [
            ("result['project_id']", "result.get('project_id', 1)"),
            ("result['description']", "result.get('description', '')"),
        ]
        
        for old_field, new_field in unit_fixes:
            content = content.replace(old_field, new_field)
        
        # كتابة الملف المحدث
        with open('rafea_complete_system.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح عدم تطابق حقول قاعدة البيانات!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {e}")
        return False

def fix_dialog_field_names():
    """إصلاح أسماء الحقول في النوافذ"""
    
    print("🔧 إصلاح أسماء الحقول في النوافذ...")
    
    try:
        # قراءة ملف النوافذ
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح نافذة الفواتير لتطابق قاعدة البيانات
        invoice_old = """            invoice_data = {
                'invoice_number': self.invoice_number_entry.get().strip(),
                'supplier_id': 1,  # إضافة الحقل المفقود
                'supplier_name': self.supplier_name_entry.get().strip(),
                'amount': float(self.amount_entry.get()) if self.amount_entry.get().strip() else 0.0,
                'invoice_date': self.date_entry.get().strip(),
                'due_date': self.date_entry.get().strip(),  # إضافة الحقل المفقود
                'status': self.status_combo.get(),
                'description': '',  # إضافة الحقل المفقود
                'created_by': 1,  # إضافة الحقل المفقود
                'created_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }"""
        
        invoice_new = """            invoice_data = {
                'invoice_number': self.invoice_number_entry.get().strip(),
                'supplier_id': 1,  # إضافة الحقل المفقود
                'supplier': self.supplier_name_entry.get().strip(),  # تطابق قاعدة البيانات
                'amount': float(self.amount_entry.get()) if self.amount_entry.get().strip() else 0.0,
                'date': self.date_entry.get().strip(),  # تطابق قاعدة البيانات
                'due_date': self.date_entry.get().strip(),  # إضافة الحقل المفقود
                'status': self.status_combo.get(),
                'description': '',  # إضافة الحقل المفقود
                'created_by': 1,  # إضافة الحقل المفقود
                'created_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }"""
        
        content = content.replace(invoice_old, invoice_new)
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح أسماء الحقول في النوافذ!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {e}")
        return False

def add_error_handling():
    """إضافة معالجة أفضل للأخطاء"""
    
    print("🔧 إضافة معالجة أفضل للأخطاء...")
    
    try:
        # قراءة الملف الرئيسي
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة معالجة أخطاء شاملة لجميع دوال الإضافة
        error_handling_template = """
            except KeyError as e:
                logger.error(f"حقل مفقود في البيانات: {e}")
                messagebox.showerror("خطأ", f"حقل مفقود في البيانات: {str(e)}")
            except ValueError as e:
                logger.error(f"قيمة غير صحيحة: {e}")
                messagebox.showerror("خطأ", f"قيمة غير صحيحة: {str(e)}")
            except Exception as e:
                logger.error(f"خطأ عام: {e}")
                messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")"""
        
        # البحث عن جميع دوال الإضافة وإضافة معالجة الأخطاء
        add_functions = [
            'def add_project',
            'def add_customer', 
            'def add_unit',
            'def add_contractor',
            'def add_supplier',
            'def add_user'
        ]
        
        for func_name in add_functions:
            # البحث عن نهاية الدالة وإضافة معالجة الأخطاء
            pattern = f'({func_name}.*?except Exception as e:.*?messagebox\.showerror.*?\n)'
            replacement = lambda m: m.group(1) + error_handling_template
            content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        # كتابة الملف المحدث
        with open('rafea_complete_system.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة معالجة أفضل للأخطاء!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة معالجة الأخطاء: {e}")
        return False

def create_comprehensive_test():
    """إنشاء اختبار شامل للنظام"""
    
    test_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع وظائف النظام
Comprehensive System Test
"""

import sqlite3
import os
from datetime import datetime

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        conn = sqlite3.connect('data/rafea_system.db')
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        conn.close()
        
        print(f"✅ قاعدة البيانات متصلة - {len(tables)} جدول موجود")
        return True
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_all_modules():
    """اختبار جميع الوحدات"""
    modules = [
        'rafea_complete_system',
        'dialogs', 
        'database_sqlite',
        'user_activity_monitor',
        'multi_user_manager'
    ]
    
    success_count = 0
    for module in modules:
        try:
            __import__(module)
            print(f"✅ وحدة {module} تعمل بشكل صحيح")
            success_count += 1
        except Exception as e:
            print(f"❌ خطأ في وحدة {module}: {e}")
    
    print(f"📊 النتيجة: {success_count}/{len(modules)} وحدات تعمل")
    return success_count == len(modules)

def main():
    """الاختبار الرئيسي"""
    print("=" * 60)
    print("🧪 اختبار شامل لنظام رافع")
    print("=" * 60)
    
    # اختبار قاعدة البيانات
    db_ok = test_database_connection()
    
    # اختبار الوحدات
    modules_ok = test_all_modules()
    
    # النتيجة النهائية
    if db_ok and modules_ok:
        print("\\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
    else:
        print("\\n⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
'''
    
    try:
        with open('comprehensive_test.py', 'w', encoding='utf-8') as f:
            f.write(test_content)
        print("✅ تم إنشاء اختبار شامل: comprehensive_test.py")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء الاختبار: {e}")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🔧 إصلاح جميع أخطاء Terminal")
    print("=" * 70)
    
    # تشغيل جميع الإصلاحات
    fixes = [
        ("إصلاح حقول قاعدة البيانات", fix_all_database_field_mismatches),
        ("إصلاح أسماء الحقول", fix_dialog_field_names),
        ("إضافة معالجة الأخطاء", add_error_handling),
        ("إنشاء اختبار شامل", create_comprehensive_test),
    ]
    
    success_count = 0
    for name, fix_func in fixes:
        print(f"\\n🔧 {name}...")
        if fix_func():
            success_count += 1
    
    print(f"\\n📊 النتيجة النهائية: {success_count}/{len(fixes)} إصلاحات نجحت")
    
    if success_count == len(fixes):
        print("\\n🎉 تم إصلاح جميع المشاكل بنجاح!")
        print("يمكنك الآن تشغيل النظام بدون أخطاء.")
    else:
        print("\\n⚠️ بعض الإصلاحات فشلت. راجع الأخطاء أعلاه.")
    
    input("\\nاضغط Enter للخروج...")
