#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النوافذ المصلحة
Test Fixed Dialogs
"""

import tkinter as tk
from dialogs_fixed import UserDialog, ProjectDialog

class FixedDialogTester:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("اختبار النوافذ المصلحة")
        self.root.geometry("500x400")
        self.root.configure(bg='#ecf0f1')
        
        self.create_widgets()
        
    def create_widgets(self):
        """إنشاء واجهة الاختبار"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="اختبار النوافذ المصلحة",
            font=('Arial', 20, 'bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        title_label.pack(pady=30)
        
        # وصف
        desc_label = tk.Label(
            self.root,
            text="هذه النوافذ مضمونة لإظهار الأزرار بوضوح",
            font=('Arial', 14),
            bg='#ecf0f1',
            fg='#7f8c8d'
        )
        desc_label.pack(pady=10)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.root, bg='#ecf0f1')
        buttons_frame.pack(expand=True)
        
        # زر اختبار المستخدمين
        user_btn = tk.Button(
            buttons_frame,
            text="اختبار نافذة المستخدمين",
            font=('Arial', 16, 'bold'),
            bg='#3498db',
            fg='white',
            width=25,
            height=3,
            command=self.test_user_dialog
        )
        user_btn.pack(pady=20)
        
        # زر اختبار المشاريع
        project_btn = tk.Button(
            buttons_frame,
            text="اختبار نافذة المشاريع",
            font=('Arial', 16, 'bold'),
            bg='#e67e22',
            fg='white',
            width=25,
            height=3,
            command=self.test_project_dialog
        )
        project_btn.pack(pady=20)
        
        # تعليمات
        instructions = tk.Label(
            self.root,
            text="اضغط على أي زر لفتح النافذة المقابلة\nيجب أن تظهر أزرار الحفظ والإلغاء بوضوح في الأسفل",
            font=('Arial', 12),
            bg='#ecf0f1',
            fg='#2c3e50',
            justify='center'
        )
        instructions.pack(pady=20)
        
    def test_user_dialog(self):
        """اختبار نافذة المستخدمين"""
        print("🧪 اختبار نافذة المستخدمين المصلحة...")
        dialog = UserDialog(self.root, "إضافة مستخدم جديد")
        self.root.wait_window(dialog.dialog)
        if dialog.result:
            print(f"✅ تم حفظ المستخدم: {dialog.result}")
        else:
            print("❌ تم إلغاء العملية")
        
    def test_project_dialog(self):
        """اختبار نافذة المشاريع"""
        print("🧪 اختبار نافذة المشاريع المصلحة...")
        dialog = ProjectDialog(self.root, "إضافة مشروع جديد")
        self.root.wait_window(dialog.dialog)
        if dialog.result:
            print(f"✅ تم حفظ المشروع: {dialog.result}")
        else:
            print("❌ تم إلغاء العملية")
        
    def run(self):
        """تشغيل الاختبار"""
        print("🧪 بدء اختبار النوافذ المصلحة...")
        print("هذه النوافذ مضمونة لإظهار الأزرار!")
        self.root.mainloop()

if __name__ == "__main__":
    tester = FixedDialogTester()
    tester.run()
