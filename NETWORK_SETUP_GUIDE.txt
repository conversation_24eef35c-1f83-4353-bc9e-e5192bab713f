
======================================================================
🌐 دليل إعداد النظام للعمل على الشبكة المحلية
   Network Setup Guide for Multi-User Access
======================================================================

📋 المتطلبات الأساسية:
1. شبكة محلية (LAN) تربط جميع الأجهزة
2. مجلد مشترك على الشبكة
3. صلاحيات قراءة وكتابة لجميع المستخدمين
4. Python مثبت على جميع الأجهزة

======================================================================
🔧 خطوات الإعداد:
======================================================================

الخطوة 1: إنشاء مجلد مشترك على الشبكة
------------------------------------------
1. على الجهاز الرئيسي (Server):
   - إنشاء مجلد: C:\SharedApps\RafeaSystem
   - مشاركة المجلد على الشبكة
   - إعطاء صلاحيات كاملة لجميع المستخدمين

2. على الأجهزة الأخرى (Clients):
   - الاتصال بالمجلد المشترك
   - ربط المجلد كـ Network Drive (مثل Z:\RafeaSystem)

الخطوة 2: نسخ ملفات النظام
---------------------------
نسخ جميع ملفات النظام إلى المجلد المشترك:
- rafea_complete_system.py
- dialogs.py
- database_sqlite.py
- utils/
- data/
- logs/

الخطوة 3: إعداد قاعدة البيانات المشتركة
---------------------------------------
قاعدة البيانات SQLite ستكون في المجلد المشترك:
- المسار: \SharedFolder\RafeaSystem\data\rafea_system.db
- جميع المستخدمين سيصلون لنفس قاعدة البيانات

======================================================================
👥 إدارة المستخدمين:
======================================================================

المستخدم الرئيسي (Admin):
- اسم المستخدم: admin
- كلمة المرور: admin123
- صلاحيات كاملة لجميع الوحدات

المستخدمين الآخرين:
- يمكن إنشاؤهم من خلال وحدة "إدارة المستخدمين"
- صلاحيات محددة حسب نوع المستخدم

======================================================================
🔒 الأمان والصلاحيات:
======================================================================

مستويات المستخدمين:
1. admin - صلاحيات كاملة
2. manager - إدارة المشاريع والعملاء
3. employee - إدخال البيانات فقط
4. viewer - عرض البيانات فقط

======================================================================
📊 مراقبة النشاط:
======================================================================

سجلات النظام:
- جميع العمليات تُسجل في ملفات السجلات
- المسار: \SharedFolder\RafeaSystem\logs\
- يمكن للمدير مراقبة جميع الأنشطة

======================================================================
🚀 تشغيل النظام:
======================================================================

على كل جهاز:
1. فتح Command Prompt أو PowerShell
2. الانتقال للمجلد المشترك:
   cd Z:\RafeaSystem
3. تشغيل النظام:
   python rafea_complete_system.py

======================================================================
⚠️ نصائح مهمة:
======================================================================

1. النسخ الاحتياطي:
   - عمل نسخة احتياطية يومية من قاعدة البيانات
   - حفظ النسخ في مكان آمن

2. الأداء:
   - تأكد من سرعة الشبكة المحلية
   - استخدم كابلات Ethernet للأداء الأفضل

3. الاستقرار:
   - تأكد من استقرار الشبكة
   - تجنب قطع الاتصال أثناء العمل

4. الصيانة:
   - تنظيف ملفات السجلات القديمة
   - مراقبة حجم قاعدة البيانات

======================================================================
🔧 استكشاف الأخطاء:
======================================================================

مشاكل شائعة وحلولها:

1. "لا يمكن الوصول لقاعدة البيانات":
   - تحقق من صلاحيات المجلد المشترك
   - تأكد من وجود ملف قاعدة البيانات

2. "بطء في الاستجابة":
   - تحقق من سرعة الشبكة
   - قلل عدد المستخدمين المتزامنين

3. "تضارب في البيانات":
   - SQLite يدعم المعاملات المتزامنة
   - تجنب التعديل المتزامن لنفس السجل

======================================================================
📞 الدعم الفني:
======================================================================

في حالة وجود مشاكل:
1. راجع ملفات السجلات في مجلد logs/
2. تحقق من اتصال الشبكة
3. تأكد من صلاحيات الملفات

======================================================================
