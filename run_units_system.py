#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام الشامل مع وظائف إدارة الوحدات المطورة
Run Complete System with Enhanced Units Management
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """الدالة الرئيسية"""
    print("=" * 80)
    print("🏢 النظام الشامل لإدارة شركة رافع للتطوير العقاري")
    print("   مع وظائف إدارة الوحدات المطورة")
    print("=" * 80)
    
    print("\n🎯 الميزات الجديدة المطورة:")
    print("=" * 50)
    
    print("\n🏠 إدارة الوحدات المتقدمة:")
    print("   ✅ إضافة وحدات جديدة مع جميع التفاصيل")
    print("   ✅ تعديل بيانات الوحدات الموجودة")
    print("   ✅ حجز الوحدات للعملاء")
    print("   ✅ بيع الوحدات مع تحديث الحالة")
    print("   ✅ حذف الوحدات مع تأكيد الأمان")
    print("   ✅ عرض تفاصيل شاملة لكل وحدة")
    print("   ✅ تحديث فوري للبيانات والإحصائيات")
    
    print("\n🏗️ إدارة المشاريع المطورة:")
    print("   ✅ إضافة مشاريع جديدة مع نوافذ حوار احترافية")
    print("   ✅ تعديل بيانات المشاريع الموجودة")
    print("   ✅ حذف المشاريع مع حماية البيانات")
    print("   ✅ عرض تفاصيل كاملة للمشاريع")
    print("   ✅ تتبع نسب الإنجاز والتكاليف")
    
    print("\n👥 إدارة العملاء المحسنة:")
    print("   ✅ إضافة عملاء جدد (أفراد وشركات)")
    print("   ✅ تعديل بيانات العملاء")
    print("   ✅ حذف العملاء مع التأكيد")
    print("   ✅ تصنيف العملاء حسب النوع")
    
    print("\n🎨 واجهة المستخدم المحسنة:")
    print("   ✅ نوافذ حوار احترافية لإدخال البيانات")
    print("   ✅ أزرار تفاعلية ملونة")
    print("   ✅ رسائل تأكيد وتحذير واضحة")
    print("   ✅ تحديث فوري للجداول والإحصائيات")
    print("   ✅ واجهة عربية كاملة مع دعم RTL")
    
    print("\n📊 لوحة التحكم التفاعلية:")
    print("   ✅ إحصائيات مباشرة ومحدثة")
    print("   ✅ بطاقات ملونة للمؤشرات الرئيسية")
    print("   ✅ قسم الأنشطة الحديثة")
    print("   ✅ قسم التنبيهات والإشعارات")
    
    print("\n🔧 الوحدات المتاحة:")
    print("   🏠 لوحة التحكم - إحصائيات شاملة")
    print("   🏗️ إدارة المشاريع - مطورة بالكامل")
    print("   🏠 مبيعات الشقق - 4 تبويبات فرعية")
    print("   👷 المقاولون والمستخلصات - جاهز للتطوير")
    print("   📦 الموردون والفواتير - جاهز للتطوير")
    print("   🛒 المشتريات - جاهز للتطوير")
    print("   🔧 الصيانة والتشغيل - جاهز للتطوير")
    print("   📋 المهام اليومية - جاهز للتطوير")
    print("   📊 التقارير - نظام متقدم")
    
    print("\n📋 بيانات الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    
    print("\n⚠️  ملاحظات مهمة:")
    print("   • جميع العمليات محمية بتأكيدات أمان")
    print("   • البيانات محفوظة في قاعدة بيانات SQLite محلية")
    print("   • يمكن التراجع عن معظم العمليات")
    print("   • النظام يدعم تعدد المستخدمين")
    
    print("\n" + "=" * 80)
    print("🚀 جاري تشغيل النظام...")
    print("=" * 80)
    
    try:
        # تشغيل النظام الشامل
        from rafea_complete_system import main as system_main
        return system_main()
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف النظام بواسطة المستخدم")
        return 0
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("\nللمساعدة:")
        print("1. تأكد من تثبيت جميع المتطلبات")
        print("2. تحقق من ملفات السجلات في مجلد logs")
        print("3. تواصل مع الدعم الفني")
        input("\nاضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
