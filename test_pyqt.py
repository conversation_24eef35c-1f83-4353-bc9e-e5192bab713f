#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار PyQt5
PyQt5 Test
"""

import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBox<PERSON>ayout, QWidget, <PERSON><PERSON>ushButton
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار PyQt5 - نظام رافع")
        self.setGeometry(100, 100, 600, 400)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط
        layout = QVBoxLayout(central_widget)
        
        # تسمية ترحيبية
        welcome_label = QLabel("مرحباً بك في نظام إدارة شركة رافع للتطوير العقاري")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setFont(QFont("Arial", 16, QFont.Bold))
        welcome_label.setStyleSheet("color: #2c3e50; margin: 20px;")
        layout.addWidget(welcome_label)
        
        # معلومات النظام
        info_label = QLabel("✅ PyQt5 يعمل بشكل صحيح\n✅ دعم اللغة العربية متوفر\n✅ الواجهة جاهزة للاستخدام")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setFont(QFont("Arial", 12))
        info_label.setStyleSheet("color: #27ae60; margin: 20px; line-height: 1.5;")
        layout.addWidget(info_label)
        
        # زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.setFont(QFont("Arial", 12, QFont.Bold))
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                margin: 20px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        close_button.clicked.connect(self.close)
        layout.addWidget(close_button)

def main():
    print("🔍 اختبار PyQt5...")
    
    try:
        app = QApplication(sys.argv)
        
        # إعداد الخط العربي
        font = QFont("Arial", 10)
        app.setFont(font)
        
        # إعداد دعم RTL
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء النافذة
        window = TestWindow()
        window.show()
        
        print("✅ تم تشغيل نافذة الاختبار بنجاح")
        print("يمكنك الآن رؤية النافذة على الشاشة")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار PyQt5: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
