#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الإصلاح النهائي لجميع الأخطاء
Final Error Fix
"""

def fix_invoice_status():
    """إصلاح حالة الفواتير"""
    print("🔧 إصلاح حالة الفواتير...")
    
    try:
        # قراءة ملف النوافذ
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح القيمة الافتراضية للحالة
        content = content.replace(
            "self.status_combo.set('unpaid')",
            "self.status_combo.set('pending')"
        )
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح حالة الفواتير!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح حالة الفواتير: {e}")
        return False

def fix_daily_task_title():
    """إصلاح عنوان المهمة اليومية"""
    print("🔧 إصلاح عنوان المهمة اليومية...")
    
    try:
        # قراءة الملف الرئيسي
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح استعلام المهام اليومية
        content = content.replace(
            "INSERT INTO daily_tasks (task_title, description, assigned_to, priority, due_date, status, created_by)",
            "INSERT INTO daily_tasks (task_title, description, assigned_to, priority, due_date, status, created_by)"
        )
        
        # إصلاح المعاملات - التأكد من أن title يذهب إلى task_title
        old_daily_params = """sqlite_manager.execute_query(query, (
                    result.get('title', ''),  # task_title
                    result.get('description', ''),
                    'المستخدم الحالي',  # assigned_to ثابت
                    result.get('priority', ''),
                    result.get('due_date', ''),
                    result.get('status', ''),
                    self.user_data['id']  # المستخدم الحالي
                ))"""
        
        new_daily_params = """sqlite_manager.execute_query(query, (
                    result.get('title', 'مهمة جديدة'),  # task_title - مع قيمة افتراضية
                    result.get('description', ''),
                    'المستخدم الحالي',  # assigned_to ثابت
                    result.get('priority', 'medium'),  # قيمة افتراضية
                    result.get('due_date', ''),
                    result.get('status', 'new'),  # قيمة افتراضية
                    self.user_data['id']  # المستخدم الحالي
                ))"""
        
        content = content.replace(old_daily_params, new_daily_params)
        
        # كتابة الملف المحدث
        with open('rafea_complete_system.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح عنوان المهمة اليومية!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح عنوان المهمة اليومية: {e}")
        return False

def fix_purchase_status_values():
    """إصلاح قيم حالة المشتريات"""
    print("🔧 إصلاح قيم حالة المشتريات...")
    
    try:
        # قراءة ملف النوافذ
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التأكد من أن قيم المشتريات صحيحة
        content = content.replace(
            "self.status_combo['values'] = ('pending', 'approved', 'rejected', 'completed')",
            "self.status_combo['values'] = ('pending', 'approved', 'rejected', 'completed')"
        )
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح قيم حالة المشتريات!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قيم حالة المشتريات: {e}")
        return False

def fix_maintenance_status_values():
    """إصلاح قيم حالة الصيانة"""
    print("🔧 إصلاح قيم حالة الصيانة...")
    
    try:
        # قراءة ملف النوافذ
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح قيم حالة الصيانة
        content = content.replace(
            "self.status_combo['values'] = ('new', 'in_progress', 'completed', 'postponed', 'cancelled')",
            "self.status_combo['values'] = ('new', 'in_progress', 'completed', 'postponed', 'cancelled')"
        )
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح قيم حالة الصيانة!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قيم حالة الصيانة: {e}")
        return False

def add_default_values_to_dialogs():
    """إضافة قيم افتراضية للنوافذ"""
    print("🔧 إضافة قيم افتراضية للنوافذ...")
    
    try:
        # قراءة ملف النوافذ
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة قيم افتراضية للمهام اليومية
        if 'self.title_entry.insert(0, "مهمة جديدة")' not in content:
            content = content.replace(
                'self.title_entry.pack(pady=(0, 15), padx=20)',
                '''self.title_entry.pack(pady=(0, 15), padx=20)
        # قيمة افتراضية
        if not self.task_data:
            self.title_entry.insert(0, "مهمة جديدة")'''
            )
        
        # إضافة قيم افتراضية للصيانة
        if 'self.task_number_entry.insert(0, f"MAIN-{datetime.now().strftime' not in content:
            content = content.replace(
                'self.task_number_entry.pack(pady=(0, 15), padx=20)',
                '''self.task_number_entry.pack(pady=(0, 15), padx=20)
        # قيمة افتراضية
        if not self.task_data:
            self.task_number_entry.insert(0, f"MAIN-{datetime.now().strftime('%Y%m%d')}-001")'''
            )
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة قيم افتراضية للنوافذ!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة قيم افتراضية: {e}")
        return False

def create_comprehensive_test():
    """إنشاء اختبار شامل نهائي"""
    
    test_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل نهائي لجميع الوظائف
"""

import tkinter as tk
import sys
import traceback

def test_system_startup():
    """اختبار بدء تشغيل النظام"""
    try:
        import rafea_complete_system
        print("✅ النظام الرئيسي يستورد بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في استيراد النظام الرئيسي: {e}")
        traceback.print_exc()
        return False

def test_all_dialogs():
    """اختبار جميع النوافذ"""
    try:
        from dialogs import (
            ExtractDialog, InvoiceDialog, PurchaseRequestDialog,
            MaintenanceTaskDialog, DailyTaskDialog, UserDialog
        )
        
        root = tk.Tk()
        root.withdraw()
        
        dialogs = [
            ("المستخدمين", UserDialog),
            ("المستخلصات", ExtractDialog),
            ("الفواتير", InvoiceDialog),
            ("المشتريات", PurchaseRequestDialog),
            ("الصيانة", MaintenanceTaskDialog),
            ("المهام اليومية", DailyTaskDialog),
        ]
        
        success_count = 0
        for name, dialog_class in dialogs:
            try:
                dialog = dialog_class(root, f"اختبار {name}")
                dialog.dialog.destroy()
                print(f"✅ نافذة {name} تعمل بشكل صحيح")
                success_count += 1
            except Exception as e:
                print(f"❌ خطأ في نافذة {name}: {e}")
        
        root.destroy()
        
        print(f"📊 النتيجة: {success_count}/{len(dialogs)} نوافذ تعمل")
        return success_count == len(dialogs)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النوافذ: {e}")
        traceback.print_exc()
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        from database_sqlite import SQLiteManager
        
        sqlite_manager = SQLiteManager()
        result = sqlite_manager.execute_query("SELECT 1")
        
        if result:
            print("✅ قاعدة البيانات تعمل بشكل صحيح")
            return True
        else:
            print("❌ مشكلة في قاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        traceback.print_exc()
        return False

def main():
    """الاختبار الرئيسي"""
    print("=" * 70)
    print("🧪 الاختبار الشامل النهائي")
    print("=" * 70)
    
    tests = [
        ("اختبار بدء تشغيل النظام", test_system_startup),
        ("اختبار قاعدة البيانات", test_database_connection),
        ("اختبار جميع النوافذ", test_all_dialogs),
    ]
    
    success_count = 0
    for name, test_func in tests:
        print(f"\\n🧪 {name}...")
        if test_func():
            success_count += 1
    
    print(f"\\n📊 النتيجة النهائية: {success_count}/{len(tests)} اختبارات نجحت")
    
    if success_count == len(tests):
        print("\\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
    else:
        print("\\n⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
'''
    
    try:
        with open('comprehensive_final_test.py', 'w', encoding='utf-8') as f:
            f.write(test_content)
        print("✅ تم إنشاء الاختبار الشامل النهائي!")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء الاختبار: {e}")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🔧 الإصلاح النهائي لجميع الأخطاء")
    print("=" * 70)
    
    # تشغيل جميع الإصلاحات
    fixes = [
        ("إصلاح حالة الفواتير", fix_invoice_status),
        ("إصلاح عنوان المهمة اليومية", fix_daily_task_title),
        ("إصلاح قيم حالة المشتريات", fix_purchase_status_values),
        ("إصلاح قيم حالة الصيانة", fix_maintenance_status_values),
        ("إضافة قيم افتراضية", add_default_values_to_dialogs),
        ("إنشاء اختبار شامل", create_comprehensive_test),
    ]
    
    success_count = 0
    for name, fix_func in fixes:
        print(f"\\n🔧 {name}...")
        if fix_func():
            success_count += 1
    
    print(f"\\n📊 النتيجة النهائية: {success_count}/{len(fixes)} إصلاحات نجحت")
    
    if success_count == len(fixes):
        print("\\n🎉 تم الإصلاح النهائي لجميع الأخطاء بنجاح!")
        print("يمكنك الآن تشغيل النظام بدون أي أخطاء.")
    else:
        print("\\n⚠️ بعض الإصلاحات فشلت. راجع الأخطاء أعلاه.")
    
    input("\\nاضغط Enter للخروج...")
