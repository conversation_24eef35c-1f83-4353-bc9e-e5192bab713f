# -*- coding: utf-8 -*-
"""
التطبيق الرئيسي لنظام إدارة شركة رافع للتطوير العقاري
Main Application for Rafea Real Estate Development Management System
"""

import sys
import os
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QSplashScreen, QMessageBox
from PyQt5.QtCore import Qt, QTimer, QTranslator, QLocale
from PyQt5.QtGui import QPixmap, QFont, QIcon
from loguru import logger

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import APP_CONFIG, UI_CONFIG, LOGGING_CONFIG
from database import db_manager, init_database
from ui.login_window import LoginWindow
from ui.main_window import MainWindow
from utils.logger_setup import setup_logger

class RafeaApplication:
    """التطبيق الرئيسي"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.login_window = None
        self.current_user = None
        
    def setup_application(self):
        """إعداد التطبيق"""
        try:
            # إنشاء تطبيق Qt
            self.app = QApplication(sys.argv)
            
            # إعداد خصائص التطبيق
            self.app.setApplicationName(APP_CONFIG['name'])
            self.app.setApplicationVersion(APP_CONFIG['version'])
            self.app.setOrganizationName(APP_CONFIG['company'])
            
            # إعداد الأيقونة
            if os.path.exists(UI_CONFIG['window_icon']):
                self.app.setWindowIcon(QIcon(UI_CONFIG['window_icon']))
            
            # إعداد الخط العربي
            self.setup_arabic_font()
            
            # إعداد دعم RTL
            if UI_CONFIG['rtl_support']:
                self.app.setLayoutDirection(Qt.RightToLeft)
            
            # إعداد الستايل
            self.setup_stylesheet()
            
            logger.info("تم إعداد التطبيق بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إعداد التطبيق: {e}")
            return False
    
    def setup_arabic_font(self):
        """إعداد الخط العربي"""
        try:
            # تحديد خطوط عربية مناسبة
            arabic_fonts = [
                "Arial Unicode MS",
                "Tahoma",
                "Segoe UI",
                "DejaVu Sans",
                "Liberation Sans"
            ]
            
            font_set = False
            for font_name in arabic_fonts:
                font = QFont(font_name, 10)
                if font.exactMatch():
                    self.app.setFont(font)
                    font_set = True
                    logger.info(f"تم تعيين الخط: {font_name}")
                    break
            
            if not font_set:
                logger.warning("لم يتم العثور على خط عربي مناسب")
                
        except Exception as e:
            logger.error(f"خطأ في إعداد الخط العربي: {e}")
    
    def setup_stylesheet(self):
        """إعداد ملف الستايل"""
        try:
            if os.path.exists(UI_CONFIG['style_sheet']):
                with open(UI_CONFIG['style_sheet'], 'r', encoding='utf-8') as file:
                    stylesheet = file.read()
                    self.app.setStyleSheet(stylesheet)
                    logger.info("تم تحميل ملف الستايل")
            else:
                # ستايل افتراضي
                default_style = """
                QMainWindow {
                    background-color: #f5f5f5;
                }
                
                QMenuBar {
                    background-color: #2c3e50;
                    color: white;
                    font-weight: bold;
                }
                
                QMenuBar::item {
                    background-color: transparent;
                    padding: 8px 12px;
                }
                
                QMenuBar::item:selected {
                    background-color: #34495e;
                }
                
                QToolBar {
                    background-color: #ecf0f1;
                    border: 1px solid #bdc3c7;
                    spacing: 3px;
                }
                
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                
                QPushButton:hover {
                    background-color: #2980b9;
                }
                
                QPushButton:pressed {
                    background-color: #21618c;
                }
                
                QLineEdit, QTextEdit, QComboBox {
                    border: 2px solid #bdc3c7;
                    border-radius: 4px;
                    padding: 5px;
                    font-size: 12px;
                }
                
                QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                    border-color: #3498db;
                }
                
                QTableWidget {
                    gridline-color: #bdc3c7;
                    background-color: white;
                    alternate-background-color: #f8f9fa;
                }
                
                QTableWidget::item {
                    padding: 8px;
                }
                
                QHeaderView::section {
                    background-color: #34495e;
                    color: white;
                    padding: 8px;
                    border: none;
                    font-weight: bold;
                }
                """
                self.app.setStyleSheet(default_style)
                logger.info("تم تطبيق الستايل الافتراضي")
                
        except Exception as e:
            logger.error(f"خطأ في إعداد ملف الستايل: {e}")
    
    def show_splash_screen(self):
        """عرض شاشة البداية"""
        try:
            # إنشاء صورة شاشة البداية
            splash_pixmap = QPixmap(400, 300)
            splash_pixmap.fill(Qt.white)
            
            splash = QSplashScreen(splash_pixmap)
            splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.SplashScreen)
            splash.show()
            
            # عرض رسائل التحميل
            messages = [
                "جاري تحميل التطبيق...",
                "جاري الاتصال بقاعدة البيانات...",
                "جاري تهيئة الواجهات...",
                "مرحباً بك في نظام رافع"
            ]
            
            for i, message in enumerate(messages):
                splash.showMessage(
                    message,
                    Qt.AlignCenter | Qt.AlignBottom,
                    Qt.black
                )
                self.app.processEvents()
                QTimer.singleShot(1000, lambda: None)
                
                # محاكاة عملية التحميل
                for _ in range(10):
                    self.app.processEvents()
                    QTimer.singleShot(100, lambda: None)
            
            splash.close()
            
        except Exception as e:
            logger.error(f"خطأ في عرض شاشة البداية: {e}")
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            if not db_manager.test_connection():
                QMessageBox.critical(
                    None,
                    "خطأ في قاعدة البيانات",
                    "لا يمكن الاتصال بقاعدة البيانات.\nيرجى التحقق من إعدادات الاتصال."
                )
                return False
            
            if not init_database():
                QMessageBox.critical(
                    None,
                    "خطأ في قاعدة البيانات",
                    "فشل في تهيئة قاعدة البيانات."
                )
                return False
            
            logger.info("تم تهيئة قاعدة البيانات بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
            QMessageBox.critical(
                None,
                "خطأ في قاعدة البيانات",
                f"خطأ في تهيئة قاعدة البيانات:\n{str(e)}"
            )
            return False
    
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        try:
            self.login_window = LoginWindow()
            self.login_window.login_successful.connect(self.on_login_successful)
            self.login_window.show()
            
        except Exception as e:
            logger.error(f"خطأ في عرض نافذة تسجيل الدخول: {e}")
    
    def on_login_successful(self, user_data):
        """معالج نجاح تسجيل الدخول"""
        try:
            self.current_user = user_data
            self.login_window.close()
            self.show_main_window()
            
        except Exception as e:
            logger.error(f"خطأ في معالجة تسجيل الدخول: {e}")
    
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        try:
            self.main_window = MainWindow(self.current_user)
            self.main_window.show()
            
        except Exception as e:
            logger.error(f"خطأ في عرض النافذة الرئيسية: {e}")
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            # إعداد نظام السجلات
            setup_logger()
            
            logger.info("بدء تشغيل التطبيق")
            
            # إعداد التطبيق
            if not self.setup_application():
                return 1
            
            # عرض شاشة البداية
            self.show_splash_screen()
            
            # تهيئة قاعدة البيانات
            if not self.initialize_database():
                return 1
            
            # عرض نافذة تسجيل الدخول
            self.show_login()
            
            # تشغيل حلقة الأحداث
            return self.app.exec_()
            
        except Exception as e:
            logger.error(f"خطأ في تشغيل التطبيق: {e}")
            if self.app:
                QMessageBox.critical(
                    None,
                    "خطأ في التطبيق",
                    f"حدث خطأ غير متوقع:\n{str(e)}"
                )
            return 1
        finally:
            # تنظيف الموارد
            if db_manager:
                db_manager.close()
            logger.info("تم إغلاق التطبيق")

def main():
    """الدالة الرئيسية"""
    app = RafeaApplication()
    return app.run()

if __name__ == "__main__":
    sys.exit(main())
