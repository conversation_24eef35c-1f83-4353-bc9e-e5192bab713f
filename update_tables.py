#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث الجداول الموجودة لإضافة الأعمدة المفقودة
Update Existing Tables to Add Missing Columns
"""

import sqlite3
import os
from pathlib import Path

def update_tables():
    """تحديث الجداول لإضافة الأعمدة المفقودة"""
    
    # مسار قاعدة البيانات
    db_path = Path(__file__).parent / "data" / "rafea_real_estate.db"
    
    print(f"🔧 تحديث الجداول في: {db_path}")
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # قائمة التحديثات المطلوبة
        updates = [
            # تحديث جدول المقاولين
            ("contractors", "status", "TEXT DEFAULT 'active'"),
            ("contractors", "notes", "TEXT"),
            ("contractors", "specialty", "TEXT"),
            ("contractors", "address", "TEXT"),
            ("contractors", "created_at", "DATETIME DEFAULT CURRENT_TIMESTAMP"),
            ("contractors", "updated_at", "DATETIME"),
            
            # تحديث جدول المستخلصات
            ("extracts", "contractor", "TEXT"),
            ("extracts", "project", "TEXT"),
            ("extracts", "amount", "DECIMAL(15,2)"),
            ("extracts", "date", "DATE"),
            ("extracts", "status", "TEXT DEFAULT 'pending'"),
            ("extracts", "description", "TEXT"),
            ("extracts", "created_at", "DATETIME DEFAULT CURRENT_TIMESTAMP"),
            ("extracts", "updated_at", "DATETIME"),
            
            # تحديث جدول الموردين
            ("suppliers", "material_type", "TEXT"),
            ("suppliers", "status", "TEXT DEFAULT 'active'"),
            ("suppliers", "notes", "TEXT"),
            ("suppliers", "address", "TEXT"),
            ("suppliers", "created_at", "DATETIME DEFAULT CURRENT_TIMESTAMP"),
            ("suppliers", "updated_at", "DATETIME"),
            
            # تحديث جدول الفواتير
            ("invoices", "supplier", "TEXT"),
            ("invoices", "amount", "DECIMAL(15,2)"),
            ("invoices", "date", "DATE"),
            ("invoices", "due_date", "DATE"),
            ("invoices", "status", "TEXT DEFAULT 'pending'"),
            ("invoices", "description", "TEXT"),
            ("invoices", "created_at", "DATETIME DEFAULT CURRENT_TIMESTAMP"),
            ("invoices", "updated_at", "DATETIME"),
            
            # تحديث جدول طلبات الشراء
            ("purchase_requests", "project", "TEXT"),
            ("purchase_requests", "materials", "TEXT"),
            ("purchase_requests", "quantity", "TEXT"),
            ("purchase_requests", "estimated_cost", "DECIMAL(15,2)"),
            ("purchase_requests", "request_date", "DATE"),
            ("purchase_requests", "status", "TEXT DEFAULT 'pending'"),
            ("purchase_requests", "notes", "TEXT"),
            ("purchase_requests", "created_by", "INTEGER"),
            ("purchase_requests", "created_at", "DATETIME DEFAULT CURRENT_TIMESTAMP"),
            ("purchase_requests", "updated_at", "DATETIME"),
            
            # تحديث جدول مهام الصيانة
            ("maintenance_tasks", "task_number", "TEXT"),
            ("maintenance_tasks", "maintenance_type", "TEXT"),
            ("maintenance_tasks", "location", "TEXT"),
            ("maintenance_tasks", "technician", "TEXT"),
            ("maintenance_tasks", "date", "DATE"),
            ("maintenance_tasks", "cost", "DECIMAL(15,2)"),
            ("maintenance_tasks", "status", "TEXT DEFAULT 'pending'"),
            ("maintenance_tasks", "description", "TEXT"),
            ("maintenance_tasks", "created_at", "DATETIME DEFAULT CURRENT_TIMESTAMP"),
            ("maintenance_tasks", "updated_at", "DATETIME"),
            
            # تحديث جدول المهام اليومية
            ("daily_tasks", "title", "TEXT"),
            ("daily_tasks", "description", "TEXT"),
            ("daily_tasks", "assigned_to", "TEXT"),
            ("daily_tasks", "priority", "TEXT DEFAULT 'medium'"),
            ("daily_tasks", "due_date", "DATE"),
            ("daily_tasks", "status", "TEXT DEFAULT 'pending'"),
            ("daily_tasks", "created_by", "INTEGER"),
            ("daily_tasks", "created_at", "DATETIME DEFAULT CURRENT_TIMESTAMP"),
            ("daily_tasks", "updated_at", "DATETIME"),
        ]
        
        # تطبيق التحديثات
        for table_name, column_name, column_type in updates:
            try:
                # التحقق من وجود الجدول
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                if not cursor.fetchone():
                    print(f"⚠️  الجدول {table_name} غير موجود، سيتم تخطيه")
                    continue
                
                # التحقق من وجود العمود
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [row[1] for row in cursor.fetchall()]
                
                if column_name not in columns:
                    print(f"➕ إضافة العمود {column_name} إلى الجدول {table_name}")
                    cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}")
                else:
                    print(f"✅ العمود {column_name} موجود بالفعل في الجدول {table_name}")
                    
            except Exception as e:
                print(f"❌ خطأ في تحديث {table_name}.{column_name}: {e}")
                continue
        
        # حفظ التغييرات
        conn.commit()
        
        print("\n✅ تم تحديث جميع الجداول بنجاح!")
        
        # عرض هيكل الجداول المحدثة
        tables_to_check = ['contractors', 'extracts', 'suppliers', 'invoices', 'purchase_requests', 'maintenance_tasks', 'daily_tasks']
        
        for table in tables_to_check:
            try:
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                print(f"\n📋 هيكل الجدول {table}:")
                for col in columns:
                    print(f"   • {col[1]} ({col[2]})")
            except:
                print(f"⚠️  لا يمكن عرض هيكل الجدول {table}")
        
        # إضافة بيانات تجريبية
        print("\n🔧 إضافة بيانات تجريبية...")
        
        # مقاولين تجريبيين
        try:
            cursor.execute("SELECT COUNT(*) FROM contractors")
            if cursor.fetchone()[0] == 0:
                contractors_data = [
                    ("أحمد محمد للمقاولات", "مقاولات عامة", "0501234567", "<EMAIL>", "الرياض", "active", "مقاول معتمد"),
                    ("شركة البناء المتقدم", "أعمال خرسانية", "0507654321", "<EMAIL>", "جدة", "active", "خبرة 15 سنة"),
                    ("مؤسسة الإنشاءات الحديثة", "تشطيبات", "0509876543", "<EMAIL>", "الدمام", "active", "متخصص في التشطيبات")
                ]
                
                cursor.executemany("""
                    INSERT INTO contractors (name, specialty, phone, email, address, status, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, contractors_data)
                print("✅ تم إضافة المقاولين التجريبيين")
        except Exception as e:
            print(f"⚠️  خطأ في إضافة المقاولين: {e}")
        
        # موردين تجريبيين
        try:
            cursor.execute("SELECT COUNT(*) FROM suppliers")
            if cursor.fetchone()[0] == 0:
                suppliers_data = [
                    ("شركة الحديد والصلب", "حديد ومعادن", "0501111111", "<EMAIL>", "الرياض", "active", "مورد رئيسي للحديد"),
                    ("مؤسسة مواد البناء", "مواد بناء", "0502222222", "<EMAIL>", "جدة", "active", "جميع مواد البناء"),
                    ("شركة الكهربائيات المتقدمة", "كهربائيات", "0503333333", "<EMAIL>", "الدمام", "active", "معدات كهربائية")
                ]
                
                cursor.executemany("""
                    INSERT INTO suppliers (name, material_type, phone, email, address, status, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, suppliers_data)
                print("✅ تم إضافة الموردين التجريبيين")
        except Exception as e:
            print(f"⚠️  خطأ في إضافة الموردين: {e}")
        
        # حفظ البيانات التجريبية
        conn.commit()
        
        print("✅ تم تحديث قاعدة البيانات بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث الجداول: {e}")
        return False
        
    finally:
        if 'conn' in locals():
            conn.close()
    
    return True

if __name__ == "__main__":
    print("=" * 70)
    print("🏢 تحديث الجداول - نظام إدارة شركة رافع للتطوير العقاري")
    print("=" * 70)
    
    success = update_tables()
    
    if success:
        print("\n🎉 تم تحديث جميع الجداول بنجاح!")
        print("يمكنك الآن تشغيل النظام الرئيسي.")
    else:
        print("\n❌ فشل في تحديث الجداول!")
    
    input("\nاضغط Enter للخروج...")
