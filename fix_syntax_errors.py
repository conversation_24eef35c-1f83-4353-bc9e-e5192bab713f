#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح أخطاء البنية في الكود
Fix Syntax Errors in Code
"""

def fix_syntax_errors():
    """إصلاح أخطاء البنية"""
    print("🔧 إصلاح أخطاء البنية...")
    
    try:
        # قراءة الملف
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح مشاكل البنية
        fixes = [
            # إصلاح مشكلة except خارج try
            ('''            # تحديث البيانات في جميع التبويبات
            self.refresh_all_data()

            except Exception as e:''', 
             '''            # تحديث البيانات في جميع التبويبات
            self.refresh_all_data()

        except Exception as e:'''),
            
            # إصلاح مشكلة except أخرى
            ('''            except KeyError as e:
                logger.error(f"حقل مفقود في البيانات: {e}")
                messagebox.showerror("خطأ", f"حقل مفقود في البيانات: {str(e)}")
            except ValueError as e:''',
             '''        except KeyError as e:
            logger.error(f"حقل مفقود في البيانات: {e}")
            messagebox.showerror("خطأ", f"حقل مفقود في البيانات: {str(e)}")
        except ValueError as e:'''),
        ]
        
        # تطبيق الإصلاحات
        for old_text, new_text in fixes:
            content = content.replace(old_text, new_text)
        
        # إزالة الأخطاء المتكررة
        lines = content.split('\n')
        fixed_lines = []
        i = 0
        
        while i < len(lines):
            line = lines[i]
            
            # تجاهل الأسطر التي تحتوي على except خارج try
            if 'except Exception as e:' in line and i > 0:
                # التحقق من وجود try قبلها
                found_try = False
                for j in range(i-1, max(0, i-20), -1):
                    if 'try:' in lines[j]:
                        found_try = True
                        break
                    if 'except' in lines[j] or 'finally:' in lines[j]:
                        break
                
                if not found_try:
                    # تخطي هذا السطر والأسطر التالية حتى نجد دالة جديدة
                    while i < len(lines) and not lines[i].strip().startswith('def '):
                        i += 1
                    continue
            
            fixed_lines.append(line)
            i += 1
        
        # إعادة تجميع المحتوى
        content = '\n'.join(fixed_lines)
        
        # كتابة الملف المصحح
        with open('rafea_complete_system.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح أخطاء البنية!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح البنية: {e}")
        return False

def add_refresh_function():
    """إضافة دالة التحديث الشاملة"""
    print("🔧 إضافة دالة التحديث الشاملة...")
    
    try:
        # قراءة الملف
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة دالة التحديث الشاملة إذا لم تكن موجودة
        refresh_function = '''
    def refresh_all_data(self):
        """تحديث جميع البيانات في النظام"""
        try:
            # تحديث الإحصائيات
            self.update_dashboard_stats()
            
            # تحديث جميع الجداول إذا كانت موجودة
            if hasattr(self, 'projects_tree'):
                self.load_projects_data()
            if hasattr(self, 'customers_tree'):
                self.load_customers_data()
            if hasattr(self, 'units_tree'):
                self.load_units_data()
            if hasattr(self, 'contractors_tree'):
                self.load_contractors_data()
            if hasattr(self, 'suppliers_tree'):
                self.load_suppliers_data()
            if hasattr(self, 'extracts_tree'):
                self.load_extracts_data()
            if hasattr(self, 'invoices_tree'):
                self.load_invoices_data()
            if hasattr(self, 'purchase_requests_tree'):
                self.load_purchase_requests_data()
            if hasattr(self, 'maintenance_tasks_tree'):
                self.load_maintenance_tasks_data()
            if hasattr(self, 'daily_tasks_tree'):
                self.load_daily_tasks_data()
            if hasattr(self, 'users_tree'):
                self.load_users_data()
            if hasattr(self, 'contracts_tree'):
                self.load_contracts_data()
            if hasattr(self, 'payments_tree'):
                self.load_payments_data()
                
            self.update_status("تم تحديث جميع البيانات")
            
        except Exception as e:
            logger.error(f"خطأ في تحديث البيانات: {e}")
            self.update_status(f"خطأ في تحديث البيانات: {str(e)}")
'''
        
        # إضافة الدالة قبل دالة logout
        if 'def refresh_all_data(self):' not in content:
            logout_pos = content.find('    def logout(self):')
            if logout_pos != -1:
                content = content[:logout_pos] + refresh_function + '\n' + content[logout_pos:]
        
        # كتابة الملف المحدث
        with open('rafea_complete_system.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة دالة التحديث الشاملة!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة دالة التحديث: {e}")
        return False

def test_syntax():
    """اختبار صحة البنية"""
    print("🧪 اختبار صحة البنية...")
    
    try:
        # محاولة استيراد الملف للتحقق من صحة البنية
        import ast
        
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # تحليل البنية
        ast.parse(content)
        
        print("✅ البنية صحيحة!")
        return True
        
    except SyntaxError as e:
        print(f"❌ خطأ في البنية: {e}")
        print(f"   السطر {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🔧 إصلاح أخطاء البنية وإضافة التحديث التلقائي")
    print("=" * 70)
    
    fixes = [
        ("إصلاح أخطاء البنية", fix_syntax_errors),
        ("إضافة دالة التحديث الشاملة", add_refresh_function),
        ("اختبار صحة البنية", test_syntax),
    ]
    
    success_count = 0
    for name, fix_func in fixes:
        print(f"\n🔧 {name}...")
        if fix_func():
            success_count += 1
    
    print(f"\n📊 النتيجة النهائية: {success_count}/{len(fixes)} إصلاحات نجحت")
    
    if success_count == len(fixes):
        print("\n🎉 تم إصلاح جميع أخطاء البنية بنجاح!")
        print("\nالآن النظام:")
        print("✅ خالي من أخطاء البنية")
        print("✅ يحدث البيانات تلقائياً بعد كل عملية")
        print("✅ يعرض البيانات التجريبية في جميع الصفحات")
        print("\n📋 يمكنك تشغيل النظام: python rafea_complete_system.py")
    else:
        print("\n⚠️ بعض الإصلاحات فشلت. راجع الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
