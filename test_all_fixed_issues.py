#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار جميع المشاكل المُصلحة
Test All Fixed Issues
"""

import tkinter as tk
from dialogs import (
    ExtractDialog, PurchaseRequestDialog, 
    MaintenanceTaskDialog, DailyTaskDialog
)

class FixedIssuesTester:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("اختبار المشاكل المُصلحة")
        self.root.geometry("800x600")
        self.root.configure(bg='#2c3e50')
        
        self.test_results = {}
        self.create_widgets()
        
    def create_widgets(self):
        """إنشاء واجهة الاختبار"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="🔧 اختبار المشاكل المُصلحة",
            font=('Arial', 24, 'bold'),
            bg='#2c3e50',
            fg='#ecf0f1'
        )
        title_label.pack(pady=30)
        
        # وصف
        desc_label = tk.Label(
            self.root,
            text="اختبار شامل للمشاكل التي تم إصلاحها",
            font=('Arial', 16),
            bg='#2c3e50',
            fg='#bdc3c7'
        )
        desc_label.pack(pady=10)
        
        # إطار النتائج
        self.results_frame = tk.Frame(self.root, bg='#34495e', relief='raised', bd=2)
        self.results_frame.pack(fill='both', expand=True, padx=40, pady=20)
        
        # عنوان النتائج
        results_title = tk.Label(
            self.results_frame,
            text="📊 نتائج الاختبار",
            font=('Arial', 18, 'bold'),
            bg='#34495e',
            fg='#ecf0f1'
        )
        results_title.pack(pady=20)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.root, bg='#2c3e50')
        buttons_frame.pack(fill='x', padx=40, pady=20)
        
        # قائمة الاختبارات
        test_buttons = [
            ("🔧 اختبار المستخلصات", self.test_extracts, '#e74c3c'),
            ("🛒 اختبار المشتريات", self.test_purchases, '#3498db'),
            ("⚙️ اختبار الصيانة", self.test_maintenance, '#f39c12'),
            ("📅 اختبار المهام اليومية", self.test_daily_tasks, '#27ae60'),
        ]
        
        # إنشاء أزرار الاختبار
        row = 0
        col = 0
        for name, command, color in test_buttons:
            btn = tk.Button(
                buttons_frame,
                text=name,
                font=('Arial', 14, 'bold'),
                bg=color,
                fg='white',
                width=25,
                height=2,
                relief='raised',
                bd=3,
                command=command
            )
            btn.grid(row=row, column=col, padx=15, pady=15, sticky='ew')
            
            col += 1
            if col > 1:  # عمودان
                col = 0
                row += 1
        
        # تكوين الأعمدة
        for i in range(2):
            buttons_frame.columnconfigure(i, weight=1)
        
        # زر اختبار شامل
        comprehensive_test_btn = tk.Button(
            self.root,
            text="🚀 اختبار شامل لجميع المشاكل المُصلحة",
            font=('Arial', 16, 'bold'),
            bg='#9b59b6',
            fg='white',
            width=40,
            height=2,
            relief='raised',
            bd=4,
            command=self.comprehensive_test
        )
        comprehensive_test_btn.pack(pady=20)
        
    def test_extracts(self):
        """اختبار نافذة المستخلصات"""
        print("🔧 اختبار نافذة المستخلصات...")
        try:
            dialog = ExtractDialog(self.root, "اختبار المستخلصات")
            
            # ملء البيانات تلقائياً
            dialog.extract_number_entry.insert(0, "EXT001")
            dialog.contractor_name_entry.insert(0, "مقاول تجريبي")
            dialog.amount_entry.insert(0, "50000")
            
            # محاكاة الحفظ
            dialog.save_extract()
            
            result = dialog.result
            self.show_test_result("المستخلصات", result, "حفظ البيانات")
            
        except Exception as e:
            self.show_test_result("المستخلصات", None, f"خطأ: {e}")
    
    def test_purchases(self):
        """اختبار نافذة المشتريات"""
        print("🛒 اختبار نافذة المشتريات...")
        try:
            dialog = PurchaseRequestDialog(self.root, "اختبار المشتريات")
            
            # ملء البيانات تلقائياً
            dialog.request_number_entry.insert(0, "PUR001")
            dialog.item_name_entry.insert(0, "مواد بناء")
            dialog.quantity_entry.insert(0, "100")
            dialog.unit_price_entry.insert(0, "50")
            
            # محاكاة الحفظ
            dialog.save_purchase()
            
            result = dialog.result
            self.show_test_result("المشتريات", result, "نافذة كاملة مع حفظ")
            
        except Exception as e:
            self.show_test_result("المشتريات", None, f"خطأ: {e}")
    
    def test_maintenance(self):
        """اختبار نافذة الصيانة"""
        print("⚙️ اختبار نافذة الصيانة...")
        try:
            dialog = MaintenanceTaskDialog(self.root, "اختبار الصيانة")
            
            # ملء البيانات تلقائياً
            dialog.task_number_entry.insert(0, "MAIN001")
            dialog.description_text.insert('1.0', "صيانة دورية للمعدات")
            dialog.assigned_to_entry.insert(0, "فريق الصيانة")
            
            # محاكاة الحفظ
            dialog.save_task()
            
            result = dialog.result
            self.show_test_result("الصيانة", result, "نافذة كاملة مع حفظ")
            
        except Exception as e:
            self.show_test_result("الصيانة", None, f"خطأ: {e}")
    
    def test_daily_tasks(self):
        """اختبار نافذة المهام اليومية"""
        print("📅 اختبار نافذة المهام اليومية...")
        try:
            dialog = DailyTaskDialog(self.root, "اختبار المهام اليومية")
            
            # ملء البيانات تلقائياً
            dialog.title_entry.insert(0, "مهمة يومية تجريبية")
            dialog.description_text.insert('1.0', "متابعة المشاريع الجارية")
            
            # محاكاة الحفظ
            dialog.save_task()
            
            result = dialog.result
            self.show_test_result("المهام اليومية", result, "نافذة كاملة مع حفظ")
            
        except Exception as e:
            self.show_test_result("المهام اليومية", None, f"خطأ: {e}")
    
    def show_test_result(self, test_name, result, details):
        """عرض نتيجة الاختبار"""
        self.test_results[test_name] = result
        
        # تحديد النتيجة
        if result:
            status = "✅ نجح"
            color = '#27ae60'
            result_details = f"{details} - تم حفظ {len(result)} حقل"
        else:
            status = "❌ فشل"
            color = '#e74c3c'
            result_details = details
        
        result_label = tk.Label(
            self.results_frame,
            text=f"{test_name}: {status} - {result_details}",
            font=('Arial', 12, 'bold'),
            bg='#34495e',
            fg=color
        )
        result_label.pack(pady=5)
        
        print(f"{status} اختبار {test_name}: {result_details}")
    
    def comprehensive_test(self):
        """اختبار شامل لجميع المشاكل المُصلحة"""
        print("🚀 بدء الاختبار الشامل...")
        
        # مسح النتائج السابقة
        for widget in self.results_frame.winfo_children():
            if isinstance(widget, tk.Label) and widget.cget('text') != "📊 نتائج الاختبار":
                widget.destroy()
        
        # اختبار جميع النوافذ
        test_functions = [
            self.test_extracts,
            self.test_purchases,
            self.test_maintenance,
            self.test_daily_tasks,
        ]
        
        for test_func in test_functions:
            try:
                test_func()
            except Exception as e:
                print(f"❌ خطأ في الاختبار: {e}")
        
        # عرض الملخص
        successful_tests = len([r for r in self.test_results.values() if r])
        total_tests = len(self.test_results)
        
        summary_label = tk.Label(
            self.results_frame,
            text=f"📊 الملخص النهائي: {successful_tests}/{total_tests} مشاكل تم حلها بنجاح",
            font=('Arial', 16, 'bold'),
            bg='#34495e',
            fg='#f39c12'
        )
        summary_label.pack(pady=20)
        
        if successful_tests == total_tests:
            final_status = tk.Label(
                self.results_frame,
                text="🎉 جميع المشاكل تم حلها بنجاح!",
                font=('Arial', 18, 'bold'),
                bg='#34495e',
                fg='#27ae60'
            )
            final_status.pack(pady=10)
        
        print(f"🎉 انتهى الاختبار الشامل: {successful_tests}/{total_tests} مشاكل تم حلها")
        
    def run(self):
        """تشغيل الاختبار"""
        print("🔧 بدء اختبار المشاكل المُصلحة...")
        self.root.mainloop()

if __name__ == "__main__":
    tester = FixedIssuesTester()
    tester.run()
