2025-06-30 17:19:01 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 17:19:01 | INFO | database:test_connection:159 | تم اختبار الاتصال بقاعدة البيانات بنجاح
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.units
[SQL: CREATE INDEX IF NOT EXISTS idx_units_project_id ON units(project_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.units
[SQL: CREATE INDEX IF NOT EXISTS idx_units_status ON units(status)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.contracts
[SQL: CREATE INDEX IF NOT EXISTS idx_contracts_customer_id ON contracts(customer_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.contracts
[SQL: CREATE INDEX IF NOT EXISTS idx_contracts_unit_id ON contracts(unit_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.payments
[SQL: CREATE INDEX IF NOT EXISTS idx_payments_contract_id ON payments(contract_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.extracts
[SQL: CREATE INDEX IF NOT EXISTS idx_extracts_contractor_id ON extracts(contractor_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.extracts
[SQL: CREATE INDEX IF NOT EXISTS idx_extracts_project_id ON extracts(project_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.invoices
[SQL: CREATE INDEX IF NOT EXISTS idx_invoices_supplier_id ON invoices(supplier_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.invoices
[SQL: CREATE INDEX IF NOT EXISTS idx_invoices_project_id ON invoices(project_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.maintenance_tasks
[SQL: CREATE INDEX IF NOT EXISTS idx_maintenance_tasks_project_id ON maintenance_tasks(project_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.daily_tasks
[SQL: CREATE INDEX IF NOT EXISTS idx_daily_tasks_assigned_to ON daily_tasks(assigned_to)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.activity_log
[SQL: CREATE INDEX IF NOT EXISTS idx_activity_log_user_id ON activity_log(user_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.activity_log
[SQL: CREATE INDEX IF NOT EXISTS idx_activity_log_created_at ON activity_log(created_at)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | INFO | database:create_tables:133 | تم إنشاء جداول قاعدة البيانات بنجاح
2025-06-30 17:19:01 | INFO | database:init_database:241 | تم تهيئة قاعدة البيانات بنجاح
2025-06-30 17:19:01 | ERROR | database:get_session:149 | خطأ في جلسة قاعدة البيانات: Textual SQL expression 'SELECT COUNT(*) FROM user...' should be explicitly declared as text('SELECT COUNT(*) FROM user...')
2025-06-30 17:19:01 | ERROR | __main__:create_admin_user:51 | خطأ في إنشاء المستخدم الإداري: Textual SQL expression 'SELECT COUNT(*) FROM user...' should be explicitly declared as text('SELECT COUNT(*) FROM user...')
2025-06-30 17:19:01 | ERROR | __main__:main:168 | خطأ في إعداد قاعدة البيانات: Textual SQL expression 'SELECT COUNT(*) FROM user...' should be explicitly declared as text('SELECT COUNT(*) FROM user...')
2025-06-30 17:19:01 | INFO | database:close:228 | تم إغلاق الاتصال بقاعدة البيانات
2025-06-30 17:20:07 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 17:20:07 | INFO | database:test_connection:159 | تم اختبار الاتصال بقاعدة البيانات بنجاح
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.units
[SQL: CREATE INDEX IF NOT EXISTS idx_units_project_id ON units(project_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.units
[SQL: CREATE INDEX IF NOT EXISTS idx_units_status ON units(status)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.contracts
[SQL: CREATE INDEX IF NOT EXISTS idx_contracts_customer_id ON contracts(customer_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.contracts
[SQL: CREATE INDEX IF NOT EXISTS idx_contracts_unit_id ON contracts(unit_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.payments
[SQL: CREATE INDEX IF NOT EXISTS idx_payments_contract_id ON payments(contract_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.extracts
[SQL: CREATE INDEX IF NOT EXISTS idx_extracts_contractor_id ON extracts(contractor_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.extracts
[SQL: CREATE INDEX IF NOT EXISTS idx_extracts_project_id ON extracts(project_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.invoices
[SQL: CREATE INDEX IF NOT EXISTS idx_invoices_supplier_id ON invoices(supplier_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.invoices
[SQL: CREATE INDEX IF NOT EXISTS idx_invoices_project_id ON invoices(project_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.maintenance_tasks
[SQL: CREATE INDEX IF NOT EXISTS idx_maintenance_tasks_project_id ON maintenance_tasks(project_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.daily_tasks
[SQL: CREATE INDEX IF NOT EXISTS idx_daily_tasks_assigned_to ON daily_tasks(assigned_to)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.activity_log
[SQL: CREATE INDEX IF NOT EXISTS idx_activity_log_user_id ON activity_log(user_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.activity_log
[SQL: CREATE INDEX IF NOT EXISTS idx_activity_log_created_at ON activity_log(created_at)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | INFO | database:create_tables:133 | تم إنشاء جداول قاعدة البيانات بنجاح
2025-06-30 17:20:07 | INFO | database:init_database:241 | تم تهيئة قاعدة البيانات بنجاح
2025-06-30 17:20:07 | ERROR | database:get_session:149 | خطأ في جلسة قاعدة البيانات: (sqlite3.OperationalError) no such table: users
[SQL: SELECT COUNT(*) FROM users WHERE username = 'admin']
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | ERROR | __main__:create_admin_user:58 | خطأ في إنشاء المستخدم الإداري: (sqlite3.OperationalError) no such table: users
[SQL: SELECT COUNT(*) FROM users WHERE username = 'admin']
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | ERROR | __main__:main:175 | خطأ في إعداد قاعدة البيانات: (sqlite3.OperationalError) no such table: users
[SQL: SELECT COUNT(*) FROM users WHERE username = 'admin']
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | INFO | database:close:228 | تم إغلاق الاتصال بقاعدة البيانات
2025-06-30 17:34:27 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 17:34:27 | INFO | database_sqlite:create_tables:64 | تم إنشاء جداول قاعدة البيانات بنجاح
2025-06-30 17:34:28 | INFO | database_sqlite:create_admin_user:155 | تم إنشاء المستخدم الإداري الافتراضي
2025-06-30 17:34:28 | INFO | database_sqlite:create_sample_data:214 | تم إنشاء البيانات التجريبية بنجاح
2025-06-30 17:34:28 | INFO | database_sqlite:init_sqlite_database:260 | تم تهيئة قاعدة بيانات SQLite بنجاح
2025-06-30 17:36:05 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 17:42:12 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 17:44:34 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 17:50:31 | INFO | main_app:login:151 | تم تسجيل دخول المستخدم: admin
2025-06-30 17:50:32 | ERROR | main_app:load_projects:790 | خطأ في تحميل المشاريع: 'RafeaMainApp' object has no attribute 'status_label'
2025-06-30 17:50:32 | ERROR | main_app:load_customers:938 | خطأ في تحميل العملاء: 'RafeaMainApp' object has no attribute 'status_label'
2025-06-30 17:50:34 | ERROR | main_app:load_units:1087 | خطأ في تحميل الوحدات: 'RafeaMainApp' object has no attribute 'status_label'
2025-06-30 17:51:22 | INFO | __main__:login:151 | تم تسجيل دخول المستخدم: admin
2025-06-30 17:51:23 | ERROR | __main__:load_projects:790 | خطأ في تحميل المشاريع: 'RafeaMainApp' object has no attribute 'status_label'
2025-06-30 17:51:25 | ERROR | __main__:load_customers:938 | خطأ في تحميل العملاء: 'RafeaMainApp' object has no attribute 'status_label'
2025-06-30 17:51:26 | ERROR | __main__:load_units:1087 | خطأ في تحميل الوحدات: 'RafeaMainApp' object has no attribute 'status_label'
2025-06-30 17:51:33 | INFO | __main__:login:149 | تم تسجيل دخول المستخدم: admin
2025-06-30 19:45:33 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 19:45:36 | INFO | main_app:login:151 | تم تسجيل دخول المستخدم: admin
2025-06-30 19:45:37 | ERROR | main_app:load_projects:790 | خطأ في تحميل المشاريع: 'RafeaMainApp' object has no attribute 'status_label'
2025-06-30 19:45:39 | ERROR | main_app:load_customers:938 | خطأ في تحميل العملاء: 'RafeaMainApp' object has no attribute 'status_label'
2025-06-30 19:45:40 | ERROR | main_app:load_units:1087 | خطأ في تحميل الوحدات: 'RafeaMainApp' object has no attribute 'status_label'
2025-06-30 19:46:03 | ERROR | main_app:edit_customer:1014 | خطأ في تعديل العميل: 'sqlite3.Row' object has no attribute 'get'
2025-06-30 19:47:27 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 19:47:32 | INFO | main_app:login:151 | تم تسجيل دخول المستخدم: admin
2025-06-30 19:48:30 | INFO | main_app:logout:1384 | تم تسجيل خروج المستخدم: admin
2025-06-30 19:56:23 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 19:56:26 | INFO | __main__:login:146 | تم تسجيل دخول المستخدم: admin
2025-06-30 19:56:27 | ERROR | database_sqlite:get_connection:41 | خطأ في قاعدة البيانات: no such column: status
2025-06-30 19:56:27 | ERROR | database_sqlite:execute_query:93 | خطأ في تنفيذ الاستعلام: no such column: status
2025-06-30 19:56:27 | ERROR | __main__:update_dashboard_stats:1100 | خطأ في تحديث إحصائيات لوحة التحكم: no such column: status
2025-06-30 19:57:14 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 19:57:15 | INFO | __main__:login:146 | تم تسجيل دخول المستخدم: admin
2025-06-30 19:57:17 | INFO | __main__:update_dashboard_stats:1106 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 19:58:28 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 19:58:29 | INFO | __main__:login:146 | تم تسجيل دخول المستخدم: admin
2025-06-30 19:58:30 | INFO | __main__:update_dashboard_stats:1106 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 20:06:48 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 20:06:51 | INFO | __main__:login:146 | تم تسجيل دخول المستخدم: admin
2025-06-30 20:06:52 | INFO | __main__:update_dashboard_stats:1115 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 20:09:16 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 20:09:40 | INFO | rafea_complete_system:login:146 | تم تسجيل دخول المستخدم: admin
2025-06-30 20:09:41 | INFO | rafea_complete_system:update_dashboard_stats:1115 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 20:10:37 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 20:10:38 | INFO | __main__:login:146 | تم تسجيل دخول المستخدم: admin
2025-06-30 20:10:39 | INFO | __main__:update_dashboard_stats:1115 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 20:27:54 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 20:27:56 | INFO | __main__:login:147 | تم تسجيل دخول المستخدم: admin
2025-06-30 20:27:57 | INFO | __main__:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 20:27:57 | ERROR | database_sqlite:get_connection:41 | خطأ في قاعدة البيانات: no such column: status
2025-06-30 20:27:57 | ERROR | database_sqlite:execute_query:93 | خطأ في تنفيذ الاستعلام: no such column: status
2025-06-30 20:27:57 | ERROR | __main__:load_contractors_data:2011 | خطأ في تحميل المقاولين: no such column: status
2025-06-30 20:27:57 | ERROR | database_sqlite:get_connection:41 | خطأ في قاعدة البيانات: no such column: contractor
2025-06-30 20:27:57 | ERROR | database_sqlite:execute_query:93 | خطأ في تنفيذ الاستعلام: no such column: contractor
2025-06-30 20:27:57 | ERROR | __main__:load_extracts_data:2055 | خطأ في تحميل المستخلصات: no such column: contractor
2025-06-30 20:27:57 | ERROR | database_sqlite:get_connection:41 | خطأ في قاعدة البيانات: no such column: material_type
2025-06-30 20:27:57 | ERROR | database_sqlite:execute_query:93 | خطأ في تنفيذ الاستعلام: no such column: material_type
2025-06-30 20:27:57 | ERROR | __main__:load_suppliers_data:2280 | خطأ في تحميل الموردين: no such column: material_type
2025-06-30 20:27:57 | ERROR | database_sqlite:get_connection:41 | خطأ في قاعدة البيانات: no such column: supplier
2025-06-30 20:27:57 | ERROR | database_sqlite:execute_query:93 | خطأ في تنفيذ الاستعلام: no such column: supplier
2025-06-30 20:27:57 | ERROR | __main__:load_invoices_data:2324 | خطأ في تحميل الفواتير: no such column: supplier
2025-06-30 20:27:57 | ERROR | database_sqlite:get_connection:41 | خطأ في قاعدة البيانات: no such column: project
2025-06-30 20:27:57 | ERROR | database_sqlite:execute_query:93 | خطأ في تنفيذ الاستعلام: no such column: project
2025-06-30 20:27:57 | ERROR | __main__:load_purchase_requests_data:2535 | خطأ في تحميل طلبات الشراء: no such column: project
2025-06-30 20:27:57 | ERROR | database_sqlite:get_connection:41 | خطأ في قاعدة البيانات: no such column: task_number
2025-06-30 20:27:57 | ERROR | database_sqlite:execute_query:93 | خطأ في تنفيذ الاستعلام: no such column: task_number
2025-06-30 20:27:57 | ERROR | __main__:load_maintenance_tasks_data:2740 | خطأ في تحميل مهام الصيانة: no such column: task_number
2025-06-30 20:27:57 | ERROR | database_sqlite:get_connection:41 | خطأ في قاعدة البيانات: no such column: title
2025-06-30 20:27:57 | ERROR | database_sqlite:execute_query:93 | خطأ في تنفيذ الاستعلام: no such column: title
2025-06-30 20:27:57 | ERROR | __main__:load_daily_tasks_data:2924 | خطأ في تحميل المهام اليومية: no such column: title
2025-06-30 20:32:46 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 20:32:49 | INFO | __main__:login:147 | تم تسجيل دخول المستخدم: admin
2025-06-30 20:32:50 | INFO | __main__:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 20:38:33 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 20:38:35 | INFO | __main__:login:147 | تم تسجيل دخول المستخدم: admin
2025-06-30 20:38:36 | INFO | __main__:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 20:41:02 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 20:41:04 | INFO | rafea_complete_system:login:147 | تم تسجيل دخول المستخدم: admin
2025-06-30 20:41:05 | INFO | rafea_complete_system:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 20:46:52 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 20:46:57 | INFO | __main__:login:147 | تم تسجيل دخول المستخدم: admin
2025-06-30 20:46:58 | INFO | __main__:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 20:48:09 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 20:48:10 | INFO | __main__:login:147 | تم تسجيل دخول المستخدم: admin
2025-06-30 20:48:11 | INFO | __main__:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 20:52:05 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 20:52:10 | INFO | __main__:login:147 | تم تسجيل دخول المستخدم: admin
2025-06-30 20:52:12 | INFO | __main__:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 20:53:08 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 20:53:10 | INFO | __main__:login:147 | تم تسجيل دخول المستخدم: admin
2025-06-30 20:53:11 | INFO | __main__:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 21:02:27 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 21:02:29 | INFO | __main__:login:147 | تم تسجيل دخول المستخدم: admin
2025-06-30 21:02:30 | INFO | __main__:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 21:05:56 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 21:05:58 | INFO | __main__:login:147 | تم تسجيل دخول المستخدم: admin
2025-06-30 21:05:59 | INFO | __main__:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 21:12:29 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 21:12:33 | INFO | __main__:login:147 | تم تسجيل دخول المستخدم: admin
2025-06-30 21:12:34 | INFO | __main__:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 21:14:16 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 21:14:20 | INFO | __main__:login:147 | تم تسجيل دخول المستخدم: admin
2025-06-30 21:14:21 | INFO | __main__:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 21:15:53 | ERROR | __main__:new_maintenance_task:2582 | خطأ في إضافة مهمة الصيانة: 'task_number'
2025-06-30 21:15:54 | ERROR | __main__:new_purchase_request:2370 | خطأ في إضافة طلب الشراء: 'request_number'
2025-06-30 21:19:00 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 21:19:02 | INFO | __main__:login:147 | تم تسجيل دخول المستخدم: admin
2025-06-30 21:19:02 | INFO | __main__:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 21:20:20 | ERROR | __main__:new_daily_task:2791 | خطأ في إضافة المهمة اليومية: 'title'
2025-06-30 21:20:22 | ERROR | __main__:new_maintenance_task:2582 | خطأ في إضافة مهمة الصيانة: 'task_number'
2025-06-30 21:20:23 | ERROR | __main__:new_purchase_request:2370 | خطأ في إضافة طلب الشراء: 'request_number'
2025-06-30 21:20:24 | ERROR | __main__:new_purchase_request:2370 | خطأ في إضافة طلب الشراء: 'request_number'
2025-06-30 21:26:48 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 21:26:50 | INFO | __main__:login:147 | تم تسجيل دخول المستخدم: admin
2025-06-30 21:26:51 | INFO | __main__:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 21:27:35 | ERROR | __main__:new_daily_task:2791 | خطأ في إضافة المهمة اليومية: 'title'
2025-06-30 21:27:37 | ERROR | __main__:new_maintenance_task:2582 | خطأ في إضافة مهمة الصيانة: 'task_number'
2025-06-30 21:27:37 | ERROR | __main__:new_purchase_request:2370 | خطأ في إضافة طلب الشراء: 'request_number'
2025-06-30 21:28:17 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 21:28:19 | INFO | __main__:login:147 | تم تسجيل دخول المستخدم: admin
2025-06-30 21:28:20 | INFO | __main__:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 21:28:39 | ERROR | __main__:add_project:1188 | خطأ في إضافة المشروع: 'completion_percentage'
2025-06-30 21:31:27 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 21:31:29 | INFO | __main__:login:147 | تم تسجيل دخول المستخدم: admin
2025-06-30 21:31:31 | INFO | __main__:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 21:31:35 | INFO | utils.logger_setup:setup_logger:60 | تم إعداد نظام السجلات بنجاح
2025-06-30 21:31:48 | ERROR | __main__:add_project:1188 | خطأ في إضافة المشروع: 'completion_percentage'
2025-06-30 21:31:56 | ERROR | __main__:edit_project:1241 | خطأ في تعديل المشروع: 'completion_percentage'
2025-06-30 21:32:00 | INFO | __main__:update_dashboard_stats:1129 | تم تحديث إحصائيات لوحة التحكم
2025-06-30 21:32:25 | ERROR | __main__:add_customer:1383 | خطأ في إضافة العميل: 'address'
