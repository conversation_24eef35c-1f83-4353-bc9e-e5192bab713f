# دليل التثبيت - نظام إدارة شركة رافع للتطوير العقاري
## Installation Guide - Rafea Real Estate Management System

## 📋 المتطلبات الأساسية

### 1. متطلبات النظام
- **نظام التشغيل**: Windows 10 أو أحدث
- **المعالج**: Intel Core i3 أو ما يعادله
- **الذاكرة**: 4 جيجابايت RAM (8 جيجابايت مُوصى به)
- **المساحة**: 5 جيجابايت مساحة فارغة
- **الشبكة**: Wi-Fi أو Ethernet للشبكة المحلية

### 2. البرامج المطلوبة
- **Python 3.8+**: [تحميل من هنا](https://www.python.org/downloads/)
- **PostgreSQL 12+**: [تحميل من هنا](https://www.postgresql.org/download/)

## 🚀 التثبيت السريع

### الطريقة الأولى: التثبيت التلقائي
1. تحميل ملفات المشروع
2. تشغيل `quick_setup.bat` كمدير
3. اتباع التعليمات على الشاشة

### الطريقة الثانية: التثبيت اليدوي

#### الخطوة 1: تثبيت Python
```bash
# تحميل وتثبيت Python من الموقع الرسمي
# تأكد من إضافة Python إلى PATH
python --version
```

#### الخطوة 2: تثبيت PostgreSQL
```bash
# تحميل وتثبيت PostgreSQL
# تذكر كلمة مرور المستخدم postgres
```

#### الخطوة 3: إعداد البيئة الافتراضية
```bash
# إنشاء بيئة افتراضية
python -m venv rafea_env

# تفعيل البيئة الافتراضية
rafea_env\Scripts\activate

# تحديث pip
python -m pip install --upgrade pip
```

#### الخطوة 4: تثبيت المتطلبات
```bash
# تثبيت المكتبات المطلوبة
pip install -r requirements.txt
```

#### الخطوة 5: إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
createdb -U postgres rafea_real_estate

# تشغيل إعداد قاعدة البيانات
python setup_database.py
```

#### الخطوة 6: تشغيل التطبيق
```bash
# تشغيل التطبيق
python run.py

# أو استخدام ملف التشغيل
start.bat
```

## ⚙️ الإعدادات

### إعداد قاعدة البيانات
قم بتحديث ملف `config.py`:

```python
DATABASE_CONFIG = {
    'host': 'localhost',          # عنوان الخادم
    'port': '5432',              # منفذ PostgreSQL
    'database': 'rafea_real_estate',  # اسم قاعدة البيانات
    'username': 'postgres',       # اسم المستخدم
    'password': 'your_password'   # كلمة المرور
}
```

### إعداد الشبكة المحلية
```python
NETWORK_CONFIG = {
    'server_host': '0.0.0.0',    # للسماح بالاتصالات الخارجية
    'server_port': 8080,         # منفذ الخدمة
    'max_connections': 50        # أقصى عدد اتصالات
}
```

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ "Python غير موجود"
**الحل:**
- تأكد من تثبيت Python
- أضف Python إلى متغير PATH
- أعد تشغيل Command Prompt

#### 2. خطأ "pip غير موجود"
**الحل:**
```bash
python -m ensurepip --upgrade
python -m pip install --upgrade pip
```

#### 3. خطأ في الاتصال بقاعدة البيانات
**الحل:**
- تأكد من تشغيل خدمة PostgreSQL
- تحقق من إعدادات الاتصال في `config.py`
- تأكد من صحة اسم المستخدم وكلمة المرور

#### 4. خطأ في تثبيت المكتبات
**الحل:**
```bash
# تحديث pip
python -m pip install --upgrade pip

# تثبيت المكتبات واحدة تلو الأخرى
pip install PyQt5
pip install psycopg2-binary
pip install bcrypt
pip install loguru
pip install reportlab
```

#### 5. مشاكل الخطوط العربية
**الحل:**
- تأكد من وجود خطوط عربية في النظام
- قم بتثبيت خط "Arial Unicode MS"

## 🌐 إعداد الشبكة المحلية

### للخادم الرئيسي:
1. تثبيت PostgreSQL على الجهاز الرئيسي
2. تكوين PostgreSQL للسماح بالاتصالات الخارجية:
   ```
   # في ملف postgresql.conf
   listen_addresses = '*'
   
   # في ملف pg_hba.conf
   host all all ***********/24 md5
   ```
3. إعادة تشغيل خدمة PostgreSQL

### للأجهزة العميلة:
1. تحديث `config.py` بعنوان IP للخادم:
   ```python
   DATABASE_CONFIG = {
       'host': '*************',  # IP الخادم الرئيسي
       'port': '5432',
       'database': 'rafea_real_estate',
       'username': 'postgres',
       'password': 'password'
   }
   ```

## 🔐 الأمان

### تغيير كلمة المرور الافتراضية
1. تسجيل الدخول بالبيانات الافتراضية:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`
2. الذهاب إلى الإعدادات
3. تغيير كلمة المرور

### إعدادات الأمان الإضافية
```python
SECURITY_CONFIG = {
    'password_min_length': 8,
    'session_timeout': 3600,
    'max_login_attempts': 5,
    'lockout_duration': 900
}
```

## 📊 النسخ الاحتياطية

### النسخ الاحتياطي التلقائي
```python
BACKUP_CONFIG = {
    'enabled': True,
    'schedule': 'daily',
    'time': '02:00',
    'location': 'data/backups'
}
```

### النسخ الاحتياطي اليدوي
```bash
# إنشاء نسخة احتياطية
pg_dump -U postgres rafea_real_estate > backup.sql

# استعادة من نسخة احتياطية
psql -U postgres rafea_real_estate < backup.sql
```

## 📞 الدعم الفني

### في حالة مواجهة مشاكل:
1. تحقق من ملفات السجلات في مجلد `logs/`
2. راجع هذا الدليل للحلول الشائعة
3. تواصل مع الدعم الفني:
   - البريد الإلكتروني: <EMAIL>
   - الهاتف: +966-XX-XXXXXXX

## ✅ التحقق من التثبيت

بعد التثبيت، تأكد من:
- [ ] تشغيل التطبيق بنجاح
- [ ] تسجيل الدخول بالبيانات الافتراضية
- [ ] عرض لوحة التحكم
- [ ] إضافة مشروع تجريبي
- [ ] إنشاء تقرير بسيط

---

**تم إعداد هذا الدليل بواسطة فريق التطوير في شركة رافع للتطوير العقاري**
