# -*- coding: utf-8 -*-
"""
ملف التكوين الرئيسي لتطبيق شركة رافع للتطوير العقاري
Main Configuration File for Rafea Real Estate Development Application
"""

import os
from pathlib import Path

# مسارات التطبيق
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
LOGS_DIR = BASE_DIR / "logs"
REPORTS_DIR = BASE_DIR / "reports"
ASSETS_DIR = BASE_DIR / "assets"

# إنشاء المجلدات إذا لم تكن موجودة
for directory in [DATA_DIR, LOGS_DIR, REPORTS_DIR, ASSETS_DIR]:
    directory.mkdir(exist_ok=True)

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'type': 'sqlite',  # استخدام SQLite للتجربة
    'database': str(DATA_DIR / 'rafea_real_estate.db'),
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': os.getenv('DB_PORT', '5432'),
    'username': os.getenv('DB_USER', 'postgres'),
    'password': os.getenv('DB_PASSWORD', 'password'),
    'pool_size': 10,
    'max_overflow': 20
}

# إعدادات الشبكة
NETWORK_CONFIG = {
    'server_host': '0.0.0.0',
    'server_port': 8080,
    'max_connections': 50,
    'timeout': 30
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'password_min_length': 8,
    'session_timeout': 3600,  # ساعة واحدة
    'max_login_attempts': 5,
    'lockout_duration': 900,  # 15 دقيقة
    'secret_key': os.getenv('SECRET_KEY', 'your-secret-key-here')
}

# إعدادات التطبيق
APP_CONFIG = {
    'name': 'نظام إدارة شركة رافع للتطوير العقاري',
    'version': '1.0.0',
    'company': 'شركة رافع للتطوير العقاري',
    'language': 'ar',
    'theme': 'default',
    'auto_backup': True,
    'backup_interval': 24,  # ساعة
    'max_backup_files': 30
}

# إعدادات التقارير
REPORTS_CONFIG = {
    'default_format': 'pdf',
    'page_size': 'A4',
    'orientation': 'portrait',
    'font_name': 'Arial Unicode MS',
    'font_size': 12,
    'margin_top': 2.5,
    'margin_bottom': 2.5,
    'margin_left': 2.0,
    'margin_right': 2.0
}

# إعدادات السجلات
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}',
    'rotation': '10 MB',
    'retention': '30 days',
    'compression': 'zip'
}

# إعدادات واجهة المستخدم
UI_CONFIG = {
    'window_title': 'نظام إدارة شركة رافع للتطوير العقاري',
    'window_icon': str(ASSETS_DIR / 'icon.png'),
    'window_width': 1200,
    'window_height': 800,
    'min_width': 1000,
    'min_height': 600,
    'style_sheet': str(ASSETS_DIR / 'styles.qss'),
    'rtl_support': True
}

# أنواع المستخدمين والصلاحيات
USER_TYPES = {
    'admin': {
        'name': 'مدير النظام',
        'permissions': ['all']
    },
    'accountant': {
        'name': 'محاسب',
        'permissions': ['view_reports', 'manage_payments', 'manage_invoices', 'manage_extracts']
    },
    'engineer': {
        'name': 'مهندس',
        'permissions': ['manage_projects', 'manage_maintenance', 'view_reports']
    },
    'sales': {
        'name': 'مبيعات',
        'permissions': ['manage_customers', 'manage_contracts', 'manage_units', 'view_reports']
    },
    'maintenance': {
        'name': 'صيانة',
        'permissions': ['manage_maintenance', 'manage_daily_tasks', 'view_reports']
    }
}

# حالات المشاريع
PROJECT_STATUSES = {
    'planning': 'تخطيط',
    'in_progress': 'قيد التنفيذ',
    'completed': 'مكتمل',
    'on_hold': 'متوقف مؤقتاً'
}

# حالات الوحدات
UNIT_STATUSES = {
    'available': 'متاح',
    'reserved': 'محجوز',
    'sold': 'مباع',
    'maintenance': 'تحت الصيانة'
}

# أنواع الوحدات
UNIT_TYPES = {
    'apartment': 'شقة',
    'villa': 'فيلا',
    'commercial': 'محل تجاري',
    'office': 'مكتب',
    'warehouse': 'مستودع'
}

# طرق الدفع
PAYMENT_METHODS = {
    'cash': 'نقداً',
    'bank_transfer': 'تحويل بنكي',
    'check': 'شيك',
    'card': 'بطاقة ائتمان'
}

# مستويات الأولوية
PRIORITY_LEVELS = {
    'low': 'منخفض',
    'medium': 'متوسط',
    'high': 'عالي',
    'urgent': 'عاجل'
}

# حالات المهام
TASK_STATUSES = {
    'pending': 'في الانتظار',
    'in_progress': 'قيد التنفيذ',
    'completed': 'مكتمل',
    'cancelled': 'ملغي'
}

# إعدادات النسخ الاحتياطي
BACKUP_CONFIG = {
    'enabled': True,
    'schedule': 'daily',
    'time': '02:00',
    'location': str(DATA_DIR / 'backups'),
    'compress': True,
    'encrypt': True
}

# رسائل النظام
SYSTEM_MESSAGES = {
    'login_success': 'تم تسجيل الدخول بنجاح',
    'login_failed': 'فشل في تسجيل الدخول',
    'access_denied': 'ليس لديك صلاحية للوصول إلى هذه الصفحة',
    'data_saved': 'تم حفظ البيانات بنجاح',
    'data_deleted': 'تم حذف البيانات بنجاح',
    'operation_failed': 'فشل في تنفيذ العملية',
    'connection_error': 'خطأ في الاتصال بقاعدة البيانات',
    'validation_error': 'خطأ في التحقق من صحة البيانات'
}

def get_database_url():
    """إنشاء رابط قاعدة البيانات"""
    if DATABASE_CONFIG.get('type') == 'sqlite':
        return f"sqlite:///{DATABASE_CONFIG['database']}"
    else:
        return f"postgresql://{DATABASE_CONFIG['username']}:{DATABASE_CONFIG['password']}@{DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}"

def get_user_permissions(user_type):
    """الحصول على صلاحيات المستخدم"""
    return USER_TYPES.get(user_type, {}).get('permissions', [])

def has_permission(user_type, permission):
    """التحقق من وجود صلاحية معينة للمستخدم"""
    permissions = get_user_permissions(user_type)
    return 'all' in permissions or permission in permissions
