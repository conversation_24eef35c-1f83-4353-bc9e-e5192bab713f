#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح حقول البيانات في جميع النوافذ
Fix Data Fields in All Dialogs
"""

def fix_all_dialog_data():
    """إصلاح جميع حقول البيانات في النوافذ"""
    
    print("🔧 إصلاح حقول البيانات في جميع النوافذ...")
    
    try:
        # قراءة الملف
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح نافذة المستخدمين
        user_data_old = """            user_data = {
                'username': self.username_entry.get().strip(),
                'email': self.email_entry.get().strip(),
                'user_type': self.user_type_combo.get(),
                'is_active': self.is_active_var.get(),
                'created_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }"""
        
        user_data_new = """            user_data = {
                'username': self.username_entry.get().strip(),
                'email': self.email_entry.get().strip(),
                'user_type': self.user_type_combo.get(),
                'is_active': self.is_active_var.get(),
                'full_name': self.username_entry.get().strip(),  # إضافة الحقل المفقود
                'created_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }"""
        
        content = content.replace(user_data_old, user_data_new)
        
        # إصلاح نافذة الوحدات
        unit_data_old = """            unit_data = {
                'unit_number': self.unit_number_entry.get().strip(),
                'floor_number': int(self.floor_entry.get()) if self.floor_entry.get().strip() else 1,
                'unit_type': self.unit_type_combo.get(),
                'area': float(self.area_entry.get()) if self.area_entry.get().strip() else 0.0,
                'price': float(self.price_entry.get()) if self.price_entry.get().strip() else 0.0,
                'status': 'available',
                'created_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }"""
        
        unit_data_new = """            unit_data = {
                'unit_number': self.unit_number_entry.get().strip(),
                'floor_number': int(self.floor_entry.get()) if self.floor_entry.get().strip() else 1,
                'unit_type': self.unit_type_combo.get(),
                'area': float(self.area_entry.get()) if self.area_entry.get().strip() else 0.0,
                'price': float(self.price_entry.get()) if self.price_entry.get().strip() else 0.0,
                'status': 'available',
                'project_id': 1,  # إضافة الحقل المفقود
                'description': '',  # إضافة الحقل المفقود
                'created_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }"""
        
        content = content.replace(unit_data_old, unit_data_new)
        
        # إصلاح نافذة المقاولين
        contractor_data_old = """            contractor_data = {
                'name': self.name_entry.get().strip(),
                'phone': self.phone_entry.get().strip(),
                'email': self.email_entry.get().strip(),
                'specialty': self.specialty_combo.get(),
                'address': self.address_entry.get().strip(),
                'registration_date': datetime.now().strftime("%Y-%m-%d"),
                'status': 'active'
            }"""
        
        contractor_data_new = """            contractor_data = {
                'name': self.name_entry.get().strip(),
                'phone': self.phone_entry.get().strip(),
                'email': self.email_entry.get().strip(),
                'specialty': self.specialty_combo.get(),
                'address': self.address_entry.get().strip(),
                'license_number': '',  # إضافة الحقل المفقود
                'rating': 0.0,  # إضافة الحقل المفقود
                'notes': '',  # إضافة الحقل المفقود
                'registration_date': datetime.now().strftime("%Y-%m-%d"),
                'status': 'active'
            }"""
        
        content = content.replace(contractor_data_old, contractor_data_new)
        
        # إصلاح نافذة الموردين
        supplier_data_old = """            supplier_data = {
                'name': self.name_entry.get().strip(),
                'phone': self.phone_entry.get().strip(),
                'email': self.email_entry.get().strip(),
                'material_type': self.material_type_combo.get(),
                'address': self.address_entry.get().strip(),
                'registration_date': datetime.now().strftime("%Y-%m-%d"),
                'status': 'active'
            }"""
        
        supplier_data_new = """            supplier_data = {
                'name': self.name_entry.get().strip(),
                'phone': self.phone_entry.get().strip(),
                'email': self.email_entry.get().strip(),
                'material_type': self.material_type_combo.get(),
                'address': self.address_entry.get().strip(),
                'contact_person': '',  # إضافة الحقل المفقود
                'tax_number': '',  # إضافة الحقل المفقود
                'notes': '',  # إضافة الحقل المفقود
                'registration_date': datetime.now().strftime("%Y-%m-%d"),
                'status': 'active'
            }"""
        
        content = content.replace(supplier_data_old, supplier_data_new)
        
        # إصلاح نافذة المستخلصات
        extract_data_old = """            extract_data = {
                'extract_number': self.extract_number_entry.get().strip(),
                'contractor_name': self.contractor_name_entry.get().strip(),
                'amount': float(self.amount_entry.get()) if self.amount_entry.get().strip() else 0.0,
                'extract_date': self.date_entry.get().strip(),
                'status': self.status_combo.get(),
                'description': self.description_text.get('1.0', tk.END).strip(),
                'created_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }"""
        
        extract_data_new = """            extract_data = {
                'extract_number': self.extract_number_entry.get().strip(),
                'contractor_id': 1,  # إضافة الحقل المفقود
                'contractor_name': self.contractor_name_entry.get().strip(),
                'project_id': 1,  # إضافة الحقل المفقود
                'amount': float(self.amount_entry.get()) if self.amount_entry.get().strip() else 0.0,
                'extract_date': self.date_entry.get().strip(),
                'status': self.status_combo.get(),
                'description': self.description_text.get('1.0', tk.END).strip(),
                'created_by': 1,  # إضافة الحقل المفقود
                'created_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }"""
        
        content = content.replace(extract_data_old, extract_data_new)
        
        # إصلاح نافذة الفواتير
        invoice_data_old = """            invoice_data = {
                'invoice_number': self.invoice_number_entry.get().strip(),
                'supplier_name': self.supplier_name_entry.get().strip(),
                'amount': float(self.amount_entry.get()) if self.amount_entry.get().strip() else 0.0,
                'invoice_date': self.date_entry.get().strip(),
                'status': self.status_combo.get(),
                'created_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }"""
        
        invoice_data_new = """            invoice_data = {
                'invoice_number': self.invoice_number_entry.get().strip(),
                'supplier_id': 1,  # إضافة الحقل المفقود
                'supplier_name': self.supplier_name_entry.get().strip(),
                'amount': float(self.amount_entry.get()) if self.amount_entry.get().strip() else 0.0,
                'invoice_date': self.date_entry.get().strip(),
                'due_date': self.date_entry.get().strip(),  # إضافة الحقل المفقود
                'status': self.status_combo.get(),
                'description': '',  # إضافة الحقل المفقود
                'created_by': 1,  # إضافة الحقل المفقود
                'created_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }"""
        
        content = content.replace(invoice_data_old, invoice_data_new)
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح جميع حقول البيانات!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {e}")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🔧 إصلاح حقول البيانات في جميع النوافذ")
    print("=" * 70)
    
    success = fix_all_dialog_data()
    
    if success:
        print("\n🎉 تم إصلاح جميع حقول البيانات بنجاح!")
        print("الآن يمكن حفظ البيانات في قاعدة البيانات بدون أخطاء.")
    else:
        print("\n❌ فشل في الإصلاح!")
    
    input("\nاضغط Enter للخروج...")
