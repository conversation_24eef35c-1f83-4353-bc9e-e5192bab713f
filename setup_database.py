# -*- coding: utf-8 -*-
"""
إعداد قاعدة البيانات الأولي
Initial Database Setup
"""

import sys
import os
from pathlib import Path
import bcrypt

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database import db_manager, init_database
from config import DATABASE_CONFIG
from utils.logger_setup import setup_logger
from loguru import logger

def create_admin_user():
    """إنشاء المستخدم الإداري الافتراضي"""
    try:
        # تشفير كلمة المرور الافتراضية
        password = "admin123"
        password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        with db_manager.get_session() as session:
            # التحقق من وجود المستخدم الإداري
            result = session.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
            count = result.fetchone()[0]
            
            if count == 0:
                # إنشاء المستخدم الإداري
                session.execute("""
                    INSERT INTO users (username, password_hash, full_name, user_type, is_active)
                    VALUES (%s, %s, %s, %s, %s)
                """, ('admin', password_hash, 'مدير النظام', 'admin', True))
                session.commit()
                
                logger.info("تم إنشاء المستخدم الإداري الافتراضي")
                print("✅ تم إنشاء المستخدم الإداري:")
                print("   اسم المستخدم: admin")
                print("   كلمة المرور: admin123")
                print("   ⚠️  يرجى تغيير كلمة المرور بعد تسجيل الدخول الأول")
            else:
                logger.info("المستخدم الإداري موجود بالفعل")
                print("ℹ️  المستخدم الإداري موجود بالفعل")
                
    except Exception as e:
        logger.error(f"خطأ في إنشاء المستخدم الإداري: {e}")
        print(f"❌ خطأ في إنشاء المستخدم الإداري: {e}")
        raise

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    try:
        with db_manager.get_session() as session:
            # التحقق من وجود بيانات
            result = session.execute("SELECT COUNT(*) FROM projects")
            count = result.fetchone()[0]
            
            if count == 0:
                # إضافة مشروع تجريبي
                session.execute("""
                    INSERT INTO projects (name, location, project_type, total_cost, 
                                        completion_percentage, status, description, created_by)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    'مشروع الواحة السكني',
                    'الرياض - حي النرجس',
                    'سكني',
                    5000000.00,
                    25.0,
                    'in_progress',
                    'مشروع سكني متكامل يضم 100 وحدة سكنية',
                    1  # admin user id
                ))
                
                # إضافة وحدات تجريبية
                project_id = session.execute("SELECT id FROM projects WHERE name = %s", ('مشروع الواحة السكني',)).fetchone()[0]
                
                for i in range(1, 11):
                    session.execute("""
                        INSERT INTO units (project_id, unit_number, floor_number, unit_type, 
                                         area, price, status)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """, (
                        project_id,
                        f'A{i:02d}',
                        (i - 1) // 4 + 1,
                        'شقة',
                        120.5,
                        450000.00,
                        'available' if i <= 7 else 'sold'
                    ))
                
                # إضافة عميل تجريبي
                session.execute("""
                    INSERT INTO customers (full_name, phone, email, address, customer_type)
                    VALUES (%s, %s, %s, %s, %s)
                """, (
                    'أحمد محمد السعيد',
                    '0501234567',
                    '<EMAIL>',
                    'الرياض - حي الملز',
                    'individual'
                ))
                
                session.commit()
                logger.info("تم إنشاء البيانات التجريبية")
                print("✅ تم إنشاء البيانات التجريبية")
            else:
                print("ℹ️  البيانات التجريبية موجودة بالفعل")
                
    except Exception as e:
        logger.error(f"خطأ في إنشاء البيانات التجريبية: {e}")
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("إعداد قاعدة البيانات لنظام شركة رافع للتطوير العقاري")
    print("=" * 60)
    
    # إعداد نظام السجلات
    setup_logger()
    
    try:
        # اختبار الاتصال بقاعدة البيانات
        print("🔍 اختبار الاتصال بقاعدة البيانات...")
        if not db_manager.test_connection():
            print("❌ فشل في الاتصال بقاعدة البيانات")
            print("يرجى التحقق من:")
            print("1. تشغيل خدمة PostgreSQL")
            print("2. إعدادات الاتصال في config.py")
            print("3. صحة اسم المستخدم وكلمة المرور")
            return 1
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # تهيئة قاعدة البيانات
        print("🏗️  إنشاء جداول قاعدة البيانات...")
        if not init_database():
            print("❌ فشل في إنشاء جداول قاعدة البيانات")
            return 1
        
        print("✅ تم إنشاء جداول قاعدة البيانات بنجاح")
        
        # إنشاء المستخدم الإداري
        print("👤 إنشاء المستخدم الإداري...")
        create_admin_user()
        
        # إنشاء البيانات التجريبية
        print("📊 إنشاء البيانات التجريبية...")
        create_sample_data()
        
        print("\n" + "=" * 60)
        print("✅ تم إعداد قاعدة البيانات بنجاح!")
        print("يمكنك الآن تشغيل التطبيق باستخدام:")
        print("   python run.py")
        print("   أو تشغيل ملف start.bat")
        print("=" * 60)
        
        return 0
        
    except Exception as e:
        logger.error(f"خطأ في إعداد قاعدة البيانات: {e}")
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return 1
    
    finally:
        # إغلاق الاتصال
        db_manager.close()

if __name__ == "__main__":
    sys.exit(main())
