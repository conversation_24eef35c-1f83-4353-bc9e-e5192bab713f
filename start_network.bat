@echo off
echo ======================================================================
echo 🏢 نظام إدارة شركة رافع للتطوير العقاري - إصدار الشبكة
echo    Rafea Real Estate Management System - Network Version
echo ======================================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على هذا الجهاز
    echo يرجى تثبيت Python أولاً من: https://python.org
    pause
    exit /b 1
)

REM التحقق من وجود ملفات النظام
if not exist "rafea_complete_system.py" (
    echo ❌ خطأ: ملفات النظام غير موجودة
    echo تأكد من نسخ جميع ملفات النظام إلى هذا المجلد
    pause
    exit /b 1
)

REM إنشاء المجلدات المطلوبة
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "utils" mkdir utils

echo ✅ جاري تشغيل النظام...
echo.

REM تشغيل النظام
python rafea_complete_system.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء تشغيل النظام
    echo راجع ملفات السجلات في مجلد logs للمزيد من التفاصيل
    pause
)
