@echo off
chcp 65001 > nul
title نظام إدارة شركة رافع للتطوير العقاري

echo ================================================
echo نظام إدارة شركة رافع للتطوير العقاري
echo Rafea Real Estate Development Management System
echo ================================================
echo.

echo جاري التحقق من Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت. يرجى تثبيت Python 3.8 أو أحدث
    echo يمكنك تحميله من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo جاري التحقق من المتطلبات...
python -c "import PyQt5, psycopg2, bcrypt, loguru, reportlab" > nul 2>&1
if errorlevel 1 (
    echo ⚠️  بعض المتطلبات مفقودة. جاري التثبيت...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        pause
        exit /b 1
    )
)

echo ✅ جميع المتطلبات متوفرة
echo.

echo جاري تشغيل التطبيق...
python run.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    echo يرجى التحقق من:
    echo 1. إعدادات قاعدة البيانات في config.py
    echo 2. تشغيل خدمة PostgreSQL
    echo 3. ملفات السجلات في مجلد logs
    echo.
    pause
)

echo.
echo تم إغلاق التطبيق
pause
