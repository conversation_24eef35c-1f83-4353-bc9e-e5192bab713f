# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للتطبيق
Main Application Window
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QMenuBar, QMenu, QAction, QToolBar, QStatusBar,
                            QTabWidget, QLabel, QFrame, QGridLayout,
                            QMessageBox, QSplitter, QTextEdit, QPushButton)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QIcon, QFont, QPixmap
from loguru import logger

from config import APP_CONFIG, UI_CONFIG, USER_TYPES, has_permission
from ui.dashboard_widget import DashboardWidget
from ui.projects_widget import ProjectsWidget
from ui.sales_widget import SalesWidget
from ui.contractors_widget import ContractorsWidget
from ui.suppliers_widget import SuppliersWidget
from ui.purchases_widget import PurchasesWidget
from ui.maintenance_widget import MaintenanceWidget
from ui.tasks_widget import TasksWidget
from ui.reports_widget import ReportsWidget

class MainWindow(QMainWindow):
    """النافذة الرئيسية"""
    
    def __init__(self, user_data):
        super().__init__()
        self.user_data = user_data
        self.current_tab = None
        self.setup_ui()
        self.setup_menu()
        self.setup_toolbar()
        self.setup_statusbar()
        self.setup_connections()
        self.load_dashboard()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعداد النافذة الرئيسية
        self.setWindowTitle(f"{APP_CONFIG['name']} - {self.user_data['full_name']}")
        self.setGeometry(100, 100, UI_CONFIG['window_width'], UI_CONFIG['window_height'])
        self.setMinimumSize(UI_CONFIG['min_width'], UI_CONFIG['min_height'])
        
        # إعداد الأيقونة
        if UI_CONFIG['window_icon']:
            self.setWindowIcon(QIcon(UI_CONFIG['window_icon']))
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # شريط المعلومات العلوي
        self.create_info_bar(main_layout)
        
        # التبويبات الرئيسية
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        self.tab_widget.setMovable(False)
        self.tab_widget.setTabsClosable(False)
        
        # تطبيق ستايل التبويبات
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: white;
            }
            
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                font-weight: bold;
            }
            
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            
            QTabBar::tab:hover {
                background-color: #d5dbdb;
            }
        """)
        
        main_layout.addWidget(self.tab_widget)
        
        # إضافة التبويبات حسب صلاحيات المستخدم
        self.add_tabs_based_on_permissions()
        
    def create_info_bar(self, layout):
        """إنشاء شريط المعلومات العلوي"""
        info_frame = QFrame()
        info_frame.setFrameStyle(QFrame.StyledPanel)
        info_frame.setMaximumHeight(60)
        info_frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                color: white;
                border-radius: 5px;
            }
        """)
        
        info_layout = QHBoxLayout(info_frame)
        info_layout.setContentsMargins(15, 10, 15, 10)
        
        # معلومات المستخدم
        user_info = QLabel(f"مرحباً، {self.user_data['full_name']}")
        user_info.setFont(QFont("Arial", 12, QFont.Bold))
        user_info.setStyleSheet("color: white;")
        
        # نوع المستخدم
        user_type_label = QLabel(f"({USER_TYPES[self.user_data['user_type']]['name']})")
        user_type_label.setFont(QFont("Arial", 10))
        user_type_label.setStyleSheet("color: #bdc3c7;")
        
        # التاريخ والوقت
        self.datetime_label = QLabel()
        self.datetime_label.setFont(QFont("Arial", 10))
        self.datetime_label.setStyleSheet("color: #bdc3c7;")
        self.datetime_label.setAlignment(Qt.AlignRight)
        
        # تحديث التاريخ والوقت كل ثانية
        self.datetime_timer = QTimer()
        self.datetime_timer.timeout.connect(self.update_datetime)
        self.datetime_timer.start(1000)
        self.update_datetime()
        
        info_layout.addWidget(user_info)
        info_layout.addWidget(user_type_label)
        info_layout.addStretch()
        info_layout.addWidget(self.datetime_label)
        
        layout.addWidget(info_frame)
    
    def update_datetime(self):
        """تحديث التاريخ والوقت"""
        from datetime import datetime
        now = datetime.now()
        self.datetime_label.setText(now.strftime("%Y-%m-%d %H:%M:%S"))
    
    def add_tabs_based_on_permissions(self):
        """إضافة التبويبات حسب الصلاحيات"""
        user_type = self.user_data['user_type']
        
        # لوحة التحكم (متاحة للجميع)
        self.dashboard_widget = DashboardWidget(self.user_data)
        self.tab_widget.addTab(self.dashboard_widget, "لوحة التحكم")
        
        # إدارة المشاريع
        if has_permission(user_type, 'manage_projects'):
            self.projects_widget = ProjectsWidget(self.user_data)
            self.tab_widget.addTab(self.projects_widget, "إدارة المشاريع")
        
        # مبيعات الشقق
        if has_permission(user_type, 'manage_customers'):
            self.sales_widget = SalesWidget(self.user_data)
            self.tab_widget.addTab(self.sales_widget, "مبيعات الشقق")
        
        # المقاولون والمستخلصات
        if has_permission(user_type, 'manage_extracts'):
            self.contractors_widget = ContractorsWidget(self.user_data)
            self.tab_widget.addTab(self.contractors_widget, "المقاولون والمستخلصات")
        
        # الموردون والفواتير
        if has_permission(user_type, 'manage_invoices'):
            self.suppliers_widget = SuppliersWidget(self.user_data)
            self.tab_widget.addTab(self.suppliers_widget, "الموردون والفواتير")
        
        # المشتريات
        if has_permission(user_type, 'manage_purchases'):
            self.purchases_widget = PurchasesWidget(self.user_data)
            self.tab_widget.addTab(self.purchases_widget, "المشتريات")
        
        # الصيانة والتشغيل
        if has_permission(user_type, 'manage_maintenance'):
            self.maintenance_widget = MaintenanceWidget(self.user_data)
            self.tab_widget.addTab(self.maintenance_widget, "الصيانة والتشغيل")
        
        # المهام اليومية
        if has_permission(user_type, 'manage_daily_tasks'):
            self.tasks_widget = TasksWidget(self.user_data)
            self.tab_widget.addTab(self.tasks_widget, "المهام اليومية")
        
        # التقارير (متاحة للجميع)
        if has_permission(user_type, 'view_reports'):
            self.reports_widget = ReportsWidget(self.user_data)
            self.tab_widget.addTab(self.reports_widget, "التقارير")
    
    def setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        # إعدادات
        settings_action = QAction("الإعدادات", self)
        settings_action.setShortcut("Ctrl+S")
        settings_action.triggered.connect(self.show_settings)
        file_menu.addAction(settings_action)
        
        file_menu.addSeparator()
        
        # تسجيل الخروج
        logout_action = QAction("تسجيل الخروج", self)
        logout_action.setShortcut("Ctrl+L")
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        # خروج
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة العرض
        view_menu = menubar.addMenu("عرض")
        
        # تحديث
        refresh_action = QAction("تحديث", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_current_tab)
        view_menu.addAction(refresh_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        # حول البرنامج
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setMovable(False)
        
        # تحديث
        refresh_action = QAction("تحديث", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_current_tab)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        # الإعدادات
        settings_action = QAction("الإعدادات", self)
        settings_action.triggered.connect(self.show_settings)
        toolbar.addAction(settings_action)
        
        toolbar.addSeparator()
        
        # تسجيل الخروج
        logout_action = QAction("تسجيل الخروج", self)
        logout_action.triggered.connect(self.logout)
        toolbar.addAction(logout_action)
    
    def setup_statusbar(self):
        """إعداد شريط الحالة"""
        statusbar = self.statusBar()
        
        # رسالة الحالة
        self.status_message = QLabel("جاهز")
        statusbar.addWidget(self.status_message)
        
        # معلومات الاتصال
        self.connection_status = QLabel("متصل")
        self.connection_status.setStyleSheet("color: green; font-weight: bold;")
        statusbar.addPermanentWidget(self.connection_status)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
    
    def load_dashboard(self):
        """تحميل لوحة التحكم"""
        self.tab_widget.setCurrentIndex(0)
    
    def on_tab_changed(self, index):
        """معالج تغيير التبويب"""
        self.current_tab = self.tab_widget.widget(index)
        tab_name = self.tab_widget.tabText(index)
        self.status_message.setText(f"التبويب النشط: {tab_name}")
        
        # تحديث البيانات عند تغيير التبويب
        if hasattr(self.current_tab, 'refresh_data'):
            self.current_tab.refresh_data()
    
    def refresh_current_tab(self):
        """تحديث التبويب الحالي"""
        if self.current_tab and hasattr(self.current_tab, 'refresh_data'):
            self.current_tab.refresh_data()
            self.status_message.setText("تم تحديث البيانات")
    
    def show_settings(self):
        """عرض نافذة الإعدادات"""
        QMessageBox.information(self, "الإعدادات", "نافذة الإعدادات قيد التطوير")
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = f"""
        <h2>{APP_CONFIG['name']}</h2>
        <p><b>الإصدار:</b> {APP_CONFIG['version']}</p>
        <p><b>الشركة:</b> {APP_CONFIG['company']}</p>
        <p><b>الوصف:</b> نظام إدارة شامل للشركات العقارية</p>
        <p><b>المطور:</b> فريق التطوير</p>
        """
        QMessageBox.about(self, "حول البرنامج", about_text)
    
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(
            self,
            "تسجيل الخروج",
            "هل أنت متأكد من تسجيل الخروج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            logger.info(f"تم تسجيل خروج المستخدم: {self.user_data['username']}")
            self.close()
            # إعادة عرض نافذة تسجيل الدخول
            from ui.login_window import LoginWindow
            login_window = LoginWindow()
            login_window.show()
    
    def closeEvent(self, event):
        """معالج إغلاق النافذة"""
        reply = QMessageBox.question(
            self,
            "إغلاق البرنامج",
            "هل أنت متأكد من إغلاق البرنامج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # حفظ الإعدادات
            self.save_window_settings()
            event.accept()
        else:
            event.ignore()
    
    def save_window_settings(self):
        """حفظ إعدادات النافذة"""
        # يمكن إضافة حفظ موقع وحجم النافذة هنا
        pass
