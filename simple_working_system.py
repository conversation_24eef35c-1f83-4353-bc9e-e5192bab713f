#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مبسط يعمل لإدارة شركة رافع للتطوير العقاري
Simple Working System for Rafea Real Estate Development
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import bcrypt
import logging
from datetime import datetime

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SQLiteManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_path='data/rafea_system.db'):
        self.db_path = db_path
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            conn.execute("PRAGMA foreign_keys = OFF")
            cursor = conn.execute(query, params or ())
            if query.strip().upper().startswith('SELECT'):
                result = cursor.fetchall()
                conn.close()
                return result
            else:
                conn.commit()
                rowcount = cursor.rowcount
                conn.close()
                return rowcount
        except Exception as e:
            if conn:
                conn.close()
            logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise

# إنشاء مثيل مدير قاعدة البيانات
sqlite_manager = SQLiteManager()

class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.user_data = None
        self.setup_window()
        self.create_widgets()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.root.title("تسجيل الدخول - نظام إدارة شركة رافع")
        self.root.geometry("400x300")
        self.root.configure(bg='#f0f0f0')
        self.root.resizable(False, False)
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # العنوان
        title_label = tk.Label(self.root, text="نظام إدارة شركة رافع للتطوير العقاري", 
                              font=('Arial', 16, 'bold'), bg='#f0f0f0', fg='#2c3e50')
        title_label.pack(pady=30)
        
        # إطار تسجيل الدخول
        login_frame = tk.Frame(self.root, bg='white', relief='raised', bd=2)
        login_frame.pack(pady=20, padx=50, fill='both', expand=True)
        
        # اسم المستخدم
        tk.Label(login_frame, text="اسم المستخدم:", bg='white', font=('Arial', 12)).pack(pady=10)
        self.username_entry = tk.Entry(login_frame, font=('Arial', 12), width=20)
        self.username_entry.pack(pady=5)
        
        # كلمة المرور
        tk.Label(login_frame, text="كلمة المرور:", bg='white', font=('Arial', 12)).pack(pady=10)
        self.password_entry = tk.Entry(login_frame, font=('Arial', 12), width=20, show='*')
        self.password_entry.pack(pady=5)
        
        # زر تسجيل الدخول
        login_btn = tk.Button(login_frame, text="تسجيل الدخول", bg='#3498db', fg='white',
                             font=('Arial', 12, 'bold'), command=self.login, width=15)
        login_btn.pack(pady=20)
        
        # ربط Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda e: self.login())
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        try:
            query = "SELECT id, username, password_hash, full_name, user_type, is_active FROM users WHERE username = ?"
            result = sqlite_manager.execute_query(query, (username,))
            
            if not result:
                messagebox.showerror("خطأ", "اسم المستخدم غير صحيح")
                return
            
            user = result[0]
            
            if not user['is_active']:
                messagebox.showerror("خطأ", "الحساب غير مفعل")
                return
            
            if bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
                self.user_data = {
                    'id': user['id'],
                    'username': user['username'],
                    'full_name': user['full_name'],
                    'user_type': user['user_type']
                }
                
                logger.info(f"تم تسجيل دخول المستخدم: {username}")
                self.root.destroy()
            else:
                messagebox.showerror("خطأ", "كلمة المرور غير صحيحة")
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل الدخول: {e}")
            messagebox.showerror("خطأ", f"فشل في تسجيل الدخول: {str(e)}")
    
    def run(self):
        """تشغيل نافذة تسجيل الدخول"""
        self.root.mainloop()
        return self.user_data

class MainSystem:
    """النظام الرئيسي"""
    
    def __init__(self, user_data):
        self.user_data = user_data
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.load_data()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title(f"نظام إدارة شركة رافع - {self.user_data['full_name']}")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f5f5f5')
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # شريط العنوان
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(header_frame, text="نظام إدارة شركة رافع للتطوير العقاري",
                              font=('Arial', 18, 'bold'), bg='#2c3e50', fg='white')
        title_label.pack(side='left', padx=20, pady=15)
        
        user_label = tk.Label(header_frame, text=f"مرحباً، {self.user_data['full_name']}",
                             font=('Arial', 12), bg='#2c3e50', fg='white')
        user_label.pack(side='right', padx=20, pady=15)
        
        # التبويبات الرئيسية
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # إنشاء التبويبات
        self.create_dashboard_tab()
        self.create_projects_tab()
        self.create_customers_tab()
        self.create_units_tab()
        self.create_reports_tab()
        
        # شريط الحالة
        self.status_frame = tk.Frame(self.root, bg='#34495e', height=30)
        self.status_frame.pack(fill='x', side='bottom')
        self.status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(self.status_frame, text="جاهز", 
                                    bg='#34495e', fg='white', font=('Arial', 10))
        self.status_label.pack(side='left', padx=10, pady=5)
    
    def create_dashboard_tab(self):
        """إنشاء تبويب لوحة التحكم"""
        dashboard_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(dashboard_frame, text='📊 لوحة التحكم')
        
        # عنوان لوحة التحكم
        title_label = tk.Label(dashboard_frame, text="لوحة التحكم", 
                              font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=20)
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(dashboard_frame, bg='white')
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        # بطاقات الإحصائيات
        self.create_stat_card(stats_frame, "المشاريع", "0", "#3498db", 0, 0)
        self.create_stat_card(stats_frame, "العملاء", "0", "#e74c3c", 0, 1)
        self.create_stat_card(stats_frame, "الوحدات", "0", "#f39c12", 0, 2)
        self.create_stat_card(stats_frame, "العقود", "0", "#27ae60", 0, 3)
        
        # منطقة الأنشطة الحديثة
        activities_label = tk.Label(dashboard_frame, text="الأنشطة الحديثة", 
                                   font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        activities_label.pack(pady=(20, 10))
        
        self.activities_text = tk.Text(dashboard_frame, height=10, width=80, 
                                      font=('Arial', 10), bg='#f8f9fa')
        self.activities_text.pack(padx=20, pady=10)
    
    def create_stat_card(self, parent, title, value, color, row, col):
        """إنشاء بطاقة إحصائية"""
        card_frame = tk.Frame(parent, bg=color, relief='raised', bd=2)
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
        parent.grid_columnconfigure(col, weight=1)
        
        title_label = tk.Label(card_frame, text=title, font=('Arial', 12, 'bold'), 
                              bg=color, fg='white')
        title_label.pack(pady=(10, 5))
        
        value_label = tk.Label(card_frame, text=value, font=('Arial', 18, 'bold'), 
                              bg=color, fg='white')
        value_label.pack(pady=(0, 10))
        
        # حفظ مرجع للتحديث لاحقاً
        setattr(self, f"{title.replace(' ', '_')}_value_label", value_label)
    
    def create_projects_tab(self):
        """إنشاء تبويب المشاريع"""
        projects_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(projects_frame, text='🏗️ المشاريع')
        
        # شريط الأدوات
        toolbar = tk.Frame(projects_frame, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)
        
        tk.Button(toolbar, text="إضافة مشروع", bg='#27ae60', fg='white', 
                 command=self.add_project).pack(side='left', padx=5)
        tk.Button(toolbar, text="تحديث", bg='#3498db', fg='white', 
                 command=self.load_projects).pack(side='left', padx=5)
        
        # جدول المشاريع
        columns = ('الرقم', 'اسم المشروع', 'الموقع', 'الميزانية', 'الحالة', 'تاريخ البداية')
        self.projects_tree = ttk.Treeview(projects_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.projects_tree.heading(col, text=col)
            self.projects_tree.column(col, width=150)
        
        self.projects_tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_customers_tab(self):
        """إنشاء تبويب العملاء"""
        customers_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(customers_frame, text='👥 العملاء')
        
        # شريط الأدوات
        toolbar = tk.Frame(customers_frame, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)
        
        tk.Button(toolbar, text="إضافة عميل", bg='#27ae60', fg='white', 
                 command=self.add_customer).pack(side='left', padx=5)
        tk.Button(toolbar, text="تحديث", bg='#3498db', fg='white', 
                 command=self.load_customers).pack(side='left', padx=5)
        
        # جدول العملاء
        columns = ('الرقم', 'الاسم', 'الهاتف', 'البريد الإلكتروني', 'العنوان')
        self.customers_tree = ttk.Treeview(customers_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.customers_tree.heading(col, text=col)
            self.customers_tree.column(col, width=150)
        
        self.customers_tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_units_tab(self):
        """إنشاء تبويب الوحدات"""
        units_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(units_frame, text='🏠 الوحدات')
        
        # شريط الأدوات
        toolbar = tk.Frame(units_frame, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)
        
        tk.Button(toolbar, text="إضافة وحدة", bg='#27ae60', fg='white', 
                 command=self.add_unit).pack(side='left', padx=5)
        tk.Button(toolbar, text="حجز وحدة", bg='#f39c12', fg='white', 
                 command=self.reserve_unit).pack(side='left', padx=5)
        tk.Button(toolbar, text="بيع وحدة", bg='#e74c3c', fg='white', 
                 command=self.sell_unit).pack(side='left', padx=5)
        tk.Button(toolbar, text="تحديث", bg='#3498db', fg='white', 
                 command=self.load_units).pack(side='left', padx=5)
        
        # جدول الوحدات
        columns = ('الرقم', 'رقم الوحدة', 'النوع', 'المساحة', 'السعر', 'الحالة')
        self.units_tree = ttk.Treeview(units_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.units_tree.heading(col, text=col)
            self.units_tree.column(col, width=120)
        
        self.units_tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        reports_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(reports_frame, text='📊 التقارير')
        
        title_label = tk.Label(reports_frame, text="التقارير والإحصائيات", 
                              font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=20)
        
        # منطقة التقارير
        self.reports_text = tk.Text(reports_frame, height=20, width=80, 
                                   font=('Arial', 10), bg='#f8f9fa')
        self.reports_text.pack(fill='both', expand=True, padx=20, pady=10)
    
    def load_data(self):
        """تحميل جميع البيانات"""
        try:
            self.load_projects()
            self.load_customers()
            self.load_units()
            self.update_dashboard()
            self.update_status("تم تحميل البيانات بنجاح")
        except Exception as e:
            logger.error(f"خطأ في تحميل البيانات: {e}")
            self.update_status(f"خطأ في تحميل البيانات: {str(e)}")
    
    def load_projects(self):
        """تحميل المشاريع"""
        try:
            # مسح البيانات القديمة
            for item in self.projects_tree.get_children():
                self.projects_tree.delete(item)
            
            # تحميل المشاريع
            query = "SELECT id, name, location, budget, status, start_date FROM projects ORDER BY id DESC"
            projects = sqlite_manager.execute_query(query)
            
            for project in projects:
                budget_text = f"{project['budget']:,.0f} ريال" if project['budget'] else "غير محدد"
                self.projects_tree.insert('', 'end', values=(
                    project['id'],
                    project['name'] or '',
                    project['location'] or '',
                    budget_text,
                    project['status'] or '',
                    project['start_date'] or ''
                ))
            
            self.update_status(f"تم تحميل {len(projects)} مشروع")
            
        except Exception as e:
            logger.error(f"خطأ في تحميل المشاريع: {e}")
            self.update_status(f"خطأ في تحميل المشاريع: {str(e)}")
    
    def load_customers(self):
        """تحميل العملاء"""
        try:
            # مسح البيانات القديمة
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)
            
            # تحميل العملاء
            query = "SELECT id, name, phone, email, address FROM customers ORDER BY id DESC"
            customers = sqlite_manager.execute_query(query)
            
            for customer in customers:
                self.customers_tree.insert('', 'end', values=(
                    customer['id'],
                    customer['name'] or '',
                    customer['phone'] or '',
                    customer['email'] or '',
                    customer['address'] or ''
                ))
            
            self.update_status(f"تم تحميل {len(customers)} عميل")
            
        except Exception as e:
            logger.error(f"خطأ في تحميل العملاء: {e}")
            self.update_status(f"خطأ في تحميل العملاء: {str(e)}")
    
    def load_units(self):
        """تحميل الوحدات"""
        try:
            # مسح البيانات القديمة
            for item in self.units_tree.get_children():
                self.units_tree.delete(item)
            
            # تحميل الوحدات
            query = "SELECT id, unit_number, unit_type, area, price, status FROM units ORDER BY id DESC"
            units = sqlite_manager.execute_query(query)
            
            for unit in units:
                area_text = f"{unit['area']:.1f} م²" if unit['area'] else "غير محدد"
                price_text = f"{unit['price']:,.0f} ريال" if unit['price'] else "غير محدد"
                status_text = {
                    'available': 'متاح',
                    'reserved': 'محجوز',
                    'sold': 'مباع'
                }.get(unit['status'], unit['status'])
                
                self.units_tree.insert('', 'end', values=(
                    unit['id'],
                    unit['unit_number'] or '',
                    unit['unit_type'] or '',
                    area_text,
                    price_text,
                    status_text
                ))
            
            self.update_status(f"تم تحميل {len(units)} وحدة")
            
        except Exception as e:
            logger.error(f"خطأ في تحميل الوحدات: {e}")
            self.update_status(f"خطأ في تحميل الوحدات: {str(e)}")
    
    def update_dashboard(self):
        """تحديث لوحة التحكم"""
        try:
            # إحصائيات المشاريع
            projects_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM projects")[0][0]
            if hasattr(self, 'المشاريع_value_label'):
                self.المشاريع_value_label.config(text=str(projects_count))
            
            # إحصائيات العملاء
            customers_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM customers")[0][0]
            if hasattr(self, 'العملاء_value_label'):
                self.العملاء_value_label.config(text=str(customers_count))
            
            # إحصائيات الوحدات
            units_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM units")[0][0]
            if hasattr(self, 'الوحدات_value_label'):
                self.الوحدات_value_label.config(text=str(units_count))
            
            # إحصائيات العقود
            contracts_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM contracts")[0][0]
            if hasattr(self, 'العقود_value_label'):
                self.العقود_value_label.config(text=str(contracts_count))
            
            # تحديث الأنشطة
            activities = f"""
📊 إحصائيات النظام:
• المشاريع: {projects_count}
• العملاء: {customers_count}
• الوحدات: {units_count}
• العقود: {contracts_count}

📅 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎉 مرحباً بك في نظام إدارة شركة رافع للتطوير العقاري!

✅ النظام يعمل بشكل صحيح
✅ البيانات محملة ومتاحة
✅ جميع الوظائف تعمل

📋 يمكنك الآن:
• إضافة مشاريع جديدة
• إدارة العملاء
• إضافة وحدات سكنية
• حجز وبيع الوحدات
• عرض التقارير والإحصائيات
"""
            
            self.activities_text.delete('1.0', tk.END)
            self.activities_text.insert('1.0', activities)
            
        except Exception as e:
            logger.error(f"خطأ في تحديث لوحة التحكم: {e}")
    
    def update_status(self, message):
        """تحديث شريط الحالة"""
        if hasattr(self, 'status_label'):
            self.status_label.config(text=message)
    
    # دوال الإضافة (مبسطة)
    def add_project(self):
        """إضافة مشروع جديد"""
        messagebox.showinfo("قريباً", "سيتم تطوير إضافة المشاريع قريباً")
    
    def add_customer(self):
        """إضافة عميل جديد"""
        messagebox.showinfo("قريباً", "سيتم تطوير إضافة العملاء قريباً")
    
    def add_unit(self):
        """إضافة وحدة جديدة"""
        messagebox.showinfo("قريباً", "سيتم تطوير إضافة الوحدات قريباً")
    
    def reserve_unit(self):
        """حجز وحدة"""
        selection = self.units_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار وحدة للحجز")
            return
        
        item = self.units_tree.item(selection[0])
        unit_id = item['values'][0]
        unit_number = item['values'][1]
        
        if messagebox.askyesno("تأكيد الحجز", f"هل تريد حجز الوحدة {unit_number}؟"):
            try:
                query = "UPDATE units SET status='reserved' WHERE id=?"
                sqlite_manager.execute_query(query, (unit_id,))
                messagebox.showinfo("نجح", f"تم حجز الوحدة {unit_number} بنجاح")
                self.load_units()
                self.update_dashboard()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حجز الوحدة: {str(e)}")
    
    def sell_unit(self):
        """بيع وحدة"""
        selection = self.units_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار وحدة للبيع")
            return
        
        item = self.units_tree.item(selection[0])
        unit_id = item['values'][0]
        unit_number = item['values'][1]
        
        if messagebox.askyesno("تأكيد البيع", f"هل تريد بيع الوحدة {unit_number}؟"):
            try:
                query = "UPDATE units SET status='sold' WHERE id=?"
                sqlite_manager.execute_query(query, (unit_id,))
                messagebox.showinfo("نجح", f"تم بيع الوحدة {unit_number} بنجاح")
                self.load_units()
                self.update_dashboard()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في بيع الوحدة: {str(e)}")
    
    def run(self):
        """تشغيل النظام"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        # نافذة تسجيل الدخول
        login_window = LoginWindow()
        user_data = login_window.run()
        
        if user_data:
            # النظام الرئيسي
            main_system = MainSystem(user_data)
            main_system.run()
        else:
            print("تم إلغاء تسجيل الدخول")
    
    except Exception as e:
        logger.error(f"خطأ في تشغيل النظام: {e}")
        messagebox.showerror("خطأ", f"فشل في تشغيل النظام: {str(e)}")

if __name__ == "__main__":
    main()
