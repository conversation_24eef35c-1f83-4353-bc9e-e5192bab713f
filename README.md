# نظام إدارة شركة رافع للتطوير العقاري
## Rafea Real Estate Development Management System

نظام إدارة شامل ومتكامل لشركة رافع للتطوير العقاري، مصمم خصيصاً لإدارة جميع جوانب العمل العقاري من المشاريع والمبيعات إلى المقاولين والتقارير.

## 🌟 المميزات الرئيسية

### 📊 لوحة التحكم التفاعلية
- عرض ملخصات شاملة للنظام
- إحصائيات مباشرة ومحدثة
- رسوم بيانية تفاعلية
- مؤشرات الأداء الرئيسية

### 🏗️ إدارة المشاريع
- تسجيل وإدارة المشاريع العقارية
- متابعة نسبة الإنجاز والتكاليف
- إدارة الجداول الزمنية
- تصنيف المشاريع حسب النوع والحالة

### 🏠 مبيعات الشقق
- إدارة قاعدة بيانات العملاء
- تسجيل العقود والحجوزات
- متابعة المدفوعات والأقساط
- تتبع حالة كل وحدة سكنية

### 👷 المقاولون والمستخلصات
- إدارة بيانات المقاولين
- تسجيل المستخلصات والمدفوعات
- متابعة الأعمال المنجزة
- تقييم أداء المقاولين

### 🚚 الموردون والفواتير
- قاعدة بيانات شاملة للموردين
- إدارة الفواتير وسجلات الدفع
- متابعة المستحقات
- تصنيف الموردين حسب الفئة

### 🛒 المشتريات
- إدارة طلبات الشراء
- نظام الموافقات المتدرج
- متابعة حالة الطلبات
- ربط المشتريات بالمشاريع

### 🔧 الصيانة والتشغيل
- تسجيل أعمال الصيانة
- متابعة التكاليف التشغيلية
- جدولة المهام الدورية
- تتبع قطع الغيار والمواد

### ✅ المهام اليومية
- تسجيل وتتبع المهام
- توزيع المهام على الفرق
- متابعة الإنجاز والمواعيد
- تقارير الإنتاجية

### 📈 التقارير المتقدمة
- تقارير مالية شاملة
- تقارير إدارية مفصلة
- إمكانية التصدير (PDF/Excel)
- خيارات تصفية وبحث متقدمة

## 🔐 نظام الأمان والصلاحيات

### أنواع المستخدمين
- **مدير النظام**: صلاحيات كاملة
- **محاسب**: إدارة المالية والتقارير
- **مهندس**: إدارة المشاريع والصيانة
- **مبيعات**: إدارة العملاء والعقود
- **صيانة**: إدارة المهام والصيانة

### مميزات الأمان
- تشفير كلمات المرور
- جلسات آمنة مع انتهاء صلاحية
- سجل شامل للأنشطة
- حماية من محاولات الاختراق

## 🌐 الشبكة المحلية

- **عمل بدون إنترنت**: التطبيق يعمل بالكامل على الشبكة المحلية
- **تعدد المستخدمين**: دعم عدة مستخدمين في نفس الوقت
- **مزامنة البيانات**: تحديث فوري للبيانات عبر الشبكة
- **نسخ احتياطية تلقائية**: حماية البيانات من الفقدان

## 🎨 واجهة المستخدم

- **دعم اللغة العربية**: واجهة كاملة باللغة العربية
- **تخطيط RTL**: دعم كامل للكتابة من اليمين إلى اليسار
- **تصميم حديث**: واجهة عصرية وسهلة الاستخدام
- **استجابة سريعة**: أداء محسن وسرعة في التنقل

## 💻 المتطلبات التقنية

### متطلبات النظام
- **نظام التشغيل**: Windows 10 أو أحدث
- **الذاكرة**: 4 جيجابايت RAM كحد أدنى
- **المساحة**: 2 جيجابايت مساحة فارغة
- **الشبكة**: Wi-Fi أو Ethernet للشبكة المحلية

### المتطلبات البرمجية
- Python 3.8 أو أحدث
- SQLite (مدمج مع Python)
- tkinter (مدمج مع Python)
- bcrypt للأمان
- loguru للسجلات

## 🚀 التثبيت والتشغيل

### الطريقة السريعة (موصى بها):
```bash
# تشغيل الإعداد التلقائي
python run_final.py
```

### الطريقة اليدوية:

#### 1. تحضير البيئة
```bash
# تثبيت Python 3.8 أو أحدث
# إنشاء بيئة افتراضية (اختياري)
python -m venv rafea_env
rafea_env\Scripts\activate
```

#### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### 3. إعداد قاعدة البيانات
```bash
# إعداد قاعدة بيانات SQLite (تلقائي)
python setup_sqlite.py
```

#### 4. تشغيل التطبيق
```bash
# التشغيل الرئيسي
python main_app.py

# أو التشغيل مع فحص شامل
python run_final.py
```

## 📋 بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

⚠️ **تنبيه**: يرجى تغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول.

## 🔧 الإعدادات

### إعدادات قاعدة البيانات
قم بتحديث ملف `config.py` بإعدادات قاعدة البيانات الخاصة بك:

```python
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': '5432',
    'database': 'rafea_real_estate',
    'username': 'postgres',
    'password': 'your_password'
}
```

### إعدادات الشبكة
```python
NETWORK_CONFIG = {
    'server_host': '0.0.0.0',
    'server_port': 8080,
    'max_connections': 50
}
```

## 📞 الدعم والمساعدة

### التوثيق
- دليل المستخدم متوفر في مجلد `docs`
- أمثلة وشروحات في مجلد `examples`

### الدعم التقني
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-XX-XXXXXXX

## 🤝 المساهمة

نرحب بمساهماتكم في تطوير النظام:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🏢 عن الشركة

**شركة رافع للتطوير العقاري** - شركة رائدة في مجال التطوير العقاري، نسعى لتقديم حلول مبتكرة ومتطورة في قطاع العقارات.

---

**تم التطوير بواسطة فريق التطوير في شركة رافع للتطوير العقاري**

© 2024 شركة رافع للتطوير العقاري. جميع الحقوق محفوظة.

---

## 🆕 التحديثات الجديدة - وظائف إدارة الوحدات المطورة

### 🏠 إدارة الوحدات المتقدمة (مطورة بالكامل):
- ✅ **إضافة وحدات جديدة** مع نافذة حوار شاملة
- ✅ **تعديل بيانات الوحدات** الموجودة
- ✅ **حجز الوحدات** للعملاء مع تحديث الحالة
- ✅ **بيع الوحدات** مع تأكيد العملية
- ✅ **حذف الوحدات** مع حماية أمان
- ✅ **عرض تفاصيل شاملة** لكل وحدة
- ✅ **تحديث فوري** للبيانات والإحصائيات

### 🏗️ إدارة المشاريع المطورة:
- ✅ **نوافذ حوار احترافية** لإدخال البيانات
- ✅ **عمليات CRUD كاملة** (إضافة، تعديل، حذف، عرض)
- ✅ **تتبع نسب الإنجاز** والتكاليف
- ✅ **عرض تفاصيل مفصلة** للمشاريع

### 👥 إدارة العملاء المحسنة:
- ✅ **إضافة عملاء جدد** (أفراد وشركات)
- ✅ **تعديل بيانات العملاء** بسهولة
- ✅ **حذف العملاء** مع تأكيد الأمان
- ✅ **تصنيف العملاء** حسب النوع

### 🎨 تحسينات الواجهة:
- ✅ **نوافذ حوار متقدمة** لجميع العمليات
- ✅ **أزرار ملونة تفاعلية** لكل وظيفة
- ✅ **رسائل تأكيد وتحذير** واضحة
- ✅ **تحديث فوري** للجداول والإحصائيات

## 🚀 طرق التشغيل المحدثة:

### التشغيل مع الميزات الجديدة:
```bash
python run_units_system.py
```

### التشغيل العادي:
```bash
python rafea_complete_system.py
```

## 📊 حالة الوحدات:

1. ✅ **لوحة التحكم** - إحصائيات تفاعلية
2. ✅ **إدارة المشاريع** - وظائف كاملة مطورة
3. ✅ **إدارة الوحدات** - نظام متكامل مطور
4. ✅ **إدارة العملاء** - عمليات CRUD كاملة
5. 🔄 **المقاولون والمستخلصات** - جاهز للتطوير
6. 🔄 **الموردون والفواتير** - جاهز للتطوير
7. 🔄 **المشتريات** - جاهز للتطوير
8. 🔄 **الصيانة والتشغيل** - جاهز للتطوير
9. 🔄 **المهام اليومية** - جاهز للتطوير
10. ✅ **التقارير** - نظام متقدم

**النظام الآن مطور بالكامل مع جميع الوظائف الأساسية جاهزة للاستخدام!** 🎉
