2025-06-30 17:19:01 | INFO | database:test_connection:159 | تم اختبار الاتصال بقاعدة البيانات بنجاح
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.units
[SQL: CREATE INDEX IF NOT EXISTS idx_units_project_id ON units(project_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.units
[SQL: CREATE INDEX IF NOT EXISTS idx_units_status ON units(status)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.contracts
[SQL: CREATE INDEX IF NOT EXISTS idx_contracts_customer_id ON contracts(customer_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.contracts
[SQL: CREATE INDEX IF NOT EXISTS idx_contracts_unit_id ON contracts(unit_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.payments
[SQL: CREATE INDEX IF NOT EXISTS idx_payments_contract_id ON payments(contract_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.extracts
[SQL: CREATE INDEX IF NOT EXISTS idx_extracts_contractor_id ON extracts(contractor_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.extracts
[SQL: CREATE INDEX IF NOT EXISTS idx_extracts_project_id ON extracts(project_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.invoices
[SQL: CREATE INDEX IF NOT EXISTS idx_invoices_supplier_id ON invoices(supplier_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.invoices
[SQL: CREATE INDEX IF NOT EXISTS idx_invoices_project_id ON invoices(project_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.maintenance_tasks
[SQL: CREATE INDEX IF NOT EXISTS idx_maintenance_tasks_project_id ON maintenance_tasks(project_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.daily_tasks
[SQL: CREATE INDEX IF NOT EXISTS idx_daily_tasks_assigned_to ON daily_tasks(assigned_to)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.activity_log
[SQL: CREATE INDEX IF NOT EXISTS idx_activity_log_user_id ON activity_log(user_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.activity_log
[SQL: CREATE INDEX IF NOT EXISTS idx_activity_log_created_at ON activity_log(created_at)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:19:01 | INFO | database:create_tables:133 | تم إنشاء جداول قاعدة البيانات بنجاح
2025-06-30 17:19:01 | INFO | database:init_database:241 | تم تهيئة قاعدة البيانات بنجاح
2025-06-30 17:19:01 | ERROR | database:get_session:149 | خطأ في جلسة قاعدة البيانات: Textual SQL expression 'SELECT COUNT(*) FROM user...' should be explicitly declared as text('SELECT COUNT(*) FROM user...')
2025-06-30 17:19:01 | INFO | database:close:228 | تم إغلاق الاتصال بقاعدة البيانات
2025-06-30 17:20:07 | INFO | database:test_connection:159 | تم اختبار الاتصال بقاعدة البيانات بنجاح
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.units
[SQL: CREATE INDEX IF NOT EXISTS idx_units_project_id ON units(project_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.units
[SQL: CREATE INDEX IF NOT EXISTS idx_units_status ON units(status)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.contracts
[SQL: CREATE INDEX IF NOT EXISTS idx_contracts_customer_id ON contracts(customer_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.contracts
[SQL: CREATE INDEX IF NOT EXISTS idx_contracts_unit_id ON contracts(unit_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.payments
[SQL: CREATE INDEX IF NOT EXISTS idx_payments_contract_id ON payments(contract_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.extracts
[SQL: CREATE INDEX IF NOT EXISTS idx_extracts_contractor_id ON extracts(contractor_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.extracts
[SQL: CREATE INDEX IF NOT EXISTS idx_extracts_project_id ON extracts(project_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.invoices
[SQL: CREATE INDEX IF NOT EXISTS idx_invoices_supplier_id ON invoices(supplier_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.invoices
[SQL: CREATE INDEX IF NOT EXISTS idx_invoices_project_id ON invoices(project_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.maintenance_tasks
[SQL: CREATE INDEX IF NOT EXISTS idx_maintenance_tasks_project_id ON maintenance_tasks(project_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.daily_tasks
[SQL: CREATE INDEX IF NOT EXISTS idx_daily_tasks_assigned_to ON daily_tasks(assigned_to)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.activity_log
[SQL: CREATE INDEX IF NOT EXISTS idx_activity_log_user_id ON activity_log(user_id)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | WARNING | database:create_tables:131 | تحذير في تنفيذ الاستعلام: (sqlite3.OperationalError) no such table: main.activity_log
[SQL: CREATE INDEX IF NOT EXISTS idx_activity_log_created_at ON activity_log(created_at)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | INFO | database:create_tables:133 | تم إنشاء جداول قاعدة البيانات بنجاح
2025-06-30 17:20:07 | INFO | database:init_database:241 | تم تهيئة قاعدة البيانات بنجاح
2025-06-30 17:20:07 | ERROR | database:get_session:149 | خطأ في جلسة قاعدة البيانات: (sqlite3.OperationalError) no such table: users
[SQL: SELECT COUNT(*) FROM users WHERE username = 'admin']
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-06-30 17:20:07 | INFO | database:close:228 | تم إغلاق الاتصال بقاعدة البيانات
2025-06-30 17:34:27 | INFO | database_sqlite:create_tables:64 | تم إنشاء جداول قاعدة البيانات بنجاح
2025-06-30 17:34:28 | INFO | database_sqlite:create_admin_user:155 | تم إنشاء المستخدم الإداري الافتراضي
2025-06-30 17:34:28 | INFO | database_sqlite:create_sample_data:214 | تم إنشاء البيانات التجريبية بنجاح
2025-06-30 17:34:28 | INFO | database_sqlite:init_sqlite_database:260 | تم تهيئة قاعدة بيانات SQLite بنجاح
