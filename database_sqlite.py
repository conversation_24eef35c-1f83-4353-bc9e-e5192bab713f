# -*- coding: utf-8 -*-
"""
إدارة قاعدة بيانات SQLite لتطبيق شركة رافع للتطوير العقاري
SQLite Database Management for Rafea Real Estate Development Application
"""

import sqlite3
import os
import bcrypt
from pathlib import Path
from contextlib import contextmanager
from loguru import logger

from config import DATA_DIR

class SQLiteManager:
    """مدير قاعدة بيانات SQLite"""
    
    def __init__(self):
        self.db_path = DATA_DIR / 'rafea_real_estate.db'
        self.ensure_database_exists()
    
    def ensure_database_exists(self):
        """التأكد من وجود قاعدة البيانات"""
        if not self.db_path.exists():
            logger.info("إنشاء قاعدة بيانات SQLite جديدة")
            self.db_path.touch()
    
    @contextmanager
    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات مع تعطيل المفاتيح الخارجية"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            # تعطيل المفاتيح الخارجية نهائياً
            conn.execute("PRAGMA foreign_keys = OFF")
            return conn
        except Exception as e:
            logger.error(f"خطأ في قاعدة البيانات: {e}")
            return None
        """الحصول على اتصال قاعدة البيانات"""
        conn = None
        try:
            conn = sqlite3.connect(str(self.db_path))
            conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
            conn.execute("PRAGMA foreign_keys = OFF")  # تعطيل المفاتيح الخارجية
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"خطأ في قاعدة البيانات: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            sql_file_path = Path(__file__).parent / 'database_design_sqlite.sql'
            
            if not sql_file_path.exists():
                logger.error("ملف تصميم قاعدة البيانات غير موجود")
                return False
            
            with open(sql_file_path, 'r', encoding='utf-8') as file:
                sql_content = file.read()
            
            with self.get_connection() as conn:
                # تنفيذ الاستعلامات
                conn.executescript(sql_content)
                conn.commit()
            
            logger.info("تم إنشاء جداول قاعدة البيانات بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء جداول قاعدة البيانات: {e}")
            return False
    
    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.execute("SELECT 1")
            result = cursor.fetchone()
            conn.close()
            return result is not None
        except Exception as e:
            logger.error(f"فشل في اختبار الاتصال: {e}")
            return False
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام وإرجاع النتائج"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            conn.execute("PRAGMA foreign_keys = OFF")
            cursor = conn.execute(query, params or ())
            if query.strip().upper().startswith('SELECT'):
                result = cursor.fetchall()
                conn.close()
                return result
            else:
                conn.commit()
                rowcount = cursor.rowcount
                conn.close()
                return rowcount
        except Exception as e:
            if conn:
                conn.close()
            logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise
    
    def execute_many(self, query, params_list):
        """تنفيذ استعلام متعدد"""
        try:
            with self.get_connection() as conn:
                cursor = conn.executemany(query, params_list)
                conn.commit()
                return cursor.rowcount
        except Exception as e:
            logger.error(f"خطأ في تنفيذ الاستعلام المتعدد: {e}")
            raise
    
    def get_table_info(self, table_name):
        """الحصول على معلومات جدول"""
        try:
            query = f"PRAGMA table_info({table_name})"
            return self.execute_query(query)
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات الجدول {table_name}: {e}")
            return []
    
    def table_exists(self, table_name):
        """التحقق من وجود جدول"""
        try:
            query = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
            result = self.execute_query(query, (table_name,))
            return len(result) > 0
        except Exception as e:
            logger.error(f"خطأ في التحقق من وجود الجدول {table_name}: {e}")
            return False
    
    def get_all_tables(self):
        """الحصول على قائمة بجميع الجداول"""
        try:
            query = "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
            result = self.execute_query(query)
            return [row['name'] for row in result]
        except Exception as e:
            logger.error(f"خطأ في الحصول على قائمة الجداول: {e}")
            return []
    
    def create_admin_user(self):
        """إنشاء المستخدم الإداري الافتراضي"""
        try:
            # التحقق من وجود المستخدم الإداري
            query = "SELECT COUNT(*) as count FROM users WHERE username = ?"
            result = self.execute_query(query, ('admin',))
            
            if result[0]['count'] == 0:
                # تشفير كلمة المرور
                password = "admin123"
                password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                
                # إدراج المستخدم الإداري
                insert_query = """
                    INSERT INTO users (username, password_hash, full_name, user_type, is_active)
                    VALUES (?, ?, ?, ?, ?)
                """
                self.execute_query(insert_query, ('admin', password_hash, 'مدير النظام', 'admin', 1))
                
                logger.info("تم إنشاء المستخدم الإداري الافتراضي")
                return True
            else:
                logger.info("المستخدم الإداري موجود بالفعل")
                return True
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء المستخدم الإداري: {e}")
            return False
    
    def create_sample_data(self):
        """إنشاء بيانات تجريبية"""
        try:
            # التحقق من وجود بيانات
            query = "SELECT COUNT(*) as count FROM projects"
            result = self.execute_query(query)
            
            if result[0]['count'] == 0:
                # إضافة مشاريع تجريبية
                projects_data = [
                    ('مشروع الواحة السكني', 'الرياض - حي النرجس', 'سكني', 5000000.0, 75.0, '2024-01-15', '2025-06-30', 'in_progress', 'مشروع سكني متكامل يضم 100 وحدة سكنية', 1),
                    ('برج الأعمال التجاري', 'جدة - الكورنيش', 'تجاري', 8000000.0, 100.0, '2023-03-01', '2024-12-31', 'completed', 'برج تجاري بـ 20 طابق', 1),
                    ('مجمع الفلل الراقية', 'الدمام - الشاطئ', 'سكني', 12000000.0, 25.0, '2024-06-01', '2026-12-31', 'planning', 'مجمع فلل فاخرة على البحر', 1)
                ]
                
                insert_project_query = """
                    INSERT INTO projects (name, location, project_type, total_cost, completion_percentage, 
                                        start_date, expected_end_date, status, description, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                self.execute_many(insert_project_query, projects_data)
                
                # إضافة وحدات تجريبية للمشروع الأول
                units_data = []
                for i in range(1, 21):  # 20 وحدة
                    floor = (i - 1) // 4 + 1
                    unit_number = f"A{i:02d}"
                    status = 'sold' if i <= 12 else 'available'
                    units_data.append((1, unit_number, floor, 'شقة', 120.5, 450000.0, status, f'شقة {i} - الطابق {floor}'))
                
                insert_unit_query = """
                    INSERT INTO units (project_id, unit_number, floor_number, unit_type, area, price, status, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                self.execute_many(insert_unit_query, units_data)
                
                # إضافة عملاء تجريبيين
                customers_data = [
                    ('أحمد محمد السعيد', '1234567890', '0501234567', '<EMAIL>', 'الرياض - حي الملز', 'individual', 'عميل مميز'),
                    ('شركة الاستثمار العقاري', '9876543210', '0509876543', '<EMAIL>', 'جدة - حي الزهراء', 'company', 'شركة استثمارية'),
                    ('فاطمة علي الأحمد', '5555555555', '0555555555', '<EMAIL>', 'الدمام - الكورنيش', 'individual', 'عميلة جديدة')
                ]
                
                insert_customer_query = """
                    INSERT INTO customers (full_name, national_id, phone, email, address, customer_type, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                self.execute_many(insert_customer_query, customers_data)
                
                logger.info("تم إنشاء البيانات التجريبية بنجاح")
                return True
            else:
                logger.info("البيانات التجريبية موجودة بالفعل")
                return True
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء البيانات التجريبية: {e}")
            return False
    
    def backup_database(self, backup_path=None):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            if not backup_path:
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = DATA_DIR / f"backup_{timestamp}.db"
            
            import shutil
            shutil.copy2(self.db_path, backup_path)
            
            logger.info(f"تم إنشاء النسخة الاحتياطية: {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return None

# إنشاء مثيل مدير قاعدة البيانات
sqlite_manager = SQLiteManager()

def init_sqlite_database():
    """تهيئة قاعدة بيانات SQLite"""
    try:
        # إنشاء الجداول
        if not sqlite_manager.create_tables():
            return False
        
        # إنشاء المستخدم الإداري
        if not sqlite_manager.create_admin_user():
            return False
        
        # إنشاء البيانات التجريبية
        if not sqlite_manager.create_sample_data():
            return False
        
        logger.info("تم تهيئة قاعدة بيانات SQLite بنجاح")
        return True
        
    except Exception as e:
        logger.error(f"خطأ في تهيئة قاعدة بيانات SQLite: {e}")
        return False

if __name__ == "__main__":
    # اختبار قاعدة البيانات
    if sqlite_manager.test_connection():
        print("✅ الاتصال بقاعدة البيانات ناجح")
        if init_sqlite_database():
            print("✅ تم تهيئة قاعدة البيانات بنجاح")
        else:
            print("❌ فشل في تهيئة قاعدة البيانات")
    else:
        print("❌ فشل في الاتصال بقاعدة البيانات")
