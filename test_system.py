# -*- coding: utf-8 -*-
"""
اختبار النظام
System Test
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """اختبار استيراد المكتبات"""
    print("🔍 اختبار استيراد المكتبات...")
    
    try:
        import PyQt5
        print("✅ PyQt5")
    except ImportError:
        print("❌ PyQt5 - غير مثبت")
        return False
    
    try:
        import psycopg2
        print("✅ psycopg2")
    except ImportError:
        print("❌ psycopg2 - غير مثبت")
        return False
    
    try:
        import bcrypt
        print("✅ bcrypt")
    except ImportError:
        print("❌ bcrypt - غير مثبت")
        return False
    
    try:
        import loguru
        print("✅ loguru")
    except ImportError:
        print("❌ loguru - غير مثبت")
        return False
    
    try:
        import reportlab
        print("✅ reportlab")
    except ImportError:
        print("❌ reportlab - غير مثبت")
        return False
    
    return True

def test_config():
    """اختبار ملف التكوين"""
    print("\n🔍 اختبار ملف التكوين...")
    
    try:
        from config import DATABASE_CONFIG, APP_CONFIG, UI_CONFIG
        print("✅ تم تحميل ملف التكوين")
        
        # التحقق من الإعدادات الأساسية
        required_db_keys = ['host', 'port', 'database', 'username', 'password']
        for key in required_db_keys:
            if key not in DATABASE_CONFIG:
                print(f"❌ مفتاح مفقود في إعدادات قاعدة البيانات: {key}")
                return False
        
        print("✅ إعدادات قاعدة البيانات صحيحة")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في تحميل ملف التكوين: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n🔍 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        from database import db_manager
        
        if db_manager.test_connection():
            print("✅ تم الاتصال بقاعدة البيانات بنجاح")
            return True
        else:
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_database_tables():
    """اختبار وجود جداول قاعدة البيانات"""
    print("\n🔍 اختبار جداول قاعدة البيانات...")
    
    try:
        from database import get_db_session
        
        required_tables = [
            'users', 'projects', 'units', 'customers', 'contracts',
            'payments', 'contractors', 'extracts', 'suppliers',
            'invoices', 'purchase_requests', 'maintenance_tasks',
            'daily_tasks', 'activity_log'
        ]
        
        with get_db_session() as session:
            for table in required_tables:
                result = session.execute(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = '{table}'
                    )
                """)
                
                if result.fetchone()[0]:
                    print(f"✅ جدول {table}")
                else:
                    print(f"❌ جدول {table} - غير موجود")
                    return False
        
        print("✅ جميع الجداول موجودة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الجداول: {e}")
        return False

def test_admin_user():
    """اختبار وجود المستخدم الإداري"""
    print("\n🔍 اختبار المستخدم الإداري...")
    
    try:
        from database import get_db_session
        
        with get_db_session() as session:
            result = session.execute("""
                SELECT username, user_type, is_active 
                FROM users 
                WHERE username = 'admin'
            """)
            
            user = result.fetchone()
            if user:
                print(f"✅ المستخدم الإداري موجود: {user[0]} ({user[1]})")
                if user[2]:
                    print("✅ المستخدم الإداري مفعل")
                    return True
                else:
                    print("❌ المستخدم الإداري غير مفعل")
                    return False
            else:
                print("❌ المستخدم الإداري غير موجود")
                return False
                
    except Exception as e:
        print(f"❌ خطأ في اختبار المستخدم الإداري: {e}")
        return False

def test_ui_components():
    """اختبار مكونات واجهة المستخدم"""
    print("\n🔍 اختبار مكونات واجهة المستخدم...")
    
    try:
        from ui.login_window import LoginWindow
        from ui.main_window import MainWindow
        from ui.dashboard_widget import DashboardWidget
        from ui.projects_widget import ProjectsWidget
        
        print("✅ تم تحميل مكونات واجهة المستخدم")
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في تحميل مكونات واجهة المستخدم: {e}")
        return False

def test_directories():
    """اختبار وجود المجلدات المطلوبة"""
    print("\n🔍 اختبار المجلدات المطلوبة...")
    
    required_dirs = ['data', 'logs', 'reports', 'assets', 'ui', 'utils']
    
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if dir_path.exists():
            print(f"✅ مجلد {dir_name}")
        else:
            print(f"❌ مجلد {dir_name} - غير موجود")
            # إنشاء المجلد إذا لم يكن موجوداً
            dir_path.mkdir(exist_ok=True)
            print(f"✅ تم إنشاء مجلد {dir_name}")
    
    return True

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("اختبار نظام شركة رافع للتطوير العقاري")
    print("Rafea Real Estate System Test")
    print("=" * 60)
    
    tests = [
        ("استيراد المكتبات", test_imports),
        ("ملف التكوين", test_config),
        ("المجلدات المطلوبة", test_directories),
        ("الاتصال بقاعدة البيانات", test_database_connection),
        ("جداول قاعدة البيانات", test_database_tables),
        ("المستخدم الإداري", test_admin_user),
        ("مكونات واجهة المستخدم", test_ui_components),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"⚠️  فشل اختبار: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        print("\nيمكنك الآن تشغيل التطبيق باستخدام:")
        print("   python run.py")
        print("   أو تشغيل ملف start.bat")
        return 0
    else:
        print("⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        print("\nللمساعدة:")
        print("1. راجع دليل التثبيت INSTALLATION_GUIDE.md")
        print("2. تأكد من تثبيت جميع المتطلبات")
        print("3. تحقق من إعدادات قاعدة البيانات")
        return 1

if __name__ == "__main__":
    sys.exit(main())
