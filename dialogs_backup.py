# -*- coding: utf-8 -*-
"""
نوافذ الحوار لتطبيق شركة رافع للتطوير العقاري
Dialog Windows for Rafea Real Estate Development Application
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date

class ProjectDialog:
    """نافذة حوار إضافة/تعديل مشروع"""
    
    def __init__(self, parent, title, project_data=None):
        self.parent = parent
        self.project_data = project_data
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x650")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"500x650+{x}+{y}")
        
        self.create_widgets()
        
        if project_data:
            self.load_project_data()
    
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # إطار المحتوى (للحقول)
        content_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        content_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات المشروع",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))
        
        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # اسم المشروع
        tk.Label(content_frame, text="اسم المشروع:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.name_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.name_entry.pack(pady=(0, 15))
        
        # الموقع
        tk.Label(content_frame, text="الموقع:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.location_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.location_entry.pack(pady=(0, 15))
        
        # نوع المشروع
        tk.Label(content_frame, text="نوع المشروع:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.type_combo['values'] = ('سكني', 'تجاري', 'إداري', 'مختلط', 'صناعي')
        self.type_combo.pack(pady=(0, 15))
        
        # التكلفة الإجمالية
        tk.Label(content_frame, text="التكلفة الإجمالية (ريال):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.cost_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.cost_entry.pack(pady=(0, 15))
        
        # نسبة الإنجاز
        tk.Label(content_frame, text="نسبة الإنجاز (%):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.completion_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.completion_entry.pack(pady=(0, 15))
        
        # تاريخ البداية
        tk.Label(content_frame, text="تاريخ البداية (YYYY-MM-DD):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.start_date_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.start_date_entry.pack(pady=(0, 15))
        
        # تاريخ الانتهاء المتوقع
        tk.Label(content_frame, text="تاريخ الانتهاء المتوقع (YYYY-MM-DD):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.end_date_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.end_date_entry.pack(pady=(0, 15))
        
        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.status_combo['values'] = ('تخطيط', 'قيد التنفيذ', 'مكتمل', 'متوقف مؤقتاً')
        self.status_combo.pack(pady=(0, 15))
        
        # الوصف
        tk.Label(content_frame, text="الوصف:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.description_text = tk.Text(content_frame, font=('Arial', 11), width=40, height=4)
        self.description_text.pack(pady=(0, 15))
        
        # أزرار التحكم - محسنة
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5', height=80)
        buttons_frame.pack(fill='x', side='bottom', pady=20, padx=20)
        buttons_frame.pack_propagate(False)  # منع تقليص الإطار

        # زر الإلغاء (يسار)
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=15,
            height=2,
            relief='raised',
            bd=3,
            command=self.cancel
        )
        cancel_btn.pack(side='left', padx=10, pady=10)
        # اختبار الأزرار
        def test_buttons():
            print(f"🔍 اختبار الأزرار في النافذة")
            print(f"📍 إطار الأزرار: {buttons_frame.winfo_exists()}")
            print(f"📍 زر الحفظ: {save_btn.winfo_exists()}")
            print(f"📍 زر الإلغاء: {cancel_btn.winfo_exists()}")
            
        self.dialog.after(100, test_buttons)

        # زر الحفظ (يمين)
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=15,
            height=2,
            relief='raised',
            bd=3,
            command=self.save_project
        )
        save_btn.pack(side='right', padx=10, pady=10)
        
        # التأكد من ظهور الأزرار
        buttons_frame.update()
        save_btn.update()
        cancel_btn.update()        

        
        # تعيين القيم الافتراضية
        if not self.project_data:
            self.start_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
            self.end_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
            self.completion_entry.insert(0, "0")
            self.type_combo.set("سكني")
            self.status_combo.set("تخطيط")
    
    def load_project_data(self):
        """تحميل بيانات المشروع للتعديل"""
        if self.project_data:
            self.name_entry.insert(0, self.project_data.get('name', ''))
            self.location_entry.insert(0, self.project_data.get('location', ''))
            self.type_combo.set(self.project_data.get('project_type', 'سكني'))
            self.cost_entry.insert(0, str(self.project_data.get('total_cost', '')))
            self.completion_entry.insert(0, str(self.project_data.get('completion_percentage', '0')))
            self.start_date_entry.insert(0, str(self.project_data.get('start_date', '')))
            self.end_date_entry.insert(0, str(self.project_data.get('expected_end_date', '')))
            
            # تحويل الحالة
            status_map = {
                'planning': 'تخطيط',
                'in_progress': 'قيد التنفيذ',
                'completed': 'مكتمل',
                'on_hold': 'متوقف مؤقتاً'
            }
            status_text = status_map.get(self.project_data.get('status', 'planning'), 'تخطيط')
            self.status_combo.set(status_text)
            
            self.description_text.insert('1.0', self.project_data.get('description', ''))
    
    def save_project(self):
        """حفظ بيانات المشروع"""
        # التحقق من صحة البيانات
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المشروع")
            return
        
        try:
            # تحويل البيانات
            cost = float(self.cost_entry.get()) if self.cost_entry.get().strip() else 0.0
            completion = float(self.completion_entry.get()) if self.completion_entry.get().strip() else 0.0
            
            # تحويل الحالة
            status_map = {
                'تخطيط': 'planning',
                'قيد التنفيذ': 'in_progress',
                'مكتمل': 'completed',
                'متوقف مؤقتاً': 'on_hold'
            }
            status = status_map.get(self.status_combo.get(), 'planning')
            
            # إعداد النتيجة
            self.result = {
                'name': self.name_entry.get().strip(),
                'location': self.location_entry.get().strip(),
                'project_type': self.type_combo.get(),
                'total_cost': cost,
                'completion_percentage': completion,
                'start_date': self.start_date_entry.get().strip() or None,
                'expected_end_date': self.end_date_entry.get().strip() or None,
                'status': status,
                'description': self.description_text.get('1.0', 'end-1c').strip()
            }
            
            self.dialog.destroy()
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للتكلفة ونسبة الإنجاز")
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()
    
    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result

class CustomerDialog:
    """نافذة حوار إضافة/تعديل عميل"""
    
    def __init__(self, parent, title, customer_data=None):
        self.parent = parent
        self.customer_data = customer_data
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x550")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (550 // 2)
        self.dialog.geometry(f"450x550+{x}+{y}")
        
        self.create_widgets()
        
        if customer_data:
            self.load_customer_data()
    
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات العميل",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))
        
        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # الاسم الكامل
        tk.Label(content_frame, text="الاسم الكامل:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.name_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.name_entry.pack(pady=(0, 15))
        
        # رقم الهوية
        tk.Label(content_frame, text="رقم الهوية:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.national_id_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.national_id_entry.pack(pady=(0, 15))
        
        # رقم الهاتف
        tk.Label(content_frame, text="رقم الهاتف:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.phone_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.phone_entry.pack(pady=(0, 15))
        
        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.email_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.email_entry.pack(pady=(0, 15))
        
        # العنوان
        tk.Label(content_frame, text="العنوان:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.address_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.address_entry.pack(pady=(0, 15))
        
        # نوع العميل
        tk.Label(content_frame, text="نوع العميل:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.type_combo['values'] = ('فرد', 'شركة')
        self.type_combo.set('فرد')
        self.type_combo.pack(pady=(0, 15))
        
        # ملاحظات
        tk.Label(content_frame, text="ملاحظات:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.notes_text = tk.Text(content_frame, font=('Arial', 11), width=35, height=4)
        self.notes_text.pack(pady=(0, 15))
        
        # أزرار التحكم - محسنة
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5', height=80)
        buttons_frame.pack(fill='x', side='bottom', pady=20, padx=20)
        buttons_frame.pack_propagate(False)  # منع تقليص الإطار

        # زر الإلغاء (يسار)
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=15,
            height=2,
            relief='raised',
            bd=3,
            command=self.cancel
        )
        cancel_btn.pack(side='left', padx=10, pady=10)
        # اختبار الأزرار
        def test_buttons():
            print(f"🔍 اختبار الأزرار في النافذة")
            print(f"📍 إطار الأزرار: {buttons_frame.winfo_exists()}")
            print(f"📍 زر الحفظ: {save_btn.winfo_exists()}")
            print(f"📍 زر الإلغاء: {cancel_btn.winfo_exists()}")
            
        self.dialog.after(100, test_buttons)

        # زر الحفظ (يمين)
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=15,
            height=2,
            relief='raised',
            bd=3,
            command=self.save_customer
        )
        save_btn.pack(side='right', padx=10, pady=10)
        
        # التأكد من ظهور الأزرار
        buttons_frame.update()
        save_btn.update()
        cancel_btn.update()        

    
    def load_customer_data(self):
        """تحميل بيانات العميل للتعديل"""
        if self.customer_data:
            self.name_entry.insert(0, self.customer_data.get('full_name', ''))
            self.national_id_entry.insert(0, self.customer_data.get('national_id', ''))
            self.phone_entry.insert(0, self.customer_data.get('phone', ''))
            self.email_entry.insert(0, self.customer_data.get('email', ''))
            self.address_entry.insert(0, self.customer_data.get('address', ''))
            
            # تحويل نوع العميل
            customer_type = 'شركة' if self.customer_data.get('customer_type') == 'company' else 'فرد'
            self.type_combo.set(customer_type)
            
            self.notes_text.insert('1.0', self.customer_data.get('notes', ''))
    
    def save_customer(self):
        """حفظ بيانات العميل"""
        # التحقق من صحة البيانات
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
            return
        
        # تحويل نوع العميل
        customer_type = 'company' if self.type_combo.get() == 'شركة' else 'individual'
        
        # إعداد النتيجة
        self.result = {
            'full_name': self.name_entry.get().strip(),
            'national_id': self.national_id_entry.get().strip(),
            'phone': self.phone_entry.get().strip(),
            'email': self.email_entry.get().strip(),
            'address': self.address_entry.get().strip(),
            'customer_type': customer_type,
            'notes': self.notes_text.get('1.0', 'end-1c').strip()
        }
        
        self.dialog.destroy()
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()
    
    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result

class UnitDialog:
    """نافذة حوار إضافة/تعديل وحدة"""

    def __init__(self, parent, title, unit_data=None):
        self.parent = parent
        self.unit_data = unit_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x650")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (650 // 2)
        self.dialog.geometry(f"500x650+{x}+{y}")

        self.create_widgets()

        if unit_data:
            self.load_unit_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات الوحدة",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))

        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))

        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # رقم الوحدة
        tk.Label(content_frame, text="رقم الوحدة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.unit_number_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.unit_number_entry.pack(pady=(0, 15))

        # المشروع
        tk.Label(content_frame, text="المشروع:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.project_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.project_combo['values'] = ('مشروع الواحة السكني', 'برج الأعمال التجاري', 'مجمع الفلل الراقية')
        self.project_combo.pack(pady=(0, 15))

        # رقم الطابق
        tk.Label(content_frame, text="رقم الطابق:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.floor_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.floor_entry.pack(pady=(0, 15))

        # نوع الوحدة
        tk.Label(content_frame, text="نوع الوحدة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.unit_type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.unit_type_combo['values'] = ('شقة', 'فيلا', 'دوبلكس', 'استوديو', 'مكتب', 'محل تجاري')
        self.unit_type_combo.pack(pady=(0, 15))

        # المساحة
        tk.Label(content_frame, text="المساحة (متر مربع):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.area_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.area_entry.pack(pady=(0, 15))

        # السعر
        tk.Label(content_frame, text="السعر (ريال):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.price_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.price_entry.pack(pady=(0, 15))

        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.status_combo['values'] = ('متاح', 'محجوز', 'مباع', 'تحت الصيانة')
        self.status_combo.set('متاح')
        self.status_combo.pack(pady=(0, 15))

        # الوصف
        tk.Label(content_frame, text="الوصف:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.description_text = tk.Text(content_frame, font=('Arial', 11), width=35, height=4)
        self.description_text.pack(pady=(0, 15))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x', pady=(15, 10))
        
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=15,
            height=2,
            command=self.save_unit
        )
        save_btn.pack(side='right', padx=10, pady=10)
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=15,
            height=2,
            command=self.cancel
        )
        cancel_btn.pack(side='left', padx=10, pady=10)
        # اختبار الأزرار
        def test_buttons():
            print(f"🔍 اختبار الأزرار في النافذة")
            print(f"📍 إطار الأزرار: {buttons_frame.winfo_exists()}")
            print(f"📍 زر الحفظ: {save_btn.winfo_exists()}")
            print(f"📍 زر الإلغاء: {cancel_btn.winfo_exists()}")
            
        self.dialog.after(100, test_buttons)        


        # تعيين القيم الافتراضية
        if not self.unit_data:
            self.project_combo.set('مشروع الواحة السكني')
            self.unit_type_combo.set('شقة')
            self.floor_entry.insert(0, "1")
            self.area_entry.insert(0, "120")
            self.price_entry.insert(0, "450000")

    def load_unit_data(self):
        """تحميل بيانات الوحدة للتعديل"""
        if self.unit_data:
            self.unit_number_entry.insert(0, self.unit_data.get('unit_number', ''))
            self.floor_entry.insert(0, str(self.unit_data.get('floor_number', '')))
            self.unit_type_combo.set(self.unit_data.get('unit_type', 'شقة'))
            self.area_entry.insert(0, str(self.unit_data.get('area', '')))
            self.price_entry.insert(0, str(self.unit_data.get('price', '')))

            # تحويل الحالة
            status_map = {
                'available': 'متاح',
                'reserved': 'محجوز',
                'sold': 'مباع',
                'maintenance': 'تحت الصيانة'
            }
            status_text = status_map.get(self.unit_data.get('status', 'available'), 'متاح')
            self.status_combo.set(status_text)

            self.description_text.insert('1.0', self.unit_data.get('description', ''))

    def save_unit(self):
        """حفظ بيانات الوحدة"""
        # التحقق من صحة البيانات
        if not self.unit_number_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال رقم الوحدة")
            return

        try:
            # تحويل البيانات
            area = float(self.area_entry.get()) if self.area_entry.get().strip() else 0.0
            price = float(self.price_entry.get()) if self.price_entry.get().strip() else 0.0
            floor_number = int(self.floor_entry.get()) if self.floor_entry.get().strip() else 1

            # تحويل الحالة
            status_map = {
                'متاح': 'available',
                'محجوز': 'reserved',
                'مباع': 'sold',
                'تحت الصيانة': 'maintenance'
            }
            status = status_map.get(self.status_combo.get(), 'available')

            # إعداد النتيجة
            self.result = {
                'unit_number': self.unit_number_entry.get().strip(),
                'project_id': 1,  # سيتم تحديثه لاحقاً حسب المشروع المختار
                'floor_number': floor_number,
                'unit_type': self.unit_type_combo.get(),
                'area': area,
                'price': price,
                'status': status,
                'description': self.description_text.get('1.0', 'end-1c').strip()
            }

            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للمساحة والسعر ورقم الطابق")

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result

class ContractorDialog:
    """نافذة حوار إضافة/تعديل مقاول"""

    def __init__(self, parent, title, contractor_data=None):
        self.parent = parent
        self.contractor_data = contractor_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x580")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"450x580+{x}+{y}")

        self.create_widgets()

        if contractor_data:
            self.load_contractor_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات المقاول",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))

        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))

        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # اسم المقاول
        tk.Label(content_frame, text="اسم المقاول:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.name_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.name_entry.pack(pady=(0, 15))

        # التخصص
        tk.Label(content_frame, text="التخصص:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.specialty_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.specialty_combo['values'] = ('مقاولات عامة', 'كهرباء', 'سباكة', 'تكييف', 'دهانات', 'بلاط وسيراميك', 'نجارة', 'حدادة')
        self.specialty_combo.pack(pady=(0, 15))

        # رقم الهاتف
        tk.Label(content_frame, text="رقم الهاتف:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.phone_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.phone_entry.pack(pady=(0, 15))

        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.email_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.email_entry.pack(pady=(0, 15))

        # العنوان
        tk.Label(content_frame, text="العنوان:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.address_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.address_entry.pack(pady=(0, 15))

        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.status_combo['values'] = ('نشط', 'غير نشط', 'معلق')
        self.status_combo.set('نشط')
        self.status_combo.pack(pady=(0, 15))

        # ملاحظات
        tk.Label(content_frame, text="ملاحظات:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.notes_text = tk.Text(content_frame, font=('Arial', 11), width=35, height=4)
        self.notes_text.pack(pady=(0, 15))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x', pady=(15, 10))
        
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=15,
            height=2,
            command=self.save_contractor
        )
        save_btn.pack(side='right', padx=10, pady=10)
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=15,
            height=2,
            command=self.cancel
        )
        cancel_btn.pack(side='left', padx=10, pady=10)
        # اختبار الأزرار
        def test_buttons():
            print(f"🔍 اختبار الأزرار في النافذة")
            print(f"📍 إطار الأزرار: {buttons_frame.winfo_exists()}")
            print(f"📍 زر الحفظ: {save_btn.winfo_exists()}")
            print(f"📍 زر الإلغاء: {cancel_btn.winfo_exists()}")
            
        self.dialog.after(100, test_buttons)        


        # تعيين القيم الافتراضية
        if not self.contractor_data:
            self.specialty_combo.set('مقاولات عامة')

    def load_contractor_data(self):
        """تحميل بيانات المقاول للتعديل"""
        if self.contractor_data:
            self.name_entry.insert(0, self.contractor_data.get('name', ''))
            self.specialty_combo.set(self.contractor_data.get('specialty', 'مقاولات عامة'))
            self.phone_entry.insert(0, self.contractor_data.get('phone', ''))
            self.email_entry.insert(0, self.contractor_data.get('email', ''))
            self.address_entry.insert(0, self.contractor_data.get('address', ''))

            # تحويل الحالة
            status_map = {
                'active': 'نشط',
                'inactive': 'غير نشط',
                'suspended': 'معلق'
            }
            status_text = status_map.get(self.contractor_data.get('status', 'active'), 'نشط')
            self.status_combo.set(status_text)

            self.notes_text.insert('1.0', self.contractor_data.get('notes', ''))

    def save_contractor(self):
        """حفظ بيانات المقاول"""
        # التحقق من صحة البيانات
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المقاول")
            return

        # تحويل الحالة
        status_map = {
            'نشط': 'active',
            'غير نشط': 'inactive',
            'معلق': 'suspended'
        }
        status = status_map.get(self.status_combo.get(), 'active')

        # إعداد النتيجة
        self.result = {
            'name': self.name_entry.get().strip(),
            'specialty': self.specialty_combo.get(),
            'phone': self.phone_entry.get().strip(),
            'email': self.email_entry.get().strip(),
            'address': self.address_entry.get().strip(),
            'status': status,
            'notes': self.notes_text.get('1.0', 'end-1c').strip()
        }

        self.dialog.destroy()

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result

class SupplierDialog:
    """نافذة حوار إضافة/تعديل مورد"""

    def __init__(self, parent, title, supplier_data=None):
        self.parent = parent
        self.supplier_data = supplier_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x580")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"450x580+{x}+{y}")

        self.create_widgets()

        if supplier_data:
            self.load_supplier_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات المورد",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))

        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))

        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # اسم المورد
        tk.Label(content_frame, text="اسم المورد:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.name_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.name_entry.pack(pady=(0, 15))

        # نوع المواد
        tk.Label(content_frame, text="نوع المواد:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.material_type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.material_type_combo['values'] = ('حديد وصلب', 'أسمنت ومواد بناء', 'كهربائيات', 'سباكة', 'دهانات', 'بلاط وسيراميك', 'أخشاب', 'مواد عزل')
        self.material_type_combo.pack(pady=(0, 15))

        # رقم الهاتف
        tk.Label(content_frame, text="رقم الهاتف:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.phone_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.phone_entry.pack(pady=(0, 15))

        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.email_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.email_entry.pack(pady=(0, 15))

        # العنوان
        tk.Label(content_frame, text="العنوان:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.address_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.address_entry.pack(pady=(0, 15))

        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.status_combo['values'] = ('نشط', 'غير نشط', 'معلق')
        self.status_combo.set('نشط')
        self.status_combo.pack(pady=(0, 15))

        # ملاحظات
        tk.Label(content_frame, text="ملاحظات:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.notes_text = tk.Text(content_frame, font=('Arial', 11), width=35, height=4)
        self.notes_text.pack(pady=(0, 15))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x', pady=(15, 10))
        
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=15,
            height=2,
            command=self.save_supplier
        )
        save_btn.pack(side='right', padx=10, pady=10)
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=15,
            height=2,
            command=self.cancel
        )
        cancel_btn.pack(side='left', padx=10, pady=10)
        # اختبار الأزرار
        def test_buttons():
            print(f"🔍 اختبار الأزرار في النافذة")
            print(f"📍 إطار الأزرار: {buttons_frame.winfo_exists()}")
            print(f"📍 زر الحفظ: {save_btn.winfo_exists()}")
            print(f"📍 زر الإلغاء: {cancel_btn.winfo_exists()}")
            
        self.dialog.after(100, test_buttons)        


        # تعيين القيم الافتراضية
        if not self.supplier_data:
            self.material_type_combo.set('حديد وصلب')

    def load_supplier_data(self):
        """تحميل بيانات المورد للتعديل"""
        if self.supplier_data:
            self.name_entry.insert(0, self.supplier_data.get('name', ''))
            self.material_type_combo.set(self.supplier_data.get('material_type', 'حديد وصلب'))
            self.phone_entry.insert(0, self.supplier_data.get('phone', ''))
            self.email_entry.insert(0, self.supplier_data.get('email', ''))
            self.address_entry.insert(0, self.supplier_data.get('address', ''))

            # تحويل الحالة
            status_map = {
                'active': 'نشط',
                'inactive': 'غير نشط',
                'suspended': 'معلق'
            }
            status_text = status_map.get(self.supplier_data.get('status', 'active'), 'نشط')
            self.status_combo.set(status_text)

            self.notes_text.insert('1.0', self.supplier_data.get('notes', ''))

    def save_supplier(self):
        """حفظ بيانات المورد"""
        # التحقق من صحة البيانات
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المورد")
            return

        # تحويل الحالة
        status_map = {
            'نشط': 'active',
            'غير نشط': 'inactive',
            'معلق': 'suspended'
        }
        status = status_map.get(self.status_combo.get(), 'active')

        # إعداد النتيجة
        self.result = {
            'name': self.name_entry.get().strip(),
            'material_type': self.material_type_combo.get(),
            'phone': self.phone_entry.get().strip(),
            'email': self.email_entry.get().strip(),
            'address': self.address_entry.get().strip(),
            'status': status,
            'notes': self.notes_text.get('1.0', 'end-1c').strip()
        }

        self.dialog.destroy()

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result

class ExtractDialog:
    """نافذة حوار إضافة/تعديل مستخلص"""

    def __init__(self, parent, title, extract_data=None):
        self.parent = parent
        self.extract_data = extract_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x650")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (550 // 2)
        self.dialog.geometry(f"500x650+{x}+{y}")

        self.create_widgets()

        if extract_data:
            self.load_extract_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات المستخلص",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))

        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))

        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # رقم المستخلص
        tk.Label(content_frame, text="رقم المستخلص:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.extract_number_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.extract_number_entry.pack(pady=(0, 15))

        # المقاول
        tk.Label(content_frame, text="المقاول:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.contractor_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.contractor_combo['values'] = ('أحمد محمد للمقاولات', 'شركة البناء المتقدم', 'مؤسسة الإنشاءات الحديثة')
        self.contractor_combo.pack(pady=(0, 15))

        # المشروع
        tk.Label(content_frame, text="المشروع:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.project_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.project_combo['values'] = ('مشروع الواحة السكني', 'برج الأعمال التجاري', 'مجمع الفلل الراقية')
        self.project_combo.pack(pady=(0, 15))

        # المبلغ
        tk.Label(content_frame, text="المبلغ (ريال):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.amount_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.amount_entry.pack(pady=(0, 15))

        # تاريخ المستخلص
        tk.Label(content_frame, text="تاريخ المستخلص (YYYY-MM-DD):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.date_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.date_entry.pack(pady=(0, 15))

        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.status_combo['values'] = ('معلق', 'تمت الموافقة', 'مدفوع', 'مرفوض')
        self.status_combo.set('معلق')
        self.status_combo.pack(pady=(0, 15))

        # الوصف
        tk.Label(content_frame, text="الوصف:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.description_text = tk.Text(content_frame, font=('Arial', 11), width=35, height=4)
        self.description_text.pack(pady=(0, 15))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x', pady=(15, 10))
        
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=15,
            height=2,
            command=self.save_extract
        )
        save_btn.pack(side='right', padx=10, pady=10)
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=15,
            height=2,
            command=self.cancel
        )
        cancel_btn.pack(side='left', padx=10, pady=10)
        # اختبار الأزرار
        def test_buttons():
            print(f"🔍 اختبار الأزرار في النافذة")
            print(f"📍 إطار الأزرار: {buttons_frame.winfo_exists()}")
            print(f"📍 زر الحفظ: {save_btn.winfo_exists()}")
            print(f"📍 زر الإلغاء: {cancel_btn.winfo_exists()}")
            
        self.dialog.after(100, test_buttons)        


        # تعيين القيم الافتراضية
        if not self.extract_data:
            from datetime import datetime
            self.date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
            self.contractor_combo.set('أحمد محمد للمقاولات')
            self.project_combo.set('مشروع الواحة السكني')

    def load_extract_data(self):
        """تحميل بيانات المستخلص للتعديل"""
        if self.extract_data:
            self.extract_number_entry.insert(0, self.extract_data.get('extract_number', ''))
            self.contractor_combo.set(self.extract_data.get('contractor', 'أحمد محمد للمقاولات'))
            self.project_combo.set(self.extract_data.get('project', 'مشروع الواحة السكني'))
            self.amount_entry.insert(0, str(self.extract_data.get('amount', '')))
            self.date_entry.insert(0, str(self.extract_data.get('date', '')))

            # تحويل الحالة
            status_map = {
                'pending': 'معلق',
                'approved': 'تمت الموافقة',
                'paid': 'مدفوع',
                'rejected': 'مرفوض'
            }
            status_text = status_map.get(self.extract_data.get('status', 'pending'), 'معلق')
            self.status_combo.set(status_text)

            self.description_text.insert('1.0', self.extract_data.get('description', ''))

    def save_extract(self):
        """حفظ بيانات المستخلص"""
        # التحقق من صحة البيانات
        if not self.extract_number_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال رقم المستخلص")
            return

        try:
            # تحويل البيانات
            amount = float(self.amount_entry.get()) if self.amount_entry.get().strip() else 0.0

            # تحويل الحالة
            status_map = {
                'معلق': 'pending',
                'تمت الموافقة': 'approved',
                'مدفوع': 'paid',
                'مرفوض': 'rejected'
            }
            status = status_map.get(self.status_combo.get(), 'pending')

            # إعداد النتيجة
            self.result = {
                'extract_number': self.extract_number_entry.get().strip(),
                'contractor': self.contractor_combo.get(),
                'project': self.project_combo.get(),
                'amount': amount,
                'date': self.date_entry.get().strip() or None,
                'status': status,
                'description': self.description_text.get('1.0', 'end-1c').strip()
            }

            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيمة صحيحة للمبلغ")

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result

class InvoiceDialog:
    """نافذة حوار إضافة/تعديل فاتورة"""

    def __init__(self, parent, title, invoice_data=None):
        self.parent = parent
        self.invoice_data = invoice_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x650")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (550 // 2)
        self.dialog.geometry(f"500x650+{x}+{y}")

        self.create_widgets()

        if invoice_data:
            self.load_invoice_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات الفاتورة",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))

        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))

        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # رقم الفاتورة
        tk.Label(content_frame, text="رقم الفاتورة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.invoice_number_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.invoice_number_entry.pack(pady=(0, 15))

        # المورد
        tk.Label(content_frame, text="المورد:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.supplier_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.supplier_combo['values'] = ('شركة الحديد والصلب', 'مؤسسة مواد البناء', 'شركة الكهربائيات المتقدمة')
        self.supplier_combo.pack(pady=(0, 15))

        # المبلغ
        tk.Label(content_frame, text="المبلغ (ريال):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.amount_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.amount_entry.pack(pady=(0, 15))

        # تاريخ الفاتورة
        tk.Label(content_frame, text="تاريخ الفاتورة (YYYY-MM-DD):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.date_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.date_entry.pack(pady=(0, 15))

        # تاريخ الاستحقاق
        tk.Label(content_frame, text="تاريخ الاستحقاق (YYYY-MM-DD):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.due_date_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.due_date_entry.pack(pady=(0, 15))

        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.status_combo['values'] = ('معلقة', 'تمت الموافقة', 'مدفوعة', 'مرفوضة')
        self.status_combo.set('معلقة')
        self.status_combo.pack(pady=(0, 15))

        # الوصف
        tk.Label(content_frame, text="الوصف:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.description_text = tk.Text(content_frame, font=('Arial', 11), width=35, height=4)
        self.description_text.pack(pady=(0, 15))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x', pady=(15, 10))
        
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=15,
            height=2,
            command=self.save_invoice
        )
        save_btn.pack(side='right', padx=10, pady=10)
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=15,
            height=2,
            command=self.cancel
        )
        cancel_btn.pack(side='left', padx=10, pady=10)
        # اختبار الأزرار
        def test_buttons():
            print(f"🔍 اختبار الأزرار في النافذة")
            print(f"📍 إطار الأزرار: {buttons_frame.winfo_exists()}")
            print(f"📍 زر الحفظ: {save_btn.winfo_exists()}")
            print(f"📍 زر الإلغاء: {cancel_btn.winfo_exists()}")
            
        self.dialog.after(100, test_buttons)        


        # تعيين القيم الافتراضية
        if not self.invoice_data:
            from datetime import datetime, timedelta
            today = datetime.now()
            self.date_entry.insert(0, today.strftime("%Y-%m-%d"))
            self.due_date_entry.insert(0, (today + timedelta(days=30)).strftime("%Y-%m-%d"))
            self.supplier_combo.set('شركة الحديد والصلب')

    def load_invoice_data(self):
        """تحميل بيانات الفاتورة للتعديل"""
        if self.invoice_data:
            self.invoice_number_entry.insert(0, self.invoice_data.get('invoice_number', ''))
            self.supplier_combo.set(self.invoice_data.get('supplier', 'شركة الحديد والصلب'))
            self.amount_entry.insert(0, str(self.invoice_data.get('amount', '')))
            self.date_entry.insert(0, str(self.invoice_data.get('date', '')))
            self.due_date_entry.insert(0, str(self.invoice_data.get('due_date', '')))

            # تحويل الحالة
            status_map = {
                'pending': 'معلقة',
                'approved': 'تمت الموافقة',
                'paid': 'مدفوعة',
                'rejected': 'مرفوضة'
            }
            status_text = status_map.get(self.invoice_data.get('status', 'pending'), 'معلقة')
            self.status_combo.set(status_text)

            self.description_text.insert('1.0', self.invoice_data.get('description', ''))

    def save_invoice(self):
        """حفظ بيانات الفاتورة"""
        # التحقق من صحة البيانات
        if not self.invoice_number_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال رقم الفاتورة")
            return

        try:
            # تحويل البيانات
            amount = float(self.amount_entry.get()) if self.amount_entry.get().strip() else 0.0

            # تحويل الحالة
            status_map = {
                'معلقة': 'pending',
                'تمت الموافقة': 'approved',
                'مدفوعة': 'paid',
                'مرفوضة': 'rejected'
            }
            status = status_map.get(self.status_combo.get(), 'pending')

            # إعداد النتيجة
            self.result = {
                'invoice_number': self.invoice_number_entry.get().strip(),
                'supplier': self.supplier_combo.get(),
                'amount': amount,
                'date': self.date_entry.get().strip() or None,
                'due_date': self.due_date_entry.get().strip() or None,
                'status': status,
                'description': self.description_text.get('1.0', 'end-1c').strip()
            }

            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيمة صحيحة للمبلغ")

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result

class PurchaseRequestDialog:
    """نافذة حوار إضافة/تعديل طلب شراء"""

    def __init__(self, parent, title, purchase_data=None):
        self.parent = parent
        self.purchase_data = purchase_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x650")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (550 // 2)
        self.dialog.geometry(f"500x650+{x}+{y}")

        self.create_widgets()

        if purchase_data:
            self.load_purchase_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات طلب الشراء",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))

        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))

        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # رقم الطلب
        tk.Label(content_frame, text="رقم الطلب:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.request_number_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.request_number_entry.pack(pady=(0, 15))

        # المشروع
        tk.Label(content_frame, text="المشروع:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.project_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.project_combo['values'] = ('مشروع الواحة السكني', 'برج الأعمال التجاري', 'مجمع الفلل الراقية')
        self.project_combo.pack(pady=(0, 15))

        # المواد المطلوبة
        tk.Label(content_frame, text="المواد المطلوبة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.materials_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.materials_entry.pack(pady=(0, 15))

        # الكمية
        tk.Label(content_frame, text="الكمية:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.quantity_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.quantity_entry.pack(pady=(0, 15))

        # التكلفة المقدرة
        tk.Label(content_frame, text="التكلفة المقدرة (ريال):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.cost_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.cost_entry.pack(pady=(0, 15))

        # تاريخ الطلب
        tk.Label(content_frame, text="تاريخ الطلب (YYYY-MM-DD):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.date_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.date_entry.pack(pady=(0, 15))

        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.status_combo['values'] = ('معلق', 'تمت الموافقة', 'مرفوض', 'تم التنفيذ')
        self.status_combo.set('معلق')
        self.status_combo.pack(pady=(0, 15))

        # ملاحظات
        tk.Label(content_frame, text="ملاحظات:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.notes_text = tk.Text(content_frame, font=('Arial', 11), width=35, height=4)
        self.notes_text.pack(pady=(0, 15))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x', pady=(15, 10))
        
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=15,
            height=2,
            command=self.save_purchase
        )
        save_btn.pack(side='right', padx=10, pady=10)
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=15,
            height=2,
            command=self.cancel
        )
        cancel_btn.pack(side='left', padx=10, pady=10)
        # اختبار الأزرار
        def test_buttons():
            print(f"🔍 اختبار الأزرار في النافذة")
            print(f"📍 إطار الأزرار: {buttons_frame.winfo_exists()}")
            print(f"📍 زر الحفظ: {save_btn.winfo_exists()}")
            print(f"📍 زر الإلغاء: {cancel_btn.winfo_exists()}")
            
        self.dialog.after(100, test_buttons)        


        # تعيين القيم الافتراضية
        if not self.purchase_data:
            from datetime import datetime
            self.date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
            self.project_combo.set('مشروع الواحة السكني')

    def load_purchase_data(self):
        """تحميل بيانات طلب الشراء للتعديل"""
        if self.purchase_data:
            self.request_number_entry.insert(0, self.purchase_data.get('request_number', ''))
            self.project_combo.set(self.purchase_data.get('project', 'مشروع الواحة السكني'))
            self.materials_entry.insert(0, self.purchase_data.get('materials', ''))
            self.quantity_entry.insert(0, self.purchase_data.get('quantity', ''))
            self.cost_entry.insert(0, str(self.purchase_data.get('estimated_cost', '')))
            self.date_entry.insert(0, str(self.purchase_data.get('request_date', '')))

            # تحويل الحالة
            status_map = {
                'pending': 'معلق',
                'approved': 'تمت الموافقة',
                'rejected': 'مرفوض',
                'executed': 'تم التنفيذ'
            }
            status_text = status_map.get(self.purchase_data.get('status', 'pending'), 'معلق')
            self.status_combo.set(status_text)

            self.notes_text.insert('1.0', self.purchase_data.get('notes', ''))

    def save_purchase(self):
        """حفظ بيانات طلب الشراء"""
        # التحقق من صحة البيانات
        if not self.request_number_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال رقم الطلب")
            return

        if not self.materials_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال المواد المطلوبة")
            return

        try:
            # تحويل البيانات
            cost = float(self.cost_entry.get()) if self.cost_entry.get().strip() else 0.0

            # تحويل الحالة
            status_map = {
                'معلق': 'pending',
                'تمت الموافقة': 'approved',
                'مرفوض': 'rejected',
                'تم التنفيذ': 'executed'
            }
            status = status_map.get(self.status_combo.get(), 'pending')

            # إعداد النتيجة
            self.result = {
                'request_number': self.request_number_entry.get().strip(),
                'project': self.project_combo.get(),
                'materials': self.materials_entry.get().strip(),
                'quantity': self.quantity_entry.get().strip(),
                'estimated_cost': cost,
                'request_date': self.date_entry.get().strip() or None,
                'status': status,
                'notes': self.notes_text.get('1.0', 'end-1c').strip()
            }

            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيمة صحيحة للتكلفة")

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result

class MaintenanceTaskDialog:
    """نافذة حوار إضافة/تعديل مهمة صيانة"""

    def __init__(self, parent, title, task_data=None):
        self.parent = parent
        self.task_data = task_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x650")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (550 // 2)
        self.dialog.geometry(f"500x650+{x}+{y}")

        self.create_widgets()

        if task_data:
            self.load_task_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات مهمة الصيانة",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))

        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))

        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # رقم المهمة
        tk.Label(content_frame, text="رقم المهمة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.task_number_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.task_number_entry.pack(pady=(0, 15))

        # نوع الصيانة
        tk.Label(content_frame, text="نوع الصيانة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.maintenance_type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.maintenance_type_combo['values'] = ('صيانة كهربائية', 'صيانة سباكة', 'صيانة تكييف', 'صيانة عامة', 'تنظيف', 'إصلاحات')
        self.maintenance_type_combo.pack(pady=(0, 15))

        # الموقع/الوحدة
        tk.Label(content_frame, text="الموقع/الوحدة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.location_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.location_entry.pack(pady=(0, 15))

        # الفني المسؤول
        tk.Label(content_frame, text="الفني المسؤول:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.technician_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.technician_entry.pack(pady=(0, 15))

        # التاريخ
        tk.Label(content_frame, text="التاريخ (YYYY-MM-DD):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.date_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.date_entry.pack(pady=(0, 15))

        # التكلفة
        tk.Label(content_frame, text="التكلفة (ريال):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.cost_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.cost_entry.pack(pady=(0, 15))

        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.status_combo['values'] = ('معلقة', 'قيد التنفيذ', 'مكتملة', 'ملغاة')
        self.status_combo.set('معلقة')
        self.status_combo.pack(pady=(0, 15))

        # الوصف
        tk.Label(content_frame, text="الوصف:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.description_text = tk.Text(content_frame, font=('Arial', 11), width=35, height=4)
        self.description_text.pack(pady=(0, 15))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x', pady=(15, 10))
        
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=15,
            height=2,
            command=self.save_task
        )
        save_btn.pack(side='right', padx=10, pady=10)
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=15,
            height=2,
            command=self.cancel
        )
        cancel_btn.pack(side='left', padx=10, pady=10)
        # اختبار الأزرار
        def test_buttons():
            print(f"🔍 اختبار الأزرار في النافذة")
            print(f"📍 إطار الأزرار: {buttons_frame.winfo_exists()}")
            print(f"📍 زر الحفظ: {save_btn.winfo_exists()}")
            print(f"📍 زر الإلغاء: {cancel_btn.winfo_exists()}")
            
        self.dialog.after(100, test_buttons)        


        # تعيين القيم الافتراضية
        if not self.task_data:
            from datetime import datetime
            self.date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
            self.maintenance_type_combo.set('صيانة عامة')

    def load_task_data(self):
        """تحميل بيانات مهمة الصيانة للتعديل"""
        if self.task_data:
            self.task_number_entry.insert(0, self.task_data.get('task_number', ''))
            self.maintenance_type_combo.set(self.task_data.get('maintenance_type', 'صيانة عامة'))
            self.location_entry.insert(0, self.task_data.get('location', ''))
            self.technician_entry.insert(0, self.task_data.get('technician', ''))
            self.date_entry.insert(0, str(self.task_data.get('date', '')))
            self.cost_entry.insert(0, str(self.task_data.get('cost', '')))

            # تحويل الحالة
            status_map = {
                'pending': 'معلقة',
                'in_progress': 'قيد التنفيذ',
                'completed': 'مكتملة',
                'cancelled': 'ملغاة'
            }
            status_text = status_map.get(self.task_data.get('status', 'pending'), 'معلقة')
            self.status_combo.set(status_text)

            self.description_text.insert('1.0', self.task_data.get('description', ''))

    def save_task(self):
        """حفظ بيانات مهمة الصيانة"""
        # التحقق من صحة البيانات
        if not self.task_number_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال رقم المهمة")
            return

        try:
            # تحويل البيانات
            cost = float(self.cost_entry.get()) if self.cost_entry.get().strip() else 0.0

            # تحويل الحالة
            status_map = {
                'معلقة': 'pending',
                'قيد التنفيذ': 'in_progress',
                'مكتملة': 'completed',
                'ملغاة': 'cancelled'
            }
            status = status_map.get(self.status_combo.get(), 'pending')

            # إعداد النتيجة
            self.result = {
                'task_number': self.task_number_entry.get().strip(),
                'maintenance_type': self.maintenance_type_combo.get(),
                'location': self.location_entry.get().strip(),
                'technician': self.technician_entry.get().strip(),
                'date': self.date_entry.get().strip() or None,
                'cost': cost,
                'status': status,
                'description': self.description_text.get('1.0', 'end-1c').strip()
            }

            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيمة صحيحة للتكلفة")

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result

class DailyTaskDialog:
    """نافذة حوار إضافة/تعديل مهمة يومية"""

    def __init__(self, parent, title, task_data=None):
        self.parent = parent
        self.task_data = task_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x580")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"500x580+{x}+{y}")

        self.create_widgets()

        if task_data:
            self.load_task_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات المهمة اليومية",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))

        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))

        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عنوان المهمة
        tk.Label(content_frame, text="عنوان المهمة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.title_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.title_entry.pack(pady=(0, 15))

        # المسؤول
        tk.Label(content_frame, text="المسؤول:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.assigned_to_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.assigned_to_entry.pack(pady=(0, 15))

        # الأولوية
        tk.Label(content_frame, text="الأولوية:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.priority_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.priority_combo['values'] = ('منخفضة', 'متوسطة', 'عالية', 'عاجلة')
        self.priority_combo.set('متوسطة')
        self.priority_combo.pack(pady=(0, 15))

        # تاريخ الاستحقاق
        tk.Label(content_frame, text="تاريخ الاستحقاق (YYYY-MM-DD):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.due_date_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.due_date_entry.pack(pady=(0, 15))

        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.status_combo['values'] = ('معلقة', 'قيد التنفيذ', 'مكتملة', 'ملغاة')
        self.status_combo.set('معلقة')
        self.status_combo.pack(pady=(0, 15))

        # الوصف
        tk.Label(content_frame, text="الوصف:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.description_text = tk.Text(content_frame, font=('Arial', 11), width=35, height=6)
        self.description_text.pack(pady=(0, 15))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x', pady=(15, 10))
        
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=15,
            height=2,
            command=self.save_task
        )
        save_btn.pack(side='right', padx=10, pady=10)
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=15,
            height=2,
            command=self.cancel
        )
        cancel_btn.pack(side='left', padx=10, pady=10)
        # اختبار الأزرار
        def test_buttons():
            print(f"🔍 اختبار الأزرار في النافذة")
            print(f"📍 إطار الأزرار: {buttons_frame.winfo_exists()}")
            print(f"📍 زر الحفظ: {save_btn.winfo_exists()}")
            print(f"📍 زر الإلغاء: {cancel_btn.winfo_exists()}")
            
        self.dialog.after(100, test_buttons)        


        # تعيين القيم الافتراضية
        if not self.task_data:
            from datetime import datetime, timedelta
            self.due_date_entry.insert(0, (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d"))

    def load_task_data(self):
        """تحميل بيانات المهمة للتعديل"""
        if self.task_data:
            self.title_entry.insert(0, self.task_data.get('title', ''))
            self.assigned_to_entry.insert(0, self.task_data.get('assigned_to', ''))

            # تحويل الأولوية
            priority_map = {
                'low': 'منخفضة',
                'medium': 'متوسطة',
                'high': 'عالية',
                'urgent': 'عاجلة'
            }
            priority_text = priority_map.get(self.task_data.get('priority', 'medium'), 'متوسطة')
            self.priority_combo.set(priority_text)

            self.due_date_entry.insert(0, str(self.task_data.get('due_date', '')))

            # تحويل الحالة
            status_map = {
                'pending': 'معلقة',
                'in_progress': 'قيد التنفيذ',
                'completed': 'مكتملة',
                'cancelled': 'ملغاة'
            }
            status_text = status_map.get(self.task_data.get('status', 'pending'), 'معلقة')
            self.status_combo.set(status_text)

            self.description_text.insert('1.0', self.task_data.get('description', ''))

    def save_task(self):
        """حفظ بيانات المهمة"""
        # التحقق من صحة البيانات
        if not self.title_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال عنوان المهمة")
            return

        # تحويل الأولوية
        priority_map = {
            'منخفضة': 'low',
            'متوسطة': 'medium',
            'عالية': 'high',
            'عاجلة': 'urgent'
        }
        priority = priority_map.get(self.priority_combo.get(), 'medium')

        # تحويل الحالة
        status_map = {
            'معلقة': 'pending',
            'قيد التنفيذ': 'in_progress',
            'مكتملة': 'completed',
            'ملغاة': 'cancelled'
        }
        status = status_map.get(self.status_combo.get(), 'pending')

        # إعداد النتيجة
        self.result = {
            'title': self.title_entry.get().strip(),
            'assigned_to': self.assigned_to_entry.get().strip(),
            'priority': priority,
            'due_date': self.due_date_entry.get().strip() or None,
            'status': status,
            'description': self.description_text.get('1.0', 'end-1c').strip()
        }

        self.dialog.destroy()

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result

class UserDialog:
    """نافذة حوار إضافة/تعديل مستخدم"""

    def __init__(self, parent, title, user_data=None):
        self.parent = parent
        self.user_data = user_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x580")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"500x580+{x}+{y}")

        self.create_widgets()

        if user_data:
            self.load_user_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات المستخدم",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))

        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))

        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # اسم المستخدم
        tk.Label(content_frame, text="اسم المستخدم:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.username_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.username_entry.pack(pady=(0, 15))

        # كلمة المرور
        tk.Label(content_frame, text="كلمة المرور:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.password_entry = tk.Entry(content_frame, font=('Arial', 12), width=35, show='*')
        self.password_entry.pack(pady=(0, 15))

        # الاسم الكامل
        tk.Label(content_frame, text="الاسم الكامل:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.full_name_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.full_name_entry.pack(pady=(0, 15))

        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.email_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.email_entry.pack(pady=(0, 15))

        # نوع المستخدم
        tk.Label(content_frame, text="نوع المستخدم:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.user_type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.user_type_combo['values'] = ('admin', 'manager', 'employee', 'viewer')
        self.user_type_combo.set('employee')
        self.user_type_combo.pack(pady=(0, 15))

        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.is_active_var = tk.BooleanVar(value=True)
        self.is_active_check = tk.Checkbutton(
            content_frame,
            text="مفعل",
            variable=self.is_active_var,
            font=('Arial', 10),
            bg='white'
        )
        self.is_active_check.pack(anchor='e', pady=(0, 15))

        # أزرار التحكم - محسنة
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5', height=80)
        buttons_frame.pack(fill='x', side='bottom', pady=20, padx=20)
        buttons_frame.pack_propagate(False)  # منع تقليص الإطار

        # زر الإلغاء (يسار)
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=15,
            height=2,
            relief='raised',
            bd=3,
            command=self.cancel
        )
        cancel_btn.pack(side='left', padx=10, pady=10)
        # اختبار الأزرار
        def test_buttons():
            print(f"🔍 اختبار الأزرار في النافذة")
            print(f"📍 إطار الأزرار: {buttons_frame.winfo_exists()}")
            print(f"📍 زر الحفظ: {save_btn.winfo_exists()}")
            print(f"📍 زر الإلغاء: {cancel_btn.winfo_exists()}")
            
        self.dialog.after(100, test_buttons)

        # زر الحفظ (يمين)
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=15,
            height=2,
            relief='raised',
            bd=3,
            command=self.save_user
        )
        save_btn.pack(side='right', padx=10, pady=10)
        
        # التأكد من ظهور الأزرار
        buttons_frame.update()
        save_btn.update()
        cancel_btn.update()        


    def load_user_data(self):
        """تحميل بيانات المستخدم للتعديل"""
        if self.user_data:
            self.username_entry.insert(0, self.user_data.get('username', ''))
            # لا نعرض كلمة المرور للأمان
            self.full_name_entry.insert(0, self.user_data.get('full_name', ''))
            self.email_entry.insert(0, self.user_data.get('email', ''))
            self.user_type_combo.set(self.user_data.get('user_type', 'employee'))
            self.is_active_var.set(bool(self.user_data.get('is_active', True)))

    def save_user(self):
        """حفظ بيانات المستخدم"""
        # التحقق من صحة البيانات
        if not self.username_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
            return

        if not self.user_data and not self.password_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور للمستخدم الجديد")
            return

        if not self.full_name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال الاسم الكامل")
            return

        # إعداد النتيجة
        self.result = {
            'username': self.username_entry.get().strip(),
            'password': self.password_entry.get().strip() if self.password_entry.get().strip() else None,
            'full_name': self.full_name_entry.get().strip(),
            'email': self.email_entry.get().strip(),
            'user_type': self.user_type_combo.get(),
            'is_active': self.is_active_var.get()
        }

        self.dialog.destroy()

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result
