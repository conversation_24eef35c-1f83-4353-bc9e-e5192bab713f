#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لأزرار نوافذ الحوار
Complete Fix for Dialog Buttons
"""

import re

def fix_all_dialog_buttons():
    """إصلاح شامل لجميع أزرار نوافذ الحوار"""
    
    print("🔧 إصلاح شامل لأزرار نوافذ الحوار...")
    
    try:
        # قراءة الملف
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن جميع أقسام الأزرار وإصلاحها
        # النمط الحالي للأزرار
        button_pattern = r'''        # أزرار التحكم
        buttons_frame = tk\.Frame\(main_frame, bg='#f5f5f5'\)
        buttons_frame\.pack\(fill='x', pady=\(15, 10\)\)

        save_btn = tk\.Button\(
            buttons_frame,
            text="حفظ",
            font=\('Arial', 12, 'bold'\),
            bg='#27ae60',
            fg='white',
            width=15,
            height=2,
            command=self\.(save_\w+)
        \)
        save_btn\.pack\(side='right', padx=\(10, 0\)\)

        cancel_btn = tk\.Button\(
            buttons_frame,
            text="إلغاء",
            font=\('Arial', 12, 'bold'\),
            bg='#e74c3c',
            fg='white',
            width=15,
            height=2,
            command=self\.cancel
        \)
        cancel_btn\.pack\(side='right', padx=\(10, 10\)\)'''
        
        # النمط الجديد المحسن للأزرار
        def create_new_buttons(match):
            save_command = match.group(1)
            return f'''        # أزرار التحكم - محسنة
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5', height=80)
        buttons_frame.pack(fill='x', side='bottom', pady=20, padx=20)
        buttons_frame.pack_propagate(False)  # منع تقليص الإطار

        # زر الإلغاء (يسار)
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=15,
            height=2,
            relief='raised',
            bd=3,
            command=self.cancel
        )
        cancel_btn.pack(side='left', padx=10, pady=10)

        # زر الحفظ (يمين)
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=15,
            height=2,
            relief='raised',
            bd=3,
            command=self.{save_command}
        )
        save_btn.pack(side='right', padx=10, pady=10)
        
        # التأكد من ظهور الأزرار
        buttons_frame.update()
        save_btn.update()
        cancel_btn.update()'''
        
        # تطبيق الإصلاح
        content = re.sub(button_pattern, create_new_buttons, content, flags=re.MULTILINE | re.DOTALL)
        
        # إصلاح إضافي للحالات المتبقية
        # البحث عن أي أزرار بنمط مختلف
        alternative_patterns = [
            # نمط آخر محتمل
            (r'buttons_frame\.pack\(fill=\'x\', pady=\(10, 0\)\)', 
             'buttons_frame.pack(fill=\'x\', side=\'bottom\', pady=20, padx=20)'),
            
            # إصلاح pack للأزرار
            (r'save_btn\.pack\(side=\'right\', padx=\(10, 0\)\)', 
             'save_btn.pack(side=\'right\', padx=10, pady=10)'),
            
            (r'cancel_btn\.pack\(side=\'right\', padx=\(10, 10\)\)', 
             'cancel_btn.pack(side=\'left\', padx=10, pady=10)'),
        ]
        
        for pattern, replacement in alternative_patterns:
            content = re.sub(pattern, replacement, content)
        
        # إزالة المعلومات التشخيصية القديمة لتجنب التشويش
        diagnostic_pattern = r'''        # معلومات تشخيصية
        print\(f"🔍 نافذة الحوار: \{self\.dialog\.winfo_geometry\(\)\}"\)
        print\(f"🔍 الإطار الرئيسي: \{main_frame\.winfo_geometry\(\)\}"\)
        print\(f"🔍 إطار الأزرار: \{buttons_frame\.winfo_geometry\(\)\}"\)
        print\("✅ تم إنشاء الأزرار بنجاح!"\)'''
        
        content = re.sub(diagnostic_pattern, '', content, flags=re.MULTILINE | re.DOTALL)
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح جميع أزرار نوافذ الحوار!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {e}")
        return False

def add_button_test():
    """إضافة اختبار للأزرار في كل نافذة"""
    
    print("🔍 إضافة اختبار للأزرار...")
    
    try:
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة كود اختبار الأزرار
        test_code = '''
        # اختبار الأزرار
        def test_buttons():
            print(f"🔍 اختبار الأزرار في النافذة")
            print(f"📍 إطار الأزرار: {buttons_frame.winfo_exists()}")
            print(f"📍 زر الحفظ: {save_btn.winfo_exists()}")
            print(f"📍 زر الإلغاء: {cancel_btn.winfo_exists()}")
            
        self.dialog.after(100, test_buttons)'''
        
        # إضافة الاختبار بعد إنشاء الأزرار
        pattern = r'(cancel_btn\.pack\(side=\'left\', padx=10, pady=10\))'
        replacement = r'\1' + test_code
        
        content = re.sub(pattern, replacement, content)
        
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة اختبار الأزرار!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الاختبار: {e}")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🔧 إصلاح شامل لأزرار نوافذ الحوار")
    print("=" * 70)
    
    success1 = fix_all_dialog_buttons()
    success2 = add_button_test()
    
    if success1:
        print("\n🎉 تم الإصلاح الشامل بنجاح!")
        print("الآن ستظهر الأزرار بوضوح في جميع النوافذ.")
        print("الأزرار ستكون في الأسفل مع إطار ثابت.")
    else:
        print("\n❌ فشل في الإصلاح!")
    
    input("\nاضغط Enter للخروج...")
