#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التشغيل النهائي لنظام شركة رافع للتطوير العقاري
Final Run Script for Rafea Real Estate Management System
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_requirements():
    """التحقق من المتطلبات"""
    print("🔍 التحقق من المتطلبات...")
    
    missing_modules = []
    
    try:
        import tkinter
        print("✅ tkinter")
    except ImportError:
        missing_modules.append("tkinter")
        print("❌ tkinter")
    
    try:
        import sqlite3
        print("✅ sqlite3")
    except ImportError:
        missing_modules.append("sqlite3")
        print("❌ sqlite3")
    
    try:
        import bcrypt
        print("✅ bcrypt")
    except ImportError:
        missing_modules.append("bcrypt")
        print("❌ bcrypt")
    
    try:
        from loguru import logger
        print("✅ loguru")
    except ImportError:
        missing_modules.append("loguru")
        print("❌ loguru")
    
    if missing_modules:
        print(f"\n❌ المكتبات المفقودة: {', '.join(missing_modules)}")
        print("يرجى تثبيت المتطلبات باستخدام: pip install -r requirements.txt")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def check_database():
    """التحقق من قاعدة البيانات"""
    print("\n🔍 التحقق من قاعدة البيانات...")
    
    try:
        from database_sqlite import sqlite_manager
        
        if not sqlite_manager.test_connection():
            print("❌ فشل في الاتصال بقاعدة البيانات")
            print("جاري إعداد قاعدة البيانات...")
            
            # محاولة إعداد قاعدة البيانات
            from database_sqlite import init_sqlite_database
            if init_sqlite_database():
                print("✅ تم إعداد قاعدة البيانات بنجاح")
                return True
            else:
                print("❌ فشل في إعداد قاعدة البيانات")
                return False
        else:
            print("✅ قاعدة البيانات متصلة وجاهزة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في التحقق من قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🏢 نظام إدارة شركة رافع للتطوير العقاري")
    print("   Rafea Real Estate Development Management System")
    print("=" * 70)
    
    # التحقق من المتطلبات
    if not check_requirements():
        input("\nاضغط Enter للخروج...")
        return 1
    
    # التحقق من قاعدة البيانات
    if not check_database():
        input("\nاضغط Enter للخروج...")
        return 1
    
    print("\n🚀 جاري تشغيل التطبيق...")
    print("📋 بيانات الدخول الافتراضية:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("\n⚠️  يرجى تغيير كلمة المرور بعد تسجيل الدخول الأول")
    print("=" * 70)
    
    try:
        # تشغيل التطبيق الرئيسي
        from main_app import main as app_main
        return app_main()
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف التطبيق بواسطة المستخدم")
        return 0
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")
        print("\nللمساعدة:")
        print("1. تأكد من تثبيت جميع المتطلبات")
        print("2. تحقق من ملفات السجلات في مجلد logs")
        print("3. تواصل مع الدعم الفني")
        input("\nاضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
