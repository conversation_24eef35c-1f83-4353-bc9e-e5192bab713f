#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح أزرار نوافذ الحوار
Fix Dialog Buttons
"""

import re

def fix_dialog_buttons():
    """إصلاح أزرار نوافذ الحوار في ملف dialogs.py"""
    
    print("🔧 إصلاح أزرار نوافذ الحوار...")
    
    try:
        # قراءة الملف
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # النمط القديم للأزرار
        old_pattern = r'''        # أزرار التحكم
        buttons_frame = tk\.Frame\(main_frame, bg='#f5f5f5'\)
        buttons_frame\.pack\(fill='x'\)
        
        save_btn = tk\.Button\(
            buttons_frame,
            text="حفظ",
            font=\('Arial', 12, 'bold'\),
            bg='#27ae60',
            fg='white',
            width=12,
            command=self\.save_\w+
        \)
        save_btn\.pack\(side='left', padx=\(0, 10\)\)
        
        cancel_btn = tk\.Button\(
            buttons_frame,
            text="إلغاء",
            font=\('Arial', 12, 'bold'\),
            bg='#e74c3c',
            fg='white',
            width=12,
            command=self\.cancel
        \)
        cancel_btn\.pack\(side='left'\)'''
        
        # النمط الجديد للأزرار
        def replace_buttons(match):
            # استخراج اسم دالة الحفظ
            save_command = re.search(r'command=self\.(save_\w+)', match.group(0))
            if save_command:
                save_func = save_command.group(1)
            else:
                save_func = "save_data"
            
            return f'''        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x', pady=(10, 0))
        
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=12,
            height=2,
            command=self.{save_func}
        )
        save_btn.pack(side='right', padx=(10, 0))
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=12,
            height=2,
            command=self.cancel
        )
        cancel_btn.pack(side='right', padx=(10, 10))'''
        
        # البحث عن جميع أنماط الأزرار القديمة وإصلاحها
        patterns_to_fix = [
            # نمط عام للأزرار
            (r'        # أزرار التحكم\s+buttons_frame = tk\.Frame\(main_frame, bg=\'#f5f5f5\'\)\s+buttons_frame\.pack\(fill=\'x\'\)\s+save_btn = tk\.Button\(\s+buttons_frame,\s+text="حفظ",\s+font=\(\'Arial\', 12, \'bold\'\),\s+bg=\'#27ae60\',\s+fg=\'white\',\s+width=12,\s+command=self\.(save_\w+)\s+\)\s+save_btn\.pack\(side=\'left\', padx=\(0, 10\)\)\s+cancel_btn = tk\.Button\(\s+buttons_frame,\s+text="إلغاء",\s+font=\(\'Arial\', 12, \'bold\'\),\s+bg=\'#e74c3c\',\s+fg=\'white\',\s+width=12,\s+command=self\.cancel\s+\)\s+cancel_btn\.pack\(side=\'left\'\)',
             lambda m: f'''        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x', pady=(10, 0))
        
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=12,
            height=2,
            command=self.{m.group(1)}
        )
        save_btn.pack(side='right', padx=(10, 0))
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=12,
            height=2,
            command=self.cancel
        )
        cancel_btn.pack(side='right', padx=(10, 10))''')
        ]
        
        # تطبيق الإصلاحات
        for pattern, replacement in patterns_to_fix:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
        
        # إصلاح يدوي للحالات المتبقية
        # البحث عن أي أزرار لم يتم إصلاحها
        remaining_buttons = re.findall(r'save_btn\.pack\(side=\'left\', padx=\(0, 10\)\)', content)
        if remaining_buttons:
            print(f"⚠️  وجدت {len(remaining_buttons)} أزرار لم يتم إصلاحها، سيتم إصلاحها يدوياً...")
            
            # إصلاح pack للأزرار
            content = content.replace("save_btn.pack(side='left', padx=(0, 10))", "save_btn.pack(side='right', padx=(10, 0))")
            content = content.replace("cancel_btn.pack(side='left')", "cancel_btn.pack(side='right', padx=(10, 10))")
            
            # إصلاح pack للإطار
            content = content.replace("buttons_frame.pack(fill='x')", "buttons_frame.pack(fill='x', pady=(10, 0))")
            
            # إضافة height للأزرار
            content = re.sub(r'(width=12,)\s+(command=)', r'\1\n            height=2,\n            \2', content)
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح أزرار نوافذ الحوار بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الأزرار: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🔧 إصلاح أزرار نوافذ الحوار")
    print("=" * 60)
    
    success = fix_dialog_buttons()
    
    if success:
        print("\n🎉 تم إصلاح جميع الأزرار بنجاح!")
        print("الآن ستظهر أزرار الحفظ والإلغاء في جميع نوافذ الحوار.")
    else:
        print("\n❌ فشل في إصلاح الأزرار!")
    
    input("\nاضغط Enter للخروج...")
