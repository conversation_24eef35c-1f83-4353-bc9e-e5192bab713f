#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل نهائي لجميع الوظائف
"""

import tkinter as tk
import sys
import traceback

def test_system_startup():
    """اختبار بدء تشغيل النظام"""
    try:
        import rafea_complete_system
        print("✅ النظام الرئيسي يستورد بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في استيراد النظام الرئيسي: {e}")
        traceback.print_exc()
        return False

def test_all_dialogs():
    """اختبار جميع النوافذ"""
    try:
        from dialogs import (
            ExtractDialog, InvoiceDialog, PurchaseRequestDialog,
            MaintenanceTaskDialog, DailyTaskDialog, UserDialog
        )
        
        root = tk.Tk()
        root.withdraw()
        
        dialogs = [
            ("المستخدمين", UserDialog),
            ("المستخلصات", ExtractDialog),
            ("الفواتير", InvoiceDialog),
            ("المشتريات", PurchaseRequestDialog),
            ("الصيانة", MaintenanceTaskDialog),
            ("المهام اليومية", DailyTaskDialog),
        ]
        
        success_count = 0
        for name, dialog_class in dialogs:
            try:
                dialog = dialog_class(root, f"اختبار {name}")
                dialog.dialog.destroy()
                print(f"✅ نافذة {name} تعمل بشكل صحيح")
                success_count += 1
            except Exception as e:
                print(f"❌ خطأ في نافذة {name}: {e}")
        
        root.destroy()
        
        print(f"📊 النتيجة: {success_count}/{len(dialogs)} نوافذ تعمل")
        return success_count == len(dialogs)
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النوافذ: {e}")
        traceback.print_exc()
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        from database_sqlite import SQLiteManager
        
        sqlite_manager = SQLiteManager()
        result = sqlite_manager.execute_query("SELECT 1")
        
        if result:
            print("✅ قاعدة البيانات تعمل بشكل صحيح")
            return True
        else:
            print("❌ مشكلة في قاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        traceback.print_exc()
        return False

def main():
    """الاختبار الرئيسي"""
    print("=" * 70)
    print("🧪 الاختبار الشامل النهائي")
    print("=" * 70)
    
    tests = [
        ("اختبار بدء تشغيل النظام", test_system_startup),
        ("اختبار قاعدة البيانات", test_database_connection),
        ("اختبار جميع النوافذ", test_all_dialogs),
    ]
    
    success_count = 0
    for name, test_func in tests:
        print(f"\n🧪 {name}...")
        if test_func():
            success_count += 1
    
    print(f"\n📊 النتيجة النهائية: {success_count}/{len(tests)} اختبارات نجحت")
    
    if success_count == len(tests):
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
