#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام الشامل المطور بالكامل
Run Complete Developed System
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """الدالة الرئيسية"""
    print("=" * 90)
    print("🏢 النظام الشامل لإدارة شركة رافع للتطوير العقاري")
    print("   مع جميع الوحدات مطورة بالكامل")
    print("=" * 90)
    
    print("\n🎯 جميع الوحدات مطورة ومكتملة:")
    print("=" * 60)
    
    print("\n✅ الوحدات المطورة بالكامل:")
    print("   🏠 لوحة التحكم - إحصائيات تفاعلية ومحدثة")
    print("   🏗️ إدارة المشاريع - عمليات CRUD كاملة")
    print("   🏠 مبيعات الشقق - 4 تبويبات فرعية مطورة")
    print("   👷 المقاولون والمستخلصات - عمليات CRUD كاملة")
    print("   📦 الموردون والفواتير - عمليات CRUD كاملة")
    print("   🛒 المشتريات - عمليات CRUD كاملة")
    print("   🔧 الصيانة والتشغيل - عمليات CRUD كاملة")
    print("   📋 المهام اليومية - عمليات CRUD كاملة")
    print("   📊 التقارير - نظام متقدم للتقارير")
    print("   👥 إدارة المستخدمين - عمليات CRUD كاملة")
    
    print("\n🎨 الميزات المتقدمة:")
    print("   ✅ نوافذ حوار احترافية لجميع العمليات")
    print("   ✅ أزرار تفاعلية ملونة")
    print("   ✅ رسائل تأكيد وتحذير واضحة")
    print("   ✅ تحديث فوري للجداول والإحصائيات")
    print("   ✅ واجهة عربية كاملة مع دعم RTL")
    print("   ✅ نظام أمان متكامل مع تشفير")
    print("   ✅ معالجة أخطاء متقدمة")
    print("   ✅ سجلات شاملة لجميع العمليات")
    
    print("\n🔧 الوظائف المطورة في كل وحدة:")
    print("   ➕ إضافة عناصر جديدة")
    print("   ✏️  تعديل العناصر الموجودة")
    print("   🗑️  حذف العناصر مع تأكيد الأمان")
    print("   👁️  عرض التفاصيل الكاملة")
    print("   🔄 تحديث البيانات فورياً")
    print("   📊 تحديث الإحصائيات تلقائياً")
    
    print("\n📋 بيانات الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    
    print("\n🗂️  قاعدة البيانات:")
    print("   • 16 جدول مطور بالكامل")
    print("   • بيانات تجريبية جاهزة")
    print("   • فهارس محسنة للأداء")
    print("   • نسخ احتياطية تلقائية")
    
    print("\n⚠️  ملاحظات مهمة:")
    print("   • جميع العمليات محمية بتأكيدات أمان")
    print("   • البيانات محفوظة في قاعدة بيانات SQLite محلية")
    print("   • يمكن التراجع عن معظم العمليات")
    print("   • النظام يدعم تعدد المستخدمين")
    print("   • جميع الوحدات تعمل بكفاءة عالية")
    
    print("\n🎯 الوحدات الجاهزة للاستخدام:")
    print("   1. ✅ لوحة التحكم - مع إحصائيات مباشرة")
    print("   2. ✅ إدارة المشاريع - مع تتبع التقدم")
    print("   3. ✅ إدارة العملاء - مع تصنيف الأنواع")
    print("   4. ✅ إدارة الوحدات - مع حالات البيع")
    print("   5. ✅ إدارة العقود - مع تتبع المدفوعات")
    print("   6. ✅ إدارة المدفوعات - مع السجلات")
    print("   7. ✅ إدارة المقاولين - مع المستخلصات")
    print("   8. ✅ إدارة الموردين - مع الفواتير")
    print("   9. ✅ إدارة المشتريات - مع طلبات الشراء")
    print("   10. ✅ إدارة الصيانة - مع مهام التشغيل")
    print("   11. ✅ إدارة المهام اليومية - مع الأولويات")
    print("   12. ✅ نظام التقارير - مع إمكانيات متقدمة")
    print("   13. ✅ إدارة المستخدمين - مع الصلاحيات")
    
    print("\n" + "=" * 90)
    print("🚀 جاري تشغيل النظام الشامل المطور...")
    print("=" * 90)
    
    try:
        # تشغيل النظام الشامل
        from rafea_complete_system import main as system_main
        return system_main()
        
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف النظام بواسطة المستخدم")
        return 0
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("\nللمساعدة:")
        print("1. تأكد من تثبيت جميع المتطلبات")
        print("2. تحقق من ملفات السجلات في مجلد logs")
        print("3. تواصل مع الدعم الفني")
        input("\nاضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    sys.exit(main())
