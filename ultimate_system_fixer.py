#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الأداة النهائية لإصلاح جميع مشاكل النظام
Ultimate System Fixer
"""

import sqlite3
import os
import bcrypt
from datetime import datetime

def fix_database_connection():
    """إصلاح مشكلة الاتصال بقاعدة البيانات"""
    print("🔧 إصلاح مشكلة الاتصال بقاعدة البيانات...")
    
    try:
        # قراءة ملف قاعدة البيانات
        with open('database_sqlite.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح دالة test_connection
        old_test_function = '''    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("SELECT 1")
                result = cursor.fetchone()
                return result is not None
        except Exception as e:
            logger.error(f"فشل في اختبار الاتصال: {e}")
            return False'''
        
        new_test_function = '''    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.execute("SELECT 1")
            result = cursor.fetchone()
            conn.close()
            return result is not None
        except Exception as e:
            logger.error(f"فشل في اختبار الاتصال: {e}")
            return False'''
        
        content = content.replace(old_test_function, new_test_function)
        
        # إصلاح دالة execute_query
        old_execute_function = '''    def execute_query(self, query, params=None):
        """تنفيذ استعلام قاعدة البيانات"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(query, params or ())
                if query.strip().upper().startswith('SELECT'):
                    return cursor.fetchall()
                else:
                    conn.commit()
                    return cursor.rowcount
        except Exception as e:
            logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise'''
        
        new_execute_function = '''    def execute_query(self, query, params=None):
        """تنفيذ استعلام قاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            conn.execute("PRAGMA foreign_keys = OFF")
            cursor = conn.execute(query, params or ())
            if query.strip().upper().startswith('SELECT'):
                result = cursor.fetchall()
                conn.close()
                return result
            else:
                conn.commit()
                rowcount = cursor.rowcount
                conn.close()
                return rowcount
        except Exception as e:
            logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise'''
        
        content = content.replace(old_execute_function, new_execute_function)
        
        # كتابة الملف المحدث
        with open('database_sqlite.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح مشكلة الاتصال بقاعدة البيانات!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
        return False

def ensure_database_exists():
    """التأكد من وجود قاعدة البيانات"""
    print("🔧 التأكد من وجود قاعدة البيانات...")
    
    db_path = 'data/rafea_system.db'
    
    if not os.path.exists(db_path):
        print("⚠️ قاعدة البيانات غير موجودة - سيتم إنشاؤها...")
        
        # إنشاء مجلد البيانات
        if not os.path.exists('data'):
            os.makedirs('data')
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # إنشاء الجداول الأساسية
            cursor.execute('''
                CREATE TABLE users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    user_type TEXT NOT NULL,
                    is_active INTEGER DEFAULT 1,
                    created_date DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE daily_tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_title TEXT NOT NULL,
                    description TEXT,
                    assigned_to TEXT,
                    priority TEXT DEFAULT 'medium',
                    due_date DATE,
                    status TEXT DEFAULT 'pending',
                    created_date DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE maintenance_tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    task_number TEXT NOT NULL,
                    task_title TEXT,
                    maintenance_type TEXT,
                    location TEXT,
                    technician TEXT,
                    date DATE,
                    cost REAL DEFAULT 0,
                    status TEXT DEFAULT 'pending',
                    description TEXT,
                    created_date DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE extracts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    extract_number TEXT NOT NULL,
                    contractor TEXT NOT NULL,
                    project INTEGER DEFAULT 1,
                    total_amount REAL NOT NULL,
                    extract_date DATE,
                    status TEXT DEFAULT 'pending',
                    description TEXT,
                    created_date DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE invoices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_number TEXT NOT NULL,
                    supplier TEXT NOT NULL,
                    total_amount REAL NOT NULL,
                    invoice_date DATE,
                    due_date DATE,
                    status TEXT DEFAULT 'pending',
                    description TEXT,
                    created_date DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE purchase_requests (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    request_number TEXT NOT NULL,
                    project INTEGER DEFAULT 1,
                    materials TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    estimated_cost REAL,
                    request_date DATE,
                    status TEXT DEFAULT 'pending',
                    notes TEXT
                )
            ''')
            
            # إنشاء مستخدم افتراضي
            password_hash = bcrypt.hashpw("admin123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            cursor.execute('''
                INSERT INTO users (username, password_hash, full_name, user_type, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', ('admin', password_hash, 'المدير العام', 'admin', 1))
            
            conn.commit()
            conn.close()
            
            print("✅ تم إنشاء قاعدة البيانات بنجاح!")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            return False
    else:
        print("✅ قاعدة البيانات موجودة")
        return True

def test_complete_system():
    """اختبار النظام الكامل"""
    print("🧪 اختبار النظام الكامل...")
    
    try:
        # اختبار قاعدة البيانات
        from database_sqlite import SQLiteManager
        sqlite_manager = SQLiteManager()
        
        if sqlite_manager.test_connection():
            print("✅ قاعدة البيانات تعمل")
        else:
            print("❌ قاعدة البيانات لا تعمل")
            return False
        
        # اختبار استيراد النظام
        import rafea_complete_system
        print("✅ النظام الرئيسي يستورد بنجاح")
        
        # اختبار النوافذ
        import tkinter as tk
        from dialogs import DailyTaskDialog, MaintenanceTaskDialog
        
        root = tk.Tk()
        root.withdraw()
        
        # اختبار نافذة المهام اليومية
        daily_dialog = DailyTaskDialog(root, "اختبار")
        daily_dialog.dialog.destroy()
        print("✅ نافذة المهام اليومية تعمل")
        
        # اختبار نافذة الصيانة
        maintenance_dialog = MaintenanceTaskDialog(root, "اختبار")
        maintenance_dialog.dialog.destroy()
        print("✅ نافذة الصيانة تعمل")
        
        root.destroy()
        
        print("🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def create_startup_script():
    """إنشاء سكريبت بدء التشغيل"""
    print("🔧 إنشاء سكريبت بدء التشغيل...")
    
    startup_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت بدء تشغيل النظام
System Startup Script
"""

import os
import sys

def main():
    """تشغيل النظام"""
    print("=" * 70)
    print("🏢 النظام الشامل لإدارة شركة رافع للتطوير العقاري")
    print("=" * 70)
    
    try:
        # التحقق من وجود قاعدة البيانات
        if not os.path.exists('data/rafea_system.db'):
            print("⚠️ قاعدة البيانات غير موجودة!")
            print("يرجى تشغيل: python create_database_from_scratch.py")
            return
        
        # تشغيل النظام
        import rafea_complete_system
        print("✅ تم تشغيل النظام بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("\\nللمساعدة:")
        print("1. تشغيل: python create_database_from_scratch.py")
        print("2. تشغيل: python ultimate_system_fixer.py")
        print("3. تشغيل: python start_system.py")

if __name__ == "__main__":
    main()
'''
    
    try:
        with open('start_system.py', 'w', encoding='utf-8') as f:
            f.write(startup_content)
        print("✅ تم إنشاء سكريبت بدء التشغيل: start_system.py")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء سكريبت بدء التشغيل: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🔧 الأداة النهائية لإصلاح جميع مشاكل النظام")
    print("=" * 70)
    
    fixes = [
        ("التأكد من وجود قاعدة البيانات", ensure_database_exists),
        ("إصلاح مشكلة الاتصال بقاعدة البيانات", fix_database_connection),
        ("اختبار النظام الكامل", test_complete_system),
        ("إنشاء سكريبت بدء التشغيل", create_startup_script),
    ]
    
    success_count = 0
    for name, fix_func in fixes:
        print(f"\\n🔧 {name}...")
        if fix_func():
            success_count += 1
    
    print(f"\\n📊 النتيجة النهائية: {success_count}/{len(fixes)} إصلاحات نجحت")
    
    if success_count == len(fixes):
        print("\\n🎉 تم إصلاح جميع مشاكل النظام بنجاح!")
        print("\\nيمكنك الآن تشغيل النظام بأحد الطرق التالية:")
        print("1. python rafea_complete_system.py")
        print("2. python start_system.py")
        print("\\nبيانات تسجيل الدخول:")
        print("اسم المستخدم: admin")
        print("كلمة المرور: admin123")
    else:
        print("\\n⚠️ بعض الإصلاحات فشلت. راجع الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
