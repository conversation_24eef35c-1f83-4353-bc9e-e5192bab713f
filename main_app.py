#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التطبيق الرئيسي المحدث لنظام شركة رافع للتطوير العقاري
Updated Main Application for Rafea Real Estate Management System
"""

import sys
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from pathlib import Path
import bcrypt
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database_sqlite import sqlite_manager
from utils.logger_setup import setup_logger
from loguru import logger
from dialogs import ProjectDialog, CustomerDialog

class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.user_data = None
        self.setup_window()
        self.create_widgets()
    
    def setup_window(self):
        """إعداد نافذة تسجيل الدخول"""
        self.root.title("تسجيل الدخول - نظام شركة رافع للتطوير العقاري")
        self.root.geometry("400x300")
        self.root.configure(bg='#f5f5f5')
        self.root.resizable(False, False)
        
        # توسيط النافذة
        self.root.eval('tk::PlaceWindow . center')
    
    def create_widgets(self):
        """إنشاء عناصر واجهة تسجيل الدخول"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg='#f5f5f5')
        main_frame.pack(expand=True, fill='both', padx=20, pady=20)
        
        # شعار الشركة
        logo_frame = tk.Frame(main_frame, bg='#2c3e50', height=80)
        logo_frame.pack(fill='x', pady=(0, 20))
        logo_frame.pack_propagate(False)
        
        logo_label = tk.Label(
            logo_frame,
            text="شركة رافع للتطوير العقاري",
            font=('Arial', 16, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        logo_label.pack(expand=True)
        
        # عنوان تسجيل الدخول
        title_label = tk.Label(
            main_frame,
            text="تسجيل الدخول",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))
        
        # حقل اسم المستخدم
        tk.Label(main_frame, text="اسم المستخدم:", font=('Arial', 10), bg='#f5f5f5').pack(anchor='e', pady=(0, 5))
        self.username_entry = tk.Entry(main_frame, font=('Arial', 12), width=25)
        self.username_entry.pack(pady=(0, 10))
        self.username_entry.insert(0, "admin")  # قيمة افتراضية
        
        # حقل كلمة المرور
        tk.Label(main_frame, text="كلمة المرور:", font=('Arial', 10), bg='#f5f5f5').pack(anchor='e', pady=(0, 5))
        self.password_entry = tk.Entry(main_frame, font=('Arial', 12), width=25, show='*')
        self.password_entry.pack(pady=(0, 20))
        self.password_entry.insert(0, "admin123")  # قيمة افتراضية
        
        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack()
        
        login_btn = tk.Button(
            buttons_frame,
            text="تسجيل الدخول",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=12,
            command=self.login
        )
        login_btn.pack(side='left', padx=(0, 10))
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=12,
            command=self.root.quit
        )
        cancel_btn.pack(side='left')
        
        # ربط Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda e: self.login())
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
    
    def login(self):
        """معالج تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        try:
            # البحث عن المستخدم في قاعدة البيانات
            query = "SELECT id, username, password_hash, full_name, user_type, is_active FROM users WHERE username = ?"
            result = sqlite_manager.execute_query(query, (username,))
            
            if not result:
                messagebox.showerror("خطأ", "اسم المستخدم غير صحيح")
                return
            
            user = result[0]
            
            # التحقق من حالة المستخدم
            if not user['is_active']:
                messagebox.showerror("خطأ", "الحساب غير مفعل")
                return
            
            # التحقق من كلمة المرور
            if bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
                self.user_data = {
                    'id': user['id'],
                    'username': user['username'],
                    'full_name': user['full_name'],
                    'user_type': user['user_type']
                }
                
                logger.info(f"تم تسجيل دخول المستخدم: {username}")
                self.root.destroy()
            else:
                messagebox.showerror("خطأ", "كلمة المرور غير صحيحة")
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل الدخول: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في تسجيل الدخول:\n{str(e)}")
    
    def run(self):
        """تشغيل نافذة تسجيل الدخول"""
        self.root.mainloop()
        return self.user_data

class RafeaMainApp:
    """التطبيق الرئيسي"""
    
    def __init__(self, user_data):
        self.user_data = user_data
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.load_data()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title(f"نظام إدارة شركة رافع للتطوير العقاري - {self.user_data['full_name']}")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f5f5f5')
        
        # تعيين الحد الأدنى لحجم النافذة
        self.root.minsize(1000, 600)
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # شريط العنوان
        self.create_header()
        
        # التبويبات الرئيسية
        self.create_tabs()
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_header(self):
        """إنشاء شريط العنوان"""
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        header_frame.pack(fill='x', padx=5, pady=5)
        header_frame.pack_propagate(False)
        
        # عنوان النظام
        title_label = tk.Label(
            header_frame,
            text="نظام إدارة شركة رافع للتطوير العقاري",
            font=('Arial', 18, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(side='left', padx=20, pady=20)
        
        # معلومات المستخدم
        user_frame = tk.Frame(header_frame, bg='#2c3e50')
        user_frame.pack(side='right', padx=20, pady=20)
        
        user_label = tk.Label(
            user_frame,
            text=f"مرحباً، {self.user_data['full_name']}",
            font=('Arial', 12, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        user_label.pack()
        
        logout_btn = tk.Button(
            user_frame,
            text="تسجيل الخروج",
            font=('Arial', 10),
            bg='#e74c3c',
            fg='white',
            command=self.logout
        )
        logout_btn.pack(pady=(5, 0))
    
    def create_tabs(self):
        """إنشاء التبويبات"""
        # إطار التبويبات
        tabs_frame = tk.Frame(self.root, bg='#ecf0f1')
        tabs_frame.pack(fill='both', expand=True, padx=5, pady=(0, 5))
        
        # إنشاء التبويبات
        self.notebook = ttk.Notebook(tabs_frame)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # تبويب لوحة التحكم
        self.dashboard_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(self.dashboard_frame, text='لوحة التحكم')
        self.create_dashboard()
        
        # تبويب المشاريع
        self.projects_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(self.projects_frame, text='إدارة المشاريع')
        self.create_projects_tab()
        
        # تبويب العملاء
        self.customers_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(self.customers_frame, text='إدارة العملاء')
        self.create_customers_tab()
        
        # تبويب الوحدات
        self.units_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(self.units_frame, text='إدارة الوحدات')
        self.create_units_tab()
        
        # تبويب التقارير
        self.reports_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(self.reports_frame, text='التقارير')
        self.create_reports_tab()
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = tk.Frame(self.root, bg='#95a5a6', height=30)
        self.status_frame.pack(fill='x', padx=5, pady=(0, 5))
        self.status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(
            self.status_frame,
            text="جاهز",
            font=('Arial', 10),
            fg='white',
            bg='#95a5a6'
        )
        self.status_label.pack(side='left', padx=10, pady=5)
        
        # معلومات قاعدة البيانات
        db_label = tk.Label(
            self.status_frame,
            text="قاعدة البيانات: SQLite",
            font=('Arial', 10),
            fg='white',
            bg='#95a5a6'
        )
        db_label.pack(side='right', padx=10, pady=5)
    
    def create_dashboard(self):
        """إنشاء لوحة التحكم"""
        # عنوان لوحة التحكم
        title_label = tk.Label(
            self.dashboard_frame,
            text="لوحة التحكم الرئيسية",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        title_label.pack(pady=20)
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(self.dashboard_frame, bg='white')
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        # سيتم تحديث الإحصائيات في load_data
        self.stats_cards = {}
        
        # معلومات النظام
        info_text = """
        🎉 مرحباً بك في نظام إدارة شركة رافع للتطوير العقاري
        
        ✅ قاعدة البيانات: SQLite (جاهزة ومتصلة)
        ✅ المستخدم الحالي: """ + self.user_data['full_name'] + """
        ✅ نوع المستخدم: """ + self.user_data['user_type'] + """
        
        📋 الوحدات المتاحة:
        • إدارة المشاريع العقارية
        • إدارة العملاء
        • إدارة الوحدات السكنية
        • التقارير والإحصائيات
        """
        
        info_label = tk.Label(
            self.dashboard_frame,
            text=info_text,
            font=('Arial', 11),
            fg='#34495e',
            bg='white',
            justify='right'
        )
        info_label.pack(pady=20, padx=20)
    
    def create_projects_tab(self):
        """إنشاء تبويب المشاريع"""
        # عنوان
        title_label = tk.Label(
            self.projects_frame,
            text="إدارة المشاريع العقارية",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        title_label.pack(pady=20)

        # شريط الأدوات
        toolbar_frame = tk.Frame(self.projects_frame, bg='white')
        toolbar_frame.pack(fill='x', padx=20, pady=10)

        add_btn = tk.Button(
            toolbar_frame,
            text="إضافة مشروع جديد",
            font=('Arial', 10, 'bold'),
            bg='#27ae60',
            fg='white',
            relief='flat',
            padx=20,
            pady=8,
            command=self.add_project
        )
        add_btn.pack(side='left', padx=(0, 10))

        edit_btn = tk.Button(
            toolbar_frame,
            text="تعديل",
            font=('Arial', 10, 'bold'),
            bg='#3498db',
            fg='white',
            relief='flat',
            padx=20,
            pady=8,
            command=self.edit_project
        )
        edit_btn.pack(side='left', padx=(0, 10))

        delete_btn = tk.Button(
            toolbar_frame,
            text="حذف",
            font=('Arial', 10, 'bold'),
            bg='#e74c3c',
            fg='white',
            relief='flat',
            padx=20,
            pady=8,
            command=self.delete_project
        )
        delete_btn.pack(side='left', padx=(0, 10))

        refresh_btn = tk.Button(
            toolbar_frame,
            text="تحديث",
            font=('Arial', 10, 'bold'),
            bg='#95a5a6',
            fg='white',
            relief='flat',
            padx=20,
            pady=8,
            command=self.refresh_projects
        )
        refresh_btn.pack(side='right')

        # جدول المشاريع
        columns = ('الرقم', 'اسم المشروع', 'الموقع', 'النوع', 'التكلفة', 'نسبة الإنجاز', 'الحالة')
        self.projects_tree = ttk.Treeview(self.projects_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.projects_tree.heading(col, text=col)
            self.projects_tree.column(col, width=150)

        # شريط التمرير
        projects_scrollbar = ttk.Scrollbar(self.projects_frame, orient='vertical', command=self.projects_tree.yview)
        self.projects_tree.configure(yscrollcommand=projects_scrollbar.set)

        # تعبئة الجدول
        self.projects_tree.pack(side='left', fill='both', expand=True, padx=(20, 0), pady=10)
        projects_scrollbar.pack(side='right', fill='y', padx=(0, 20), pady=10)

        # تحميل بيانات المشاريع
        self.load_projects()
    
    def create_customers_tab(self):
        """إنشاء تبويب العملاء"""
        # عنوان
        title_label = tk.Label(
            self.customers_frame,
            text="إدارة العملاء",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        title_label.pack(pady=20)

        # شريط الأدوات
        toolbar_frame = tk.Frame(self.customers_frame, bg='white')
        toolbar_frame.pack(fill='x', padx=20, pady=10)

        add_customer_btn = tk.Button(
            toolbar_frame,
            text="إضافة عميل جديد",
            font=('Arial', 10, 'bold'),
            bg='#27ae60',
            fg='white',
            relief='flat',
            padx=20,
            pady=8,
            command=self.add_customer
        )
        add_customer_btn.pack(side='left', padx=(0, 10))

        edit_customer_btn = tk.Button(
            toolbar_frame,
            text="تعديل",
            font=('Arial', 10, 'bold'),
            bg='#3498db',
            fg='white',
            relief='flat',
            padx=20,
            pady=8,
            command=self.edit_customer
        )
        edit_customer_btn.pack(side='left', padx=(0, 10))

        delete_customer_btn = tk.Button(
            toolbar_frame,
            text="حذف",
            font=('Arial', 10, 'bold'),
            bg='#e74c3c',
            fg='white',
            relief='flat',
            padx=20,
            pady=8,
            command=self.delete_customer
        )
        delete_customer_btn.pack(side='left', padx=(0, 10))

        refresh_customers_btn = tk.Button(
            toolbar_frame,
            text="تحديث",
            font=('Arial', 10, 'bold'),
            bg='#95a5a6',
            fg='white',
            relief='flat',
            padx=20,
            pady=8,
            command=self.refresh_customers
        )
        refresh_customers_btn.pack(side='right')

        # جدول العملاء
        columns = ('الرقم', 'الاسم الكامل', 'رقم الهوية', 'الهاتف', 'البريد الإلكتروني', 'النوع')
        self.customers_tree = ttk.Treeview(self.customers_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.customers_tree.heading(col, text=col)
            self.customers_tree.column(col, width=150)

        # شريط التمرير
        customers_scrollbar = ttk.Scrollbar(self.customers_frame, orient='vertical', command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=customers_scrollbar.set)

        # تعبئة الجدول
        self.customers_tree.pack(side='left', fill='both', expand=True, padx=(20, 0), pady=10)
        customers_scrollbar.pack(side='right', fill='y', padx=(0, 20), pady=10)

        # تحميل بيانات العملاء
        self.load_customers()
    
    def create_units_tab(self):
        """إنشاء تبويب الوحدات"""
        # عنوان
        title_label = tk.Label(
            self.units_frame,
            text="إدارة الوحدات السكنية",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        title_label.pack(pady=20)

        # شريط الأدوات
        toolbar_frame = tk.Frame(self.units_frame, bg='white')
        toolbar_frame.pack(fill='x', padx=20, pady=10)

        refresh_units_btn = tk.Button(
            toolbar_frame,
            text="تحديث",
            font=('Arial', 10, 'bold'),
            bg='#95a5a6',
            fg='white',
            relief='flat',
            padx=20,
            pady=8,
            command=self.refresh_units
        )
        refresh_units_btn.pack(side='right')

        # جدول الوحدات
        columns = ('الرقم', 'رقم الوحدة', 'المشروع', 'الطابق', 'النوع', 'المساحة', 'السعر', 'الحالة')
        self.units_tree = ttk.Treeview(self.units_frame, columns=columns, show='headings', height=15)

        for col in columns:
            self.units_tree.heading(col, text=col)
            self.units_tree.column(col, width=120)

        # شريط التمرير
        units_scrollbar = ttk.Scrollbar(self.units_frame, orient='vertical', command=self.units_tree.yview)
        self.units_tree.configure(yscrollcommand=units_scrollbar.set)

        # تعبئة الجدول
        self.units_tree.pack(side='left', fill='both', expand=True, padx=(20, 0), pady=10)
        units_scrollbar.pack(side='right', fill='y', padx=(0, 20), pady=10)

        # تحميل بيانات الوحدات
        self.load_units()
    
    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        # عنوان
        title_label = tk.Label(
            self.reports_frame,
            text="التقارير والإحصائيات",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        title_label.pack(pady=20)

        # إطار التقارير
        reports_main_frame = tk.Frame(self.reports_frame, bg='white')
        reports_main_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # قسم التقارير السريعة
        quick_reports_frame = tk.LabelFrame(
            reports_main_frame,
            text="التقارير السريعة",
            font=('Arial', 12, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        quick_reports_frame.pack(fill='x', pady=(0, 20))

        # أزرار التقارير السريعة
        reports_buttons_frame = tk.Frame(quick_reports_frame, bg='white')
        reports_buttons_frame.pack(fill='x', padx=10, pady=10)

        # تقرير المشاريع
        projects_report_btn = tk.Button(
            reports_buttons_frame,
            text="تقرير المشاريع",
            font=('Arial', 10, 'bold'),
            bg='#3498db',
            fg='white',
            width=15,
            command=self.generate_projects_report
        )
        projects_report_btn.grid(row=0, column=0, padx=5, pady=5)

        # تقرير العملاء
        customers_report_btn = tk.Button(
            reports_buttons_frame,
            text="تقرير العملاء",
            font=('Arial', 10, 'bold'),
            bg='#27ae60',
            fg='white',
            width=15,
            command=self.generate_customers_report
        )
        customers_report_btn.grid(row=0, column=1, padx=5, pady=5)

        # تقرير الوحدات
        units_report_btn = tk.Button(
            reports_buttons_frame,
            text="تقرير الوحدات",
            font=('Arial', 10, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=15,
            command=self.generate_units_report
        )
        units_report_btn.grid(row=0, column=2, padx=5, pady=5)

        # تقرير شامل
        comprehensive_report_btn = tk.Button(
            reports_buttons_frame,
            text="تقرير شامل",
            font=('Arial', 10, 'bold'),
            bg='#9b59b6',
            fg='white',
            width=15,
            command=self.generate_comprehensive_report
        )
        comprehensive_report_btn.grid(row=0, column=3, padx=5, pady=5)

        # منطقة عرض التقرير
        self.report_display_frame = tk.LabelFrame(
            reports_main_frame,
            text="عرض التقرير",
            font=('Arial', 12, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        self.report_display_frame.pack(fill='both', expand=True)

        # منطقة النص لعرض التقرير
        self.report_text = tk.Text(
            self.report_display_frame,
            font=('Arial', 11),
            wrap=tk.WORD,
            bg='#f8f9fa',
            fg='#2c3e50'
        )

        # شريط التمرير للتقرير
        report_scrollbar = ttk.Scrollbar(self.report_display_frame, orient='vertical', command=self.report_text.yview)
        self.report_text.configure(yscrollcommand=report_scrollbar.set)

        self.report_text.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        report_scrollbar.pack(side='right', fill='y', padx=(0, 10), pady=10)

        # عرض تقرير افتراضي
        self.show_default_report()
    
    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            # تحميل الإحصائيات
            self.load_statistics()
            self.update_status("تم تحميل البيانات بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في تحميل البيانات: {e}")
            self.update_status(f"خطأ في تحميل البيانات: {str(e)}")
    
    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            # إحصائيات المشاريع
            projects_count = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM projects")[0]['count']
            active_projects = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM projects WHERE status = 'in_progress'")[0]['count']
            
            # إحصائيات الوحدات
            units_count = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM units")[0]['count']
            sold_units = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM units WHERE status = 'sold'")[0]['count']
            
            # إحصائيات العملاء
            customers_count = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM customers")[0]['count']
            
            # إنشاء بطاقات الإحصائيات
            stats = [
                ("إجمالي المشاريع", str(projects_count), "#e74c3c"),
                ("المشاريع النشطة", str(active_projects), "#27ae60"),
                ("إجمالي الوحدات", str(units_count), "#3498db"),
                ("الوحدات المباعة", str(sold_units), "#f39c12"),
                ("إجمالي العملاء", str(customers_count), "#9b59b6")
            ]
            
            # إنشاء بطاقات الإحصائيات في لوحة التحكم
            stats_frame = None
            for widget in self.dashboard_frame.winfo_children():
                if isinstance(widget, tk.Frame) and widget.winfo_children():
                    stats_frame = widget
                    break
            
            if stats_frame:
                # مسح البطاقات القديمة
                for widget in stats_frame.winfo_children():
                    widget.destroy()
                
                # إنشاء البطاقات الجديدة
                for i, (title, value, color) in enumerate(stats):
                    self.create_stat_card(stats_frame, title, value, color, i)
            
        except Exception as e:
            logger.error(f"خطأ في تحميل الإحصائيات: {e}")
    
    def create_stat_card(self, parent, title, value, color, index):
        """إنشاء بطاقة إحصائية"""
        card_frame = tk.Frame(parent, bg=color, relief='raised', bd=2)
        card_frame.grid(row=0, column=index, padx=10, pady=10, sticky='ew')
        parent.grid_columnconfigure(index, weight=1)
        
        value_label = tk.Label(
            card_frame,
            text=value,
            font=('Arial', 20, 'bold'),
            fg='white',
            bg=color
        )
        value_label.pack(pady=(10, 5))
        
        title_label = tk.Label(
            card_frame,
            text=title,
            font=('Arial', 12),
            fg='white',
            bg=color
        )
        title_label.pack(pady=(0, 10))
    
    def update_status(self, message):
        """تحديث رسالة شريط الحالة"""
        self.status_label.config(text=message)

    # دوال إدارة المشاريع
    def load_projects(self):
        """تحميل المشاريع من قاعدة البيانات"""
        try:
            # مسح البيانات القديمة
            for item in self.projects_tree.get_children():
                self.projects_tree.delete(item)

            # تحميل المشاريع
            query = """
                SELECT id, name, location, project_type, total_cost,
                       completion_percentage, status
                FROM projects
                ORDER BY id DESC
            """
            projects = sqlite_manager.execute_query(query)

            # إضافة المشاريع إلى الجدول
            for project in projects:
                status_text = {
                    'planning': 'تخطيط',
                    'in_progress': 'قيد التنفيذ',
                    'completed': 'مكتمل',
                    'on_hold': 'متوقف مؤقتاً'
                }.get(project['status'], project['status'])

                cost_text = f"{project['total_cost']:,.0f} ريال" if project['total_cost'] else "غير محدد"
                completion_text = f"{project['completion_percentage']:.1f}%" if project['completion_percentage'] else "0%"

                self.projects_tree.insert('', 'end', values=(
                    project['id'],
                    project['name'] or '',
                    project['location'] or '',
                    project['project_type'] or '',
                    cost_text,
                    completion_text,
                    status_text
                ))

            if hasattr(self, 'status_label'):
                self.update_status(f"تم تحميل {len(projects)} مشروع")

        except Exception as e:
            logger.error(f"خطأ في تحميل المشاريع: {e}")
            messagebox.showerror("خطأ", f"فشل في تحميل المشاريع:\n{str(e)}")

    def add_project(self):
        """إضافة مشروع جديد"""
        dialog = ProjectDialog(self.root, "إضافة مشروع جديد")
        result = dialog.run()

        if result:
            try:
                query = """
                    INSERT INTO projects (name, location, project_type, total_cost,
                                        completion_percentage, start_date, expected_end_date,
                                        status, description, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result['name'],
                    result['location'],
                    result['project_type'],
                    result['total_cost'],
                    result['completion_percentage'],
                    result['start_date'],
                    result['expected_end_date'],
                    result['status'],
                    result['description'],
                    self.user_data['id']
                ))

                messagebox.showinfo("نجح", "تم إضافة المشروع بنجاح")
                self.load_projects()
                self.load_statistics()  # تحديث الإحصائيات

            except Exception as e:
                logger.error(f"خطأ في إضافة المشروع: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة المشروع:\n{str(e)}")

    def edit_project(self):
        """تعديل المشروع المحدد"""
        selection = self.projects_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للتعديل")
            return

        item = self.projects_tree.item(selection[0])
        project_id = item['values'][0]

        # جلب بيانات المشروع
        try:
            query = """
                SELECT name, location, project_type, total_cost, completion_percentage,
                       start_date, expected_end_date, status, description
                FROM projects WHERE id = ?
            """
            result = sqlite_manager.execute_query(query, (project_id,))

            if result:
                project_data = dict(result[0])
                dialog = ProjectDialog(self.root, "تعديل المشروع", project_data)
                updated_data = dialog.run()

                if updated_data:
                    update_query = """
                        UPDATE projects
                        SET name=?, location=?, project_type=?, total_cost=?,
                            completion_percentage=?, start_date=?, expected_end_date=?,
                            status=?, description=?, updated_at=CURRENT_TIMESTAMP
                        WHERE id=?
                    """
                    sqlite_manager.execute_query(update_query, (
                        updated_data['name'],
                        updated_data['location'],
                        updated_data['project_type'],
                        updated_data['total_cost'],
                        updated_data['completion_percentage'],
                        updated_data['start_date'],
                        updated_data['expected_end_date'],
                        updated_data['status'],
                        updated_data['description'],
                        project_id
                    ))

                    messagebox.showinfo("نجح", "تم تحديث المشروع بنجاح")
                    self.load_projects()
                    self.load_statistics()

        except Exception as e:
            logger.error(f"خطأ في تعديل المشروع: {e}")
            messagebox.showerror("خطأ", f"فشل في تعديل المشروع:\n{str(e)}")

    def delete_project(self):
        """حذف المشروع المحدد"""
        selection = self.projects_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للحذف")
            return

        item = self.projects_tree.item(selection[0])
        project_id = item['values'][0]
        project_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المشروع '{project_name}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM projects WHERE id = ?", (project_id,))
                messagebox.showinfo("نجح", "تم حذف المشروع بنجاح")
                self.load_projects()
                self.load_statistics()

            except Exception as e:
                logger.error(f"خطأ في حذف المشروع: {e}")
                messagebox.showerror("خطأ", f"فشل في حذف المشروع:\n{str(e)}")

    def refresh_projects(self):
        """تحديث قائمة المشاريع"""
        self.load_projects()

    # دوال إدارة العملاء
    def load_customers(self):
        """تحميل العملاء من قاعدة البيانات"""
        try:
            # مسح البيانات القديمة
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            # تحميل العملاء
            query = """
                SELECT id, full_name, national_id, phone, email, customer_type
                FROM customers
                ORDER BY id DESC
            """
            customers = sqlite_manager.execute_query(query)

            # إضافة العملاء إلى الجدول
            for customer in customers:
                customer_type_text = 'شركة' if customer['customer_type'] == 'company' else 'فرد'

                self.customers_tree.insert('', 'end', values=(
                    customer['id'],
                    customer['full_name'] or '',
                    customer['national_id'] or '',
                    customer['phone'] or '',
                    customer['email'] or '',
                    customer_type_text
                ))

            if hasattr(self, 'status_label'):
                self.update_status(f"تم تحميل {len(customers)} عميل")

        except Exception as e:
            logger.error(f"خطأ في تحميل العملاء: {e}")
            messagebox.showerror("خطأ", f"فشل في تحميل العملاء:\n{str(e)}")

    def add_customer(self):
        """إضافة عميل جديد"""
        dialog = CustomerDialog(self.root, "إضافة عميل جديد")
        result = dialog.run()

        if result:
            try:
                query = """
                    INSERT INTO customers (full_name, national_id, phone, email, address, customer_type, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result['full_name'],
                    result['national_id'],
                    result['phone'],
                    result['email'],
                    result['address'],
                    result['customer_type'],
                    result['notes']
                ))

                messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
                self.load_customers()
                self.load_statistics()  # تحديث الإحصائيات

            except Exception as e:
                logger.error(f"خطأ في إضافة العميل: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة العميل:\n{str(e)}")

    def edit_customer(self):
        """تعديل العميل المحدد"""
        selection = self.customers_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للتعديل")
            return

        item = self.customers_tree.item(selection[0])
        customer_id = item['values'][0]

        # جلب بيانات العميل
        try:
            query = """
                SELECT full_name, national_id, phone, email, address, customer_type, notes
                FROM customers WHERE id = ?
            """
            result = sqlite_manager.execute_query(query, (customer_id,))

            if result:
                customer_data = dict(result[0])
                dialog = CustomerDialog(self.root, "تعديل العميل", customer_data)
                updated_data = dialog.run()

                if updated_data:
                    update_query = """
                        UPDATE customers
                        SET full_name=?, national_id=?, phone=?, email=?, address=?, customer_type=?, notes=?, updated_at=CURRENT_TIMESTAMP
                        WHERE id=?
                    """
                    sqlite_manager.execute_query(update_query, (
                        updated_data['full_name'],
                        updated_data['national_id'],
                        updated_data['phone'],
                        updated_data['email'],
                        updated_data['address'],
                        updated_data['customer_type'],
                        updated_data['notes'],
                        customer_id
                    ))

                    messagebox.showinfo("نجح", "تم تحديث العميل بنجاح")
                    self.load_customers()

        except Exception as e:
            logger.error(f"خطأ في تعديل العميل: {e}")
            messagebox.showerror("خطأ", f"فشل في تعديل العميل:\n{str(e)}")

    def delete_customer(self):
        """حذف العميل المحدد"""
        selection = self.customers_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للحذف")
            return

        item = self.customers_tree.item(selection[0])
        customer_id = item['values'][0]
        customer_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف العميل '{customer_name}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM customers WHERE id = ?", (customer_id,))
                messagebox.showinfo("نجح", "تم حذف العميل بنجاح")
                self.load_customers()
                self.load_statistics()

            except Exception as e:
                logger.error(f"خطأ في حذف العميل: {e}")
                messagebox.showerror("خطأ", f"فشل في حذف العميل:\n{str(e)}")

    def refresh_customers(self):
        """تحديث قائمة العملاء"""
        self.load_customers()

    # دوال إدارة الوحدات
    def load_units(self):
        """تحميل الوحدات من قاعدة البيانات"""
        try:
            # مسح البيانات القديمة
            for item in self.units_tree.get_children():
                self.units_tree.delete(item)

            # تحميل الوحدات مع بيانات المشروع
            query = """
                SELECT u.id, u.unit_number, p.name as project_name, u.floor_number,
                       u.unit_type, u.area, u.price, u.status
                FROM units u
                LEFT JOIN projects p ON u.project_id = p.id
                ORDER BY u.id DESC
            """
            units = sqlite_manager.execute_query(query)

            # إضافة الوحدات إلى الجدول
            for unit in units:
                status_text = {
                    'available': 'متاح',
                    'reserved': 'محجوز',
                    'sold': 'مباع',
                    'maintenance': 'تحت الصيانة'
                }.get(unit['status'], unit['status'])

                area_text = f"{unit['area']:.1f} م²" if unit['area'] else "غير محدد"
                price_text = f"{unit['price']:,.0f} ريال" if unit['price'] else "غير محدد"

                self.units_tree.insert('', 'end', values=(
                    unit['id'],
                    unit['unit_number'] or '',
                    unit['project_name'] or '',
                    unit['floor_number'] or '',
                    unit['unit_type'] or '',
                    area_text,
                    price_text,
                    status_text
                ))

            if hasattr(self, 'status_label'):
                self.update_status(f"تم تحميل {len(units)} وحدة")

        except Exception as e:
            logger.error(f"خطأ في تحميل الوحدات: {e}")
            messagebox.showerror("خطأ", f"فشل في تحميل الوحدات:\n{str(e)}")

    def refresh_units(self):
        """تحديث قائمة الوحدات"""
        self.load_units()

    # دوال التقارير
    def show_default_report(self):
        """عرض التقرير الافتراضي"""
        try:
            # جمع الإحصائيات
            projects_count = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM projects")[0]['count']
            active_projects = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM projects WHERE status = 'in_progress'")[0]['count']
            units_count = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM units")[0]['count']
            sold_units = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM units WHERE status = 'sold'")[0]['count']
            customers_count = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM customers")[0]['count']

            # إنشاء التقرير
            report = f"""
═══════════════════════════════════════════════════════════════════
                    تقرير شامل - نظام شركة رافع للتطوير العقاري
═══════════════════════════════════════════════════════════════════

📊 الإحصائيات العامة:
────────────────────────────────────────────────────────────────────

🏗️  المشاريع:
   • إجمالي المشاريع: {projects_count}
   • المشاريع النشطة: {active_projects}
   • المشاريع المكتملة: {projects_count - active_projects}

🏠 الوحدات:
   • إجمالي الوحدات: {units_count}
   • الوحدات المباعة: {sold_units}
   • الوحدات المتاحة: {units_count - sold_units}
   • نسبة المبيعات: {(sold_units/units_count*100) if units_count > 0 else 0:.1f}%

👥 العملاء:
   • إجمالي العملاء: {customers_count}

📅 تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
👤 المستخدم: {self.user_data['full_name']}

═══════════════════════════════════════════════════════════════════

💡 لإنشاء تقارير مفصلة، استخدم الأزرار أعلاه لاختيار نوع التقرير المطلوب.

            """

            # عرض التقرير
            self.report_text.delete('1.0', tk.END)
            self.report_text.insert('1.0', report)

        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير الافتراضي: {e}")

    def generate_projects_report(self):
        """إنشاء تقرير المشاريع"""
        try:
            # جلب بيانات المشاريع
            query = """
                SELECT name, location, project_type, total_cost, completion_percentage, status
                FROM projects
                ORDER BY id DESC
            """
            projects = sqlite_manager.execute_query(query)

            # إنشاء التقرير
            report = f"""
═══════════════════════════════════════════════════════════════════
                            تقرير المشاريع العقارية
═══════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
📊 إجمالي المشاريع: {len(projects)}

────────────────────────────────────────────────────────────────────
"""

            for i, project in enumerate(projects, 1):
                status_text = {
                    'planning': 'تخطيط',
                    'in_progress': 'قيد التنفيذ',
                    'completed': 'مكتمل',
                    'on_hold': 'متوقف مؤقتاً'
                }.get(project['status'], project['status'])

                cost_text = f"{project['total_cost']:,.0f} ريال" if project['total_cost'] else "غير محدد"
                completion_text = f"{project['completion_percentage']:.1f}%" if project['completion_percentage'] else "0%"

                report += f"""
{i}. {project['name']}
   📍 الموقع: {project['location'] or 'غير محدد'}
   🏗️  النوع: {project['project_type'] or 'غير محدد'}
   💰 التكلفة: {cost_text}
   📈 نسبة الإنجاز: {completion_text}
   📊 الحالة: {status_text}
   ────────────────────────────────────────────────────────────────────
"""

            # عرض التقرير
            self.report_text.delete('1.0', tk.END)
            self.report_text.insert('1.0', report)

        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير المشاريع: {e}")
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير المشاريع:\n{str(e)}")

    def generate_customers_report(self):
        """إنشاء تقرير العملاء"""
        try:
            # جلب بيانات العملاء
            query = """
                SELECT full_name, national_id, phone, email, customer_type
                FROM customers
                ORDER BY id DESC
            """
            customers = sqlite_manager.execute_query(query)

            # إنشاء التقرير
            report = f"""
═══════════════════════════════════════════════════════════════════
                              تقرير العملاء
═══════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
👥 إجمالي العملاء: {len(customers)}

────────────────────────────────────────────────────────────────────
"""

            for i, customer in enumerate(customers, 1):
                customer_type_text = 'شركة' if customer['customer_type'] == 'company' else 'فرد'

                report += f"""
{i}. {customer['full_name']}
   🆔 رقم الهوية: {customer['national_id'] or 'غير محدد'}
   📞 الهاتف: {customer['phone'] or 'غير محدد'}
   📧 البريد الإلكتروني: {customer['email'] or 'غير محدد'}
   👤 النوع: {customer_type_text}
   ────────────────────────────────────────────────────────────────────
"""

            # عرض التقرير
            self.report_text.delete('1.0', tk.END)
            self.report_text.insert('1.0', report)

        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير العملاء: {e}")
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير العملاء:\n{str(e)}")

    def generate_units_report(self):
        """إنشاء تقرير الوحدات"""
        try:
            # جلب بيانات الوحدات
            query = """
                SELECT u.unit_number, p.name as project_name, u.floor_number,
                       u.unit_type, u.area, u.price, u.status
                FROM units u
                LEFT JOIN projects p ON u.project_id = p.id
                ORDER BY u.id DESC
            """
            units = sqlite_manager.execute_query(query)

            # إنشاء التقرير
            report = f"""
═══════════════════════════════════════════════════════════════════
                            تقرير الوحدات السكنية
═══════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
🏠 إجمالي الوحدات: {len(units)}

────────────────────────────────────────────────────────────────────
"""

            for i, unit in enumerate(units, 1):
                status_text = {
                    'available': 'متاح',
                    'reserved': 'محجوز',
                    'sold': 'مباع',
                    'maintenance': 'تحت الصيانة'
                }.get(unit['status'], unit['status'])

                area_text = f"{unit['area']:.1f} م²" if unit['area'] else "غير محدد"
                price_text = f"{unit['price']:,.0f} ريال" if unit['price'] else "غير محدد"

                report += f"""
{i}. وحدة رقم {unit['unit_number']}
   🏗️  المشروع: {unit['project_name'] or 'غير محدد'}
   🏢 الطابق: {unit['floor_number'] or 'غير محدد'}
   🏠 النوع: {unit['unit_type'] or 'غير محدد'}
   📐 المساحة: {area_text}
   💰 السعر: {price_text}
   📊 الحالة: {status_text}
   ────────────────────────────────────────────────────────────────────
"""

            # عرض التقرير
            self.report_text.delete('1.0', tk.END)
            self.report_text.insert('1.0', report)

        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير الوحدات: {e}")
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير الوحدات:\n{str(e)}")

    def generate_comprehensive_report(self):
        """إنشاء تقرير شامل مفصل"""
        try:
            # جمع جميع الإحصائيات
            projects_stats = sqlite_manager.execute_query("""
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as active,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                    AVG(completion_percentage) as avg_completion,
                    SUM(total_cost) as total_cost
                FROM projects
            """)[0]

            units_stats = sqlite_manager.execute_query("""
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'sold' THEN 1 ELSE 0 END) as sold,
                    SUM(CASE WHEN status = 'available' THEN 1 ELSE 0 END) as available,
                    AVG(price) as avg_price,
                    SUM(price) as total_value
                FROM units
            """)[0]

            customers_stats = sqlite_manager.execute_query("""
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN customer_type = 'individual' THEN 1 ELSE 0 END) as individuals,
                    SUM(CASE WHEN customer_type = 'company' THEN 1 ELSE 0 END) as companies
                FROM customers
            """)[0]

            # إنشاء التقرير الشامل
            report = f"""
═══════════════════════════════════════════════════════════════════
                        التقرير الشامل المفصل
                    نظام شركة رافع للتطوير العقاري
═══════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
👤 المستخدم: {self.user_data['full_name']}

🏗️  إحصائيات المشاريع:
────────────────────────────────────────────────────────────────────
   • إجمالي المشاريع: {projects_stats['total']}
   • المشاريع النشطة: {projects_stats['active']}
   • المشاريع المكتملة: {projects_stats['completed']}
   • متوسط نسبة الإنجاز: {projects_stats['avg_completion']:.1f}%
   • إجمالي قيمة المشاريع: {projects_stats['total_cost']:,.0f} ريال

🏠 إحصائيات الوحدات:
────────────────────────────────────────────────────────────────────
   • إجمالي الوحدات: {units_stats['total']}
   • الوحدات المباعة: {units_stats['sold']}
   • الوحدات المتاحة: {units_stats['available']}
   • نسبة المبيعات: {(units_stats['sold']/units_stats['total']*100) if units_stats['total'] > 0 else 0:.1f}%
   • متوسط سعر الوحدة: {units_stats['avg_price']:,.0f} ريال
   • إجمالي قيمة الوحدات: {units_stats['total_value']:,.0f} ريال

👥 إحصائيات العملاء:
────────────────────────────────────────────────────────────────────
   • إجمالي العملاء: {customers_stats['total']}
   • العملاء الأفراد: {customers_stats['individuals']}
   • العملاء الشركات: {customers_stats['companies']}

📊 مؤشرات الأداء:
────────────────────────────────────────────────────────────────────
   • معدل إنجاز المشاريع: {(projects_stats['completed']/projects_stats['total']*100) if projects_stats['total'] > 0 else 0:.1f}%
   • معدل مبيعات الوحدات: {(units_stats['sold']/units_stats['total']*100) if units_stats['total'] > 0 else 0:.1f}%
   • متوسط الوحدات لكل مشروع: {(units_stats['total']/projects_stats['total']) if projects_stats['total'] > 0 else 0:.1f}

═══════════════════════════════════════════════════════════════════
                            انتهى التقرير
═══════════════════════════════════════════════════════════════════
            """

            # عرض التقرير
            self.report_text.delete('1.0', tk.END)
            self.report_text.insert('1.0', report)

        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير الشامل: {e}")
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير الشامل:\n{str(e)}")
    
    def logout(self):
        """تسجيل الخروج"""
        if messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟"):
            logger.info(f"تم تسجيل خروج المستخدم: {self.user_data['username']}")
            self.root.quit()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("نظام إدارة شركة رافع للتطوير العقاري")
    print("Rafea Real Estate Management System")
    print("=" * 60)
    
    # إعداد نظام السجلات
    setup_logger()
    
    try:
        # اختبار قاعدة البيانات
        if not sqlite_manager.test_connection():
            print("❌ فشل في الاتصال بقاعدة البيانات")
            print("يرجى تشغيل setup_sqlite.py أولاً")
            return 1
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # تشغيل نافذة تسجيل الدخول
        login_window = LoginWindow()
        user_data = login_window.run()
        
        if not user_data:
            print("تم إلغاء تسجيل الدخول")
            return 0
        
        # تشغيل التطبيق الرئيسي
        main_app = RafeaMainApp(user_data)
        main_app.run()
        
        return 0
        
    except Exception as e:
        logger.error(f"خطأ في تشغيل التطبيق: {e}")
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
