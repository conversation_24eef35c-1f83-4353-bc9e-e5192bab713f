@echo off
chcp 65001 > nul
title إعداد نظام شركة رافع للتطوير العقاري

echo ================================================
echo إعداد نظام شركة رافع للتطوير العقاري
echo Rafea Real Estate Development System Setup
echo ================================================
echo.

echo 🔍 التحقق من Python...
python --version > nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo يرجى تثبيت Python 3.8 أو أحدث من:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python متوفر
echo.

echo 📦 تثبيت المتطلبات...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    echo يرجى التحقق من اتصال الإنترنت وإعادة المحاولة
    pause
    exit /b 1
)

echo ✅ تم تثبيت المتطلبات بنجاح
echo.

echo 🏗️  إعداد قاعدة البيانات...
echo تأكد من تشغيل PostgreSQL قبل المتابعة
echo اضغط أي مفتاح للمتابعة أو Ctrl+C للإلغاء
pause > nul

python setup_database.py
if errorlevel 1 (
    echo ❌ فشل في إعداد قاعدة البيانات
    echo يرجى التحقق من:
    echo 1. تشغيل خدمة PostgreSQL
    echo 2. إعدادات الاتصال في config.py
    echo 3. صحة اسم المستخدم وكلمة المرور
    echo.
    pause
    exit /b 1
)

echo.
echo ================================================
echo ✅ تم إعداد النظام بنجاح!
echo ================================================
echo.
echo يمكنك الآن تشغيل التطبيق بإحدى الطرق التالية:
echo 1. تشغيل ملف start.bat
echo 2. تشغيل الأمر: python run.py
echo.
echo بيانات الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: admin123
echo.
echo ⚠️  يرجى تغيير كلمة المرور بعد تسجيل الدخول الأول
echo.
pause
