#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مربعات حوار محسنة للنظام
Enhanced Dialogs for the System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

class EnhancedDialog:
    """مربع حوار محسن"""
    
    def __init__(self, parent, title, fields):
        self.parent = parent
        self.title = title
        self.fields = fields
        self.result = None
        self.dialog = None
        self.entries = {}
        self.create_dialog()
    
    def create_dialog(self):
        """إنشاء مربع الحوار"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("500x400")
        self.dialog.configure(bg='white')
        self.dialog.resizable(False, False)
        
        # توسيط النافذة
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # حساب موقع النافذة
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
        
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='white', padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # العنوان
        title_label = tk.Label(main_frame, text=self.title, 
                              font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=(0, 20))
        
        # إطار الحقول
        fields_frame = tk.Frame(main_frame, bg='white')
        fields_frame.pack(fill='both', expand=True)
        
        # إنشاء الحقول
        for i, field_info in enumerate(self.fields):
            field_name = field_info[0]
            field_label = field_info[1]
            field_type = field_info[2] if len(field_info) > 2 else 'text'
            # تسمية الحقل
            label = tk.Label(fields_frame, text=field_label + ":", 
                           font=('Arial', 12), bg='white', fg='#34495e')
            label.grid(row=i, column=0, sticky='w', pady=10, padx=(0, 10))
            
            # حقل الإدخال
            if field_type == 'text':
                entry = tk.Entry(fields_frame, font=('Arial', 12), width=25, 
                               relief='solid', bd=1)
            elif field_type == 'number':
                entry = tk.Entry(fields_frame, font=('Arial', 12), width=25, 
                               relief='solid', bd=1)
            elif field_type == 'combo':
                entry = ttk.Combobox(fields_frame, font=('Arial', 12), width=23)
                if field_name == 'unit_type':
                    entry['values'] = ('apartment', 'villa', 'office', 'shop')
                elif field_name == 'status':
                    entry['values'] = ('active', 'inactive', 'completed')
                elif field_name == 'priority':
                    entry['values'] = ('high', 'medium', 'low')
                elif field_name == 'user_type':
                    entry['values'] = ('admin', 'manager', 'employee', 'viewer')
                elif field_name == 'specialty':
                    entry['values'] = ('أعمال عامة', 'كهرباء', 'سباكة', 'تكييف', 'دهانات', 'بلاط', 'نجارة')
                elif field_name == 'material_type':
                    entry['values'] = ('مواد بناء', 'حديد وصلب', 'أدوات صحية', 'كهربائيات', 'دهانات', 'بلاط وسيراميك')
                elif field_name == 'maintenance_type':
                    entry['values'] = ('صيانة دورية', 'إصلاح عطل', 'تنظيف', 'فحص', 'استبدال قطع')
            elif field_type == 'textarea':
                entry = tk.Text(fields_frame, font=('Arial', 12), width=25, height=3,
                              relief='solid', bd=1)
            else:
                entry = tk.Entry(fields_frame, font=('Arial', 12), width=25, 
                               relief='solid', bd=1)
            
            entry.grid(row=i, column=1, sticky='ew', pady=10)
            fields_frame.grid_columnconfigure(1, weight=1)
            
            self.entries[field_name] = entry
        
        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg='white')
        buttons_frame.pack(fill='x', pady=(20, 0))
        
        # زر الحفظ
        save_btn = tk.Button(buttons_frame, text="حفظ", bg='#27ae60', fg='white',
                           font=('Arial', 12, 'bold'), command=self.save_data,
                           width=12, height=2)
        save_btn.pack(side='right', padx=(10, 0))
        
        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="إلغاء", bg='#e74c3c', fg='white',
                             font=('Arial', 12, 'bold'), command=self.cancel,
                             width=12, height=2)
        cancel_btn.pack(side='right')
        
        # ربط Enter بالحفظ
        self.dialog.bind('<Return>', lambda e: self.save_data())
        self.dialog.bind('<Escape>', lambda e: self.cancel())
        
        # تركيز على أول حقل
        if self.entries:
            first_entry = list(self.entries.values())[0]
            first_entry.focus()
    
    def save_data(self):
        """حفظ البيانات"""
        self.result = {}
        
        for field_name, entry in self.entries.items():
            if isinstance(entry, tk.Text):
                value = entry.get('1.0', tk.END).strip()
            else:
                value = entry.get().strip()
            
            self.result[field_name] = value
        
        # التحقق من الحقول المطلوبة
        required_fields = []
        for field_info in self.fields:
            if len(field_info) > 3 and field_info[3]:
                required_fields.append(field_info[0])

        missing_fields = []
        for field_name in required_fields:
            if not self.result.get(field_name):
                field_label = None
                for field_info in self.fields:
                    if field_info[0] == field_name:
                        field_label = field_info[1]
                        break
                if field_label:
                    missing_fields.append(field_label)

        if missing_fields:
            messagebox.showerror("خطأ", f"يرجى ملء الحقول التالية:\n" + "\n".join(missing_fields))
            return
        
        self.dialog.destroy()
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

class ProjectDialog(EnhancedDialog):
    """مربع حوار المشاريع"""
    
    def __init__(self, parent, title):
        fields = [
            ('name', 'اسم المشروع', 'text', True),
            ('location', 'الموقع', 'text', True),
            ('budget', 'الميزانية (ريال)', 'number', False),
            ('status', 'الحالة', 'combo', False),
            ('start_date', 'تاريخ البداية (YYYY-MM-DD)', 'text', False),
            ('description', 'الوصف', 'textarea', False)
        ]
        super().__init__(parent, title, fields)

class CustomerDialog(EnhancedDialog):
    """مربع حوار العملاء"""
    
    def __init__(self, parent, title):
        fields = [
            ('name', 'اسم العميل', 'text', True),
            ('phone', 'رقم الهاتف', 'text', True),
            ('email', 'البريد الإلكتروني', 'text', False),
            ('address', 'العنوان', 'text', False),
            ('national_id', 'رقم الهوية', 'text', False)
        ]
        super().__init__(parent, title, fields)

class UnitDialog(EnhancedDialog):
    """مربع حوار الوحدات"""
    
    def __init__(self, parent, title):
        fields = [
            ('unit_number', 'رقم الوحدة', 'text', True),
            ('unit_type', 'نوع الوحدة', 'combo', True),
            ('area', 'المساحة (م²)', 'number', False),
            ('price', 'السعر (ريال)', 'number', False),
            ('description', 'الوصف', 'textarea', False)
        ]
        super().__init__(parent, title, fields)

class ContractDialog(EnhancedDialog):
    """مربع حوار العقود"""
    
    def __init__(self, parent, title):
        fields = [
            ('contract_number', 'رقم العقد', 'text', True),
            ('customer_id', 'رقم العميل', 'number', True),
            ('unit_id', 'رقم الوحدة', 'number', True),
            ('total_amount', 'المبلغ الإجمالي', 'number', True),
            ('paid_amount', 'المبلغ المدفوع', 'number', False),
            ('contract_type', 'نوع العقد (sale/reservation)', 'text', True),
            ('notes', 'ملاحظات', 'textarea', False)
        ]
        super().__init__(parent, title, fields)

class PaymentDialog(EnhancedDialog):
    """مربع حوار المدفوعات"""
    
    def __init__(self, parent, title):
        fields = [
            ('contract_id', 'رقم العقد', 'number', True),
            ('amount', 'المبلغ', 'number', True),
            ('payment_method', 'طريقة الدفع (cash/bank_transfer/check/card)', 'text', True),
            ('reference_number', 'رقم المرجع', 'text', False),
            ('notes', 'ملاحظات', 'textarea', False)
        ]
        super().__init__(parent, title, fields)

class TaskDialog(EnhancedDialog):
    """مربع حوار المهام"""
    
    def __init__(self, parent, title):
        fields = [
            ('task_title', 'عنوان المهمة', 'text', True),
            ('description', 'الوصف', 'textarea', True),
            ('assigned_to', 'المسؤول', 'text', True),
            ('priority', 'الأولوية', 'combo', False),
            ('due_date', 'تاريخ الاستحقاق (YYYY-MM-DD)', 'text', False)
        ]
        super().__init__(parent, title, fields)

class UserDialog(EnhancedDialog):
    """مربع حوار المستخدمين"""

    def __init__(self, parent, title):
        fields = [
            ('username', 'اسم المستخدم', 'text', True),
            ('password', 'كلمة المرور', 'text', True),
            ('full_name', 'الاسم الكامل', 'text', True),
            ('email', 'البريد الإلكتروني', 'text', False),
            ('user_type', 'نوع المستخدم', 'combo', True)
        ]
        super().__init__(parent, title, fields)

class ContractorDialog(EnhancedDialog):
    """مربع حوار المقاولين"""

    def __init__(self, parent, title):
        fields = [
            ('name', 'اسم المقاول', 'text', True),
            ('specialty', 'التخصص', 'combo', True),
            ('phone', 'رقم الهاتف', 'text', True),
            ('email', 'البريد الإلكتروني', 'text', False),
            ('address', 'العنوان', 'text', False),
            ('notes', 'ملاحظات', 'textarea', False)
        ]
        super().__init__(parent, title, fields)

class ExtractDialog(EnhancedDialog):
    """مربع حوار المستخلصات"""

    def __init__(self, parent, title):
        fields = [
            ('extract_number', 'رقم المستخلص', 'text', True),
            ('contractor', 'اسم المقاول', 'text', True),
            ('project', 'المشروع', 'text', True),
            ('total_amount', 'المبلغ الإجمالي', 'number', True),
            ('extract_date', 'تاريخ المستخلص (YYYY-MM-DD)', 'text', False),
            ('description', 'الوصف', 'textarea', False)
        ]
        super().__init__(parent, title, fields)

class SupplierDialog(EnhancedDialog):
    """مربع حوار الموردين"""

    def __init__(self, parent, title):
        fields = [
            ('name', 'اسم المورد', 'text', True),
            ('material_type', 'نوع المواد', 'combo', True),
            ('phone', 'رقم الهاتف', 'text', True),
            ('email', 'البريد الإلكتروني', 'text', False),
            ('address', 'العنوان', 'text', False),
            ('notes', 'ملاحظات', 'textarea', False)
        ]
        super().__init__(parent, title, fields)

class InvoiceDialog(EnhancedDialog):
    """مربع حوار الفواتير"""

    def __init__(self, parent, title):
        fields = [
            ('invoice_number', 'رقم الفاتورة', 'text', True),
            ('supplier', 'اسم المورد', 'text', True),
            ('total_amount', 'المبلغ الإجمالي', 'number', True),
            ('invoice_date', 'تاريخ الفاتورة (YYYY-MM-DD)', 'text', False),
            ('due_date', 'تاريخ الاستحقاق (YYYY-MM-DD)', 'text', False),
            ('description', 'الوصف', 'textarea', False)
        ]
        super().__init__(parent, title, fields)

class PurchaseRequestDialog(EnhancedDialog):
    """مربع حوار طلبات الشراء"""

    def __init__(self, parent, title):
        fields = [
            ('request_number', 'رقم الطلب', 'text', True),
            ('project', 'المشروع', 'text', True),
            ('materials', 'المواد المطلوبة', 'textarea', True),
            ('quantity', 'الكمية', 'number', True),
            ('estimated_cost', 'التكلفة المقدرة', 'number', False),
            ('notes', 'ملاحظات', 'textarea', False)
        ]
        super().__init__(parent, title, fields)

class MaintenanceTaskDialog(EnhancedDialog):
    """مربع حوار مهام الصيانة"""

    def __init__(self, parent, title):
        fields = [
            ('task_number', 'رقم المهمة', 'text', True),
            ('task_title', 'عنوان المهمة', 'text', True),
            ('maintenance_type', 'نوع الصيانة', 'combo', True),
            ('location', 'الموقع', 'text', True),
            ('technician', 'الفني المسؤول', 'text', True),
            ('cost', 'التكلفة المقدرة', 'number', False),
            ('description', 'الوصف', 'textarea', False)
        ]
        super().__init__(parent, title, fields)

class DailyTaskDialog(EnhancedDialog):
    """مربع حوار المهام اليومية"""

    def __init__(self, parent, title):
        fields = [
            ('task_title', 'عنوان المهمة', 'text', True),
            ('description', 'الوصف', 'textarea', True),
            ('assigned_to', 'المسؤول', 'text', True),
            ('priority', 'الأولوية', 'combo', False),
            ('due_date', 'تاريخ الاستحقاق (YYYY-MM-DD)', 'text', False)
        ]
        super().__init__(parent, title, fields)

def show_info_dialog(parent, title, message):
    """عرض مربع حوار معلومات محسن"""
    dialog = tk.Toplevel(parent)
    dialog.title(title)
    dialog.geometry("400x200")
    dialog.configure(bg='white')
    dialog.resizable(False, False)
    
    # توسيط النافذة
    dialog.transient(parent)
    dialog.grab_set()
    
    x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
    y = (dialog.winfo_screenheight() // 2) - (200 // 2)
    dialog.geometry(f"400x200+{x}+{y}")
    
    # إطار رئيسي
    main_frame = tk.Frame(dialog, bg='white', padx=20, pady=20)
    main_frame.pack(fill='both', expand=True)
    
    # العنوان
    title_label = tk.Label(main_frame, text=title, 
                          font=('Arial', 14, 'bold'), bg='white', fg='#2c3e50')
    title_label.pack(pady=(0, 15))
    
    # الرسالة
    message_label = tk.Label(main_frame, text=message, 
                           font=('Arial', 11), bg='white', fg='#34495e',
                           wraplength=350, justify='center')
    message_label.pack(pady=(0, 20))
    
    # زر موافق
    ok_btn = tk.Button(main_frame, text="موافق", bg='#3498db', fg='white',
                      font=('Arial', 12, 'bold'), command=dialog.destroy,
                      width=15, height=2)
    ok_btn.pack()
    
    # ربط Enter بإغلاق النافذة
    dialog.bind('<Return>', lambda e: dialog.destroy())
    dialog.bind('<Escape>', lambda e: dialog.destroy())
    
    ok_btn.focus()

def show_confirm_dialog(parent, title, message):
    """عرض مربع حوار تأكيد محسن"""
    dialog = tk.Toplevel(parent)
    dialog.title(title)
    dialog.geometry("450x200")
    dialog.configure(bg='white')
    dialog.resizable(False, False)
    
    # توسيط النافذة
    dialog.transient(parent)
    dialog.grab_set()
    
    x = (dialog.winfo_screenwidth() // 2) - (450 // 2)
    y = (dialog.winfo_screenheight() // 2) - (200 // 2)
    dialog.geometry(f"450x200+{x}+{y}")
    
    result = [False]  # استخدام قائمة للتمكن من التعديل
    
    # إطار رئيسي
    main_frame = tk.Frame(dialog, bg='white', padx=20, pady=20)
    main_frame.pack(fill='both', expand=True)
    
    # العنوان
    title_label = tk.Label(main_frame, text=title, 
                          font=('Arial', 14, 'bold'), bg='white', fg='#e74c3c')
    title_label.pack(pady=(0, 15))
    
    # الرسالة
    message_label = tk.Label(main_frame, text=message, 
                           font=('Arial', 11), bg='white', fg='#34495e',
                           wraplength=400, justify='center')
    message_label.pack(pady=(0, 20))
    
    # إطار الأزرار
    buttons_frame = tk.Frame(main_frame, bg='white')
    buttons_frame.pack()
    
    def confirm():
        result[0] = True
        dialog.destroy()
    
    def cancel():
        result[0] = False
        dialog.destroy()
    
    # زر نعم
    yes_btn = tk.Button(buttons_frame, text="نعم", bg='#27ae60', fg='white',
                       font=('Arial', 12, 'bold'), command=confirm,
                       width=12, height=2)
    yes_btn.pack(side='left', padx=(0, 10))
    
    # زر لا
    no_btn = tk.Button(buttons_frame, text="لا", bg='#e74c3c', fg='white',
                      font=('Arial', 12, 'bold'), command=cancel,
                      width=12, height=2)
    no_btn.pack(side='left')
    
    # ربط المفاتيح
    dialog.bind('<Return>', lambda e: confirm())
    dialog.bind('<Escape>', lambda e: cancel())
    
    yes_btn.focus()
    
    # انتظار إغلاق النافذة
    dialog.wait_window()
    
    return result[0]
