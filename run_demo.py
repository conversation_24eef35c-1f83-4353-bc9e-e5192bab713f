#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل تجريبي للتطبيق
Demo Run Script
"""

import sys
import os
from pathlib import Path
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_demo_user():
    """إنشاء مستخدم تجريبي"""
    return {
        'id': 1,
        'username': 'admin',
        'full_name': 'مدير النظام',
        'user_type': 'admin'
    }

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("تشغيل تجريبي لنظام شركة رافع للتطوير العقاري")
    print("Demo Run - Rafea Real Estate Management System")
    print("=" * 50)
    
    try:
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # إعداد خصائص التطبيق
        app.setApplicationName("نظام إدارة شركة رافع للتطوير العقاري")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("شركة رافع للتطوير العقاري")
        
        # إعداد الخط العربي
        arabic_fonts = [
            "Arial Unicode MS",
            "Tahoma", 
            "Segoe UI",
            "DejaVu Sans"
        ]
        
        for font_name in arabic_fonts:
            font = QFont(font_name, 10)
            if font.exactMatch():
                app.setFont(font)
                print(f"✅ تم تعيين الخط: {font_name}")
                break
        
        # إعداد دعم RTL
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد ستايل أساسي
        app.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            
            QMenuBar {
                background-color: #2c3e50;
                color: white;
                font-weight: bold;
            }
            
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
            }
            
            QMenuBar::item:selected {
                background-color: #34495e;
            }
            
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #2980b9;
            }
            
            QLineEdit, QTextEdit, QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                font-size: 12px;
            }
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border-color: #3498db;
            }
        """)
        
        print("✅ تم إعداد التطبيق بنجاح")
        
        # عرض رسالة ترحيب
        msg = QMessageBox()
        msg.setWindowTitle("مرحباً بك")
        msg.setText("مرحباً بك في نظام إدارة شركة رافع للتطوير العقاري")
        msg.setInformativeText("هذا تشغيل تجريبي للواجهة الرئيسية")
        msg.setIcon(QMessageBox.Information)
        msg.exec_()
        
        # تحميل النافذة الرئيسية
        try:
            from ui.main_window import MainWindow
            
            # إنشاء مستخدم تجريبي
            demo_user = create_demo_user()
            
            # إنشاء النافذة الرئيسية
            main_window = MainWindow(demo_user)
            main_window.show()
            
            print("✅ تم تحميل النافذة الرئيسية")
            
            # تشغيل حلقة الأحداث
            return app.exec_()
            
        except Exception as e:
            print(f"❌ خطأ في تحميل النافذة الرئيسية: {e}")
            
            # عرض رسالة خطأ
            error_msg = QMessageBox()
            error_msg.setWindowTitle("خطأ")
            error_msg.setText("حدث خطأ في تحميل النافذة الرئيسية")
            error_msg.setDetailedText(str(e))
            error_msg.setIcon(QMessageBox.Critical)
            error_msg.exec_()
            
            return 1
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
