#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية بالطريقة الصحيحة
Add Sample Data Correctly
"""

import sqlite3
import os
from datetime import datetime

def check_table_structure():
    """فحص بنية الجداول"""
    print("🔍 فحص بنية الجداول...")
    
    try:
        conn = sqlite3.connect('data/rafea_system.db')
        cursor = conn.cursor()
        
        # فحص بنية جدول المشاريع
        cursor.execute("PRAGMA table_info(projects)")
        projects_columns = cursor.fetchall()
        print("📋 أعمدة جدول المشاريع:")
        for col in projects_columns:
            print(f"   - {col[1]} ({col[2]})")
        
        # فحص بنية جدول العملاء
        cursor.execute("PRAGMA table_info(customers)")
        customers_columns = cursor.fetchall()
        print("📋 أعمدة جدول العملاء:")
        for col in customers_columns:
            print(f"   - {col[1]} ({col[2]})")
        
        # فحص بنية جدول الوحدات
        cursor.execute("PRAGMA table_info(units)")
        units_columns = cursor.fetchall()
        print("📋 أعمدة جدول الوحدات:")
        for col in units_columns:
            print(f"   - {col[1]} ({col[2]})")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص بنية الجداول: {e}")
        return False

def add_correct_sample_data():
    """إضافة بيانات تجريبية بالطريقة الصحيحة"""
    print("🔧 إضافة بيانات تجريبية بالطريقة الصحيحة...")
    
    try:
        conn = sqlite3.connect('data/rafea_system.db')
        cursor = conn.cursor()
        
        # إضافة مشاريع تجريبية (حسب البنية الفعلية)
        print("   📋 إضافة مشاريع تجريبية...")
        projects_data = [
            ('مشروع الواحة السكني', 'مشروع سكني متكامل', 'الرياض', '2024-01-01', '2025-12-31', 5000000, 'active'),
            ('مشروع النخيل التجاري', 'مشروع تجاري حديث', 'جدة', '2024-02-01', '2026-01-31', 8000000, 'active'),
            ('مشروع الياسمين', 'مشروع سكني اقتصادي', 'الدمام', '2023-06-01', '2024-12-31', 3000000, 'active')
        ]
        
        for project in projects_data:
            cursor.execute("""
                INSERT OR IGNORE INTO projects (name, description, location, start_date, end_date, budget, status)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, project)
        
        # إضافة عملاء تجريبيين (حسب البنية الفعلية)
        print("   👥 إضافة عملاء تجريبيين...")
        customers_data = [
            ('أحمد محمد السعيد', '1234567890', '0501234567', '<EMAIL>'),
            ('فاطمة علي الأحمد', '5555555555', '0509876543', '<EMAIL>'),
            ('محمد عبدالله الخالد', '7777777777', '0507777777', '<EMAIL>'),
            ('سارة أحمد المطيري', '8888888888', '0508888888', '<EMAIL>')
        ]
        
        for customer in customers_data:
            cursor.execute("""
                INSERT OR IGNORE INTO customers (name, phone, email, address)
                VALUES (?, ?, ?, ?)
            """, customer)
        
        # إضافة وحدات تجريبية (حسب البنية الفعلية)
        print("   🏠 إضافة وحدات تجريبية...")
        units_data = [
            ('A101', 'apartment', 120.5, 450000, 'available'),
            ('A102', 'apartment', 135.0, 520000, 'available'),
            ('A103', 'apartment', 110.0, 420000, 'available'),
            ('B201', 'apartment', 150.0, 600000, 'reserved'),
            ('B202', 'apartment', 140.0, 580000, 'available'),
            ('B203', 'apartment', 125.0, 500000, 'available'),
            ('C301', 'apartment', 160.0, 650000, 'available'),
            ('C302', 'apartment', 155.0, 630000, 'sold'),
            ('C303', 'apartment', 145.0, 590000, 'available'),
            ('D401', 'apartment', 100.0, 380000, 'available')
        ]
        
        for unit in units_data:
            cursor.execute("""
                INSERT OR IGNORE INTO units (unit_number, unit_type, area, price, status)
                VALUES (?, ?, ?, ?, ?)
            """, unit)
        
        # إضافة مقاولين تجريبيين (حسب البنية الفعلية)
        print("   👷 إضافة مقاولين تجريبيين...")
        contractors_data = [
            ('شركة البناء الحديث', '0501111111', '<EMAIL>', 'الرياض'),
            ('مؤسسة التشييد المتقدم', '0502222222', '<EMAIL>', 'جدة'),
            ('شركة التطوير العقاري', '0503333333', '<EMAIL>', 'الدمام')
        ]
        
        for contractor in contractors_data:
            cursor.execute("""
                INSERT OR IGNORE INTO contractors (name, phone, email, address)
                VALUES (?, ?, ?, ?)
            """, contractor)
        
        # إضافة موردين تجريبيين (حسب البنية الفعلية)
        print("   📦 إضافة موردين تجريبيين...")
        suppliers_data = [
            ('شركة مواد البناء', '0504444444', '<EMAIL>', 'الرياض'),
            ('مؤسسة الحديد والصلب', '0505555555', '<EMAIL>', 'جدة'),
            ('شركة الأدوات الصحية', '0506666666', '<EMAIL>', 'الدمام')
        ]
        
        for supplier in suppliers_data:
            cursor.execute("""
                INSERT OR IGNORE INTO suppliers (name, phone, email, address)
                VALUES (?, ?, ?, ?)
            """, supplier)
        
        # إضافة مستخلصات تجريبية
        print("   📄 إضافة مستخلصات تجريبية...")
        extracts_data = [
            ('EXT-001', 'شركة البناء الحديث', 1, 150000, '2024-01-15', 'pending', 'مستخلص أعمال البناء'),
            ('EXT-002', 'مؤسسة التشييد المتقدم', 2, 200000, '2024-02-01', 'approved', 'مستخلص أعمال الكهرباء'),
            ('EXT-003', 'شركة التطوير العقاري', 1, 180000, '2024-02-15', 'paid', 'مستخلص أعمال التشطيب')
        ]
        
        for extract in extracts_data:
            cursor.execute("""
                INSERT OR IGNORE INTO extracts (extract_number, contractor, project, 
                                              total_amount, extract_date, status, description)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, extract)
        
        # إضافة فواتير تجريبية
        print("   🧾 إضافة فواتير تجريبية...")
        invoices_data = [
            ('INV-001', 'شركة مواد البناء', 75000, '2024-01-10', '2024-02-10', 'pending', 'فاتورة مواد البناء'),
            ('INV-002', 'مؤسسة الحديد والصلب', 120000, '2024-01-20', '2024-02-20', 'paid', 'فاتورة حديد وصلب'),
            ('INV-003', 'شركة الأدوات الصحية', 45000, '2024-02-01', '2024-03-01', 'approved', 'فاتورة أدوات صحية')
        ]
        
        for invoice in invoices_data:
            cursor.execute("""
                INSERT OR IGNORE INTO invoices (invoice_number, supplier, total_amount, 
                                              invoice_date, due_date, status, description)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, invoice)
        
        # إضافة طلبات شراء تجريبية
        print("   🛒 إضافة طلبات شراء تجريبية...")
        purchases_data = [
            ('PUR-001', 1, 'أسمنت وحديد', 100, 50000, '2024-01-05', 'approved', 'طلب شراء مواد البناء'),
            ('PUR-002', 2, 'أدوات كهربائية', 50, 25000, '2024-01-15', 'pending', 'طلب شراء أدوات كهربائية'),
            ('PUR-003', 1, 'أدوات صحية', 30, 15000, '2024-02-01', 'completed', 'طلب شراء أدوات صحية')
        ]
        
        for purchase in purchases_data:
            cursor.execute("""
                INSERT OR IGNORE INTO purchase_requests (request_number, project, materials, 
                                                       quantity, estimated_cost, request_date, status, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, purchase)
        
        # إضافة مهام صيانة تجريبية
        print("   🔧 إضافة مهام صيانة تجريبية...")
        maintenance_data = [
            ('MAIN-001', 'إصلاح المصعد', 'صيانة', 'المبنى الأول', 'فريق الصيانة', '2024-01-10', 0, 'pending', 'إصلاح عطل في المصعد'),
            ('MAIN-002', 'فحص التكييف', 'فحص', 'المبنى الثاني', 'فني التكييف', '2024-01-15', 0, 'in_progress', 'فحص دوري للتكييف'),
            ('MAIN-003', 'تنظيف الخزانات', 'تنظيف', 'جميع المباني', 'شركة التنظيف', '2024-02-01', 0, 'completed', 'تنظيف دوري للخزانات')
        ]
        
        for maintenance in maintenance_data:
            cursor.execute("""
                INSERT OR IGNORE INTO maintenance_tasks (task_number, task_title, maintenance_type, 
                                                       location, technician, date, cost, status, description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, maintenance)
        
        # إضافة مهام يومية تجريبية
        print("   📋 إضافة مهام يومية تجريبية...")
        daily_tasks_data = [
            ('مراجعة التقارير اليومية', 'مراجعة جميع التقارير', 'المدير', 'high', '2024-01-20', 'pending'),
            ('متابعة المشاريع', 'متابعة تقدم المشاريع', 'مدير المشاريع', 'medium', '2024-01-21', 'in_progress'),
            ('اجتماع الفريق', 'اجتماع أسبوعي مع الفريق', 'جميع الموظفين', 'medium', '2024-01-22', 'completed'),
            ('مراجعة الميزانية', 'مراجعة ميزانية المشاريع', 'المحاسب', 'high', '2024-01-23', 'pending'),
            ('زيارة الموقع', 'زيارة ميدانية للمشروع', 'المهندس', 'medium', '2024-01-24', 'pending')
        ]
        
        for task in daily_tasks_data:
            cursor.execute("""
                INSERT OR IGNORE INTO daily_tasks (task_title, description, assigned_to, 
                                                 priority, due_date, status)
                VALUES (?, ?, ?, ?, ?, ?)
            """, task)
        
        # إضافة عقود تجريبية
        print("   📄 إضافة عقود تجريبية...")
        contracts_data = [
            ('CON-001', 1, 1, 450000, 100000, 350000, 'sale', 'active', '2024-01-10', 'عقد بيع الوحدة A101'),
            ('CON-002', 2, 4, 600000, 150000, 450000, 'reservation', 'active', '2024-01-15', 'عقد حجز الوحدة B201'),
            ('CON-003', 3, 8, 630000, 630000, 0, 'sale', 'completed', '2024-01-20', 'عقد بيع الوحدة C302')
        ]
        
        for contract in contracts_data:
            cursor.execute("""
                INSERT OR IGNORE INTO contracts (contract_number, customer_id, unit_id, total_amount, 
                                               paid_amount, remaining_amount, contract_type, status, 
                                               contract_date, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, contract)
        
        # إضافة مدفوعات تجريبية
        print("   💰 إضافة مدفوعات تجريبية...")
        payments_data = [
            (1, 100000, '2024-01-10', 'cash', 'CASH-001', 'دفعة أولى نقداً'),
            (2, 150000, '2024-01-15', 'bank_transfer', 'TRANS-001', 'دفعة حجز بالتحويل'),
            (3, 300000, '2024-01-20', 'bank_transfer', 'TRANS-002', 'دفعة أولى بالتحويل'),
            (3, 330000, '2024-02-01', 'check', 'CHECK-001', 'دفعة نهائية بالشيك')
        ]
        
        for payment in payments_data:
            cursor.execute("""
                INSERT OR IGNORE INTO payments (contract_id, amount, payment_date, payment_method, 
                                              reference_number, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            """, payment)
        
        conn.commit()
        conn.close()
        
        print("✅ تم إضافة جميع البيانات التجريبية بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات التجريبية: {e}")
        return False

def verify_data():
    """التحقق من البيانات المضافة"""
    print("🔍 التحقق من البيانات المضافة...")
    
    try:
        conn = sqlite3.connect('data/rafea_system.db')
        cursor = conn.cursor()
        
        tables_to_check = [
            'projects', 'customers', 'units', 'contractors', 'suppliers',
            'extracts', 'invoices', 'purchase_requests', 'maintenance_tasks',
            'daily_tasks', 'contracts', 'payments'
        ]
        
        total_records = 0
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                total_records += count
                print(f"   📊 {table}: {count} سجل")
            except Exception as e:
                print(f"   ❌ خطأ في جدول {table}: {e}")
        
        conn.close()
        
        print(f"\n📊 إجمالي السجلات: {total_records}")
        return total_records > 50
        
    except Exception as e:
        print(f"❌ خطأ في التحقق من البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🔧 إضافة بيانات تجريبية بالطريقة الصحيحة")
    print("=" * 70)
    
    # فحص بنية الجداول
    check_table_structure()
    
    # إضافة البيانات التجريبية
    print("\n🔧 إضافة البيانات التجريبية...")
    if add_correct_sample_data():
        # التحقق من البيانات
        print("\n🔍 التحقق من البيانات...")
        if verify_data():
            print("\n🎉 تم إضافة البيانات التجريبية بنجاح!")
            print("\n📋 الآن يمكنك:")
            print("1. تشغيل النظام: python rafea_complete_system.py")
            print("2. تسجيل الدخول: admin / admin123")
            print("3. ستجد البيانات في جميع الصفحات")
            print("4. يمكنك إضافة/تعديل/حذف البيانات")
        else:
            print("\n⚠️ هناك مشكلة في البيانات المضافة")
    else:
        print("\n❌ فشل في إضافة البيانات التجريبية")

if __name__ == "__main__":
    main()
