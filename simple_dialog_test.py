#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة حوار بسيطة مع أزرار مضمونة
Simple Dialog Test with Guaranteed Buttons
"""

import tkinter as tk
from tkinter import ttk, messagebox

class SimpleUserDialog:
    def __init__(self, parent):
        self.parent = parent
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("إضافة مستخدم جديد")
        self.dialog.geometry("500x400")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
        
        self.create_widgets()
        
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        
        # العنوان
        title_label = tk.Label(
            self.dialog,
            text="إضافة مستخدم جديد",
            font=('Arial', 16, 'bold'),
            bg='#f5f5f5',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # إطار الحقول
        fields_frame = tk.Frame(self.dialog, bg='white', relief='raised', bd=2)
        fields_frame.pack(fill='both', expand=True, padx=20, pady=(0, 20))
        
        # اسم المستخدم
        tk.Label(fields_frame, text="اسم المستخدم:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(20, 5), padx=20)
        self.username_entry = tk.Entry(fields_frame, font=('Arial', 12), width=40)
        self.username_entry.pack(pady=(0, 15), padx=20)
        
        # كلمة المرور
        tk.Label(fields_frame, text="كلمة المرور:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.password_entry = tk.Entry(fields_frame, font=('Arial', 12), width=40, show='*')
        self.password_entry.pack(pady=(0, 15), padx=20)
        
        # البريد الإلكتروني
        tk.Label(fields_frame, text="البريد الإلكتروني:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.email_entry = tk.Entry(fields_frame, font=('Arial', 12), width=40)
        self.email_entry.pack(pady=(0, 20), padx=20)
        
        # إطار الأزرار - هذا هو الجزء المهم
        buttons_frame = tk.Frame(self.dialog, bg='#f5f5f5', height=80)
        buttons_frame.pack(fill='x', side='bottom', padx=20, pady=20)
        buttons_frame.pack_propagate(False)  # منع تقليص الإطار
        
        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ المستخدم",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.save_user
        )
        save_btn.pack(side='right', padx=(10, 0), pady=10)
        
        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.cancel
        )
        cancel_btn.pack(side='right', padx=(10, 10), pady=10)
        
        # التأكد من ظهور الأزرار
        self.dialog.update()
        buttons_frame.update()
        save_btn.update()
        cancel_btn.update()
        
        print("✅ تم إنشاء الأزرار بنجاح!")
        print(f"📍 إطار الأزرار: {buttons_frame.winfo_width()}x{buttons_frame.winfo_height()}")
        print(f"📍 زر الحفظ: {save_btn.winfo_width()}x{save_btn.winfo_height()}")
        print(f"📍 زر الإلغاء: {cancel_btn.winfo_width()}x{cancel_btn.winfo_height()}")
        
    def save_user(self):
        """حفظ بيانات المستخدم"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        email = self.email_entry.get().strip()
        
        if not username:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
            return
            
        if not password:
            messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
            return
            
        messagebox.showinfo("نجح", f"تم حفظ المستخدم:\nاسم المستخدم: {username}\nالبريد: {email}")
        self.result = {'username': username, 'password': password, 'email': email}
        self.dialog.destroy()
        
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

def test_simple_dialog():
    """اختبار النافذة البسيطة"""
    root = tk.Tk()
    root.title("اختبار النافذة البسيطة")
    root.geometry("400x300")
    root.configure(bg='#ecf0f1')
    
    def open_dialog():
        dialog = SimpleUserDialog(root)
        root.wait_window(dialog.dialog)
        if dialog.result:
            print(f"النتيجة: {dialog.result}")
        else:
            print("تم الإلغاء")
    
    test_btn = tk.Button(
        root,
        text="اختبار نافذة المستخدم",
        font=('Arial', 16, 'bold'),
        bg='#3498db',
        fg='white',
        width=25,
        height=3,
        command=open_dialog
    )
    test_btn.pack(expand=True)
    
    instructions = tk.Label(
        root,
        text="اضغط على الزر لفتح نافذة إضافة مستخدم\nيجب أن تظهر أزرار الحفظ والإلغاء بوضوح",
        font=('Arial', 12),
        bg='#ecf0f1',
        fg='#2c3e50',
        justify='center'
    )
    instructions.pack(pady=20)
    
    print("🧪 اختبار النافذة البسيطة")
    print("اضغط على الزر لفتح نافذة الحوار")
    
    root.mainloop()

if __name__ == "__main__":
    test_simple_dialog()
