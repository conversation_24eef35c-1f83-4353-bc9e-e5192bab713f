# -*- coding: utf-8 -*-
"""
إدارة قاعدة البيانات لتطبيق شركة رافع للتطوير العقاري
Database Management for Rafea Real Estate Development Application
"""

import os
import sys
from datetime import datetime
from sqlalchemy import create_engine, text, event
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager
from loguru import logger
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

from config import DATABASE_CONFIG, get_database_url

# إعداد قاعدة النماذج
Base = declarative_base()

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self._setup_database()
    
    def _setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            # إنشاء قاعدة البيانات إذا لم تكن موجودة
            self._create_database_if_not_exists()
            
            # إنشاء محرك قاعدة البيانات
            self.engine = create_engine(
                get_database_url(),
                poolclass=QueuePool,
                pool_size=DATABASE_CONFIG['pool_size'],
                max_overflow=DATABASE_CONFIG['max_overflow'],
                pool_pre_ping=True,
                echo=False
            )
            
            # إعداد جلسة قاعدة البيانات
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # إضافة مستمع للأحداث
            event.listen(self.engine, 'connect', self._set_sqlite_pragma)
            
            logger.info("تم إعداد قاعدة البيانات بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في إعداد قاعدة البيانات: {e}")
            raise
    
    def _create_database_if_not_exists(self):
        """إنشاء قاعدة البيانات إذا لم تكن موجودة"""
        try:
            # إذا كانت SQLite، لا نحتاج لإنشاء قاعدة البيانات مسبقاً
            if DATABASE_CONFIG.get('type') == 'sqlite':
                logger.info("استخدام قاعدة بيانات SQLite")
                return

            # الاتصال بقاعدة البيانات الافتراضية (PostgreSQL)
            conn = psycopg2.connect(
                host=DATABASE_CONFIG['host'],
                port=DATABASE_CONFIG['port'],
                user=DATABASE_CONFIG['username'],
                password=DATABASE_CONFIG['password'],
                database='postgres'
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)

            cursor = conn.cursor()

            # التحقق من وجود قاعدة البيانات
            cursor.execute(
                "SELECT 1 FROM pg_catalog.pg_database WHERE datname = %s",
                (DATABASE_CONFIG['database'],)
            )

            if not cursor.fetchone():
                # إنشاء قاعدة البيانات
                cursor.execute(f"CREATE DATABASE {DATABASE_CONFIG['database']}")
                logger.info(f"تم إنشاء قاعدة البيانات: {DATABASE_CONFIG['database']}")

            cursor.close()
            conn.close()

        except Exception as e:
            logger.warning(f"تعذر إنشاء قاعدة البيانات: {e}")
    
    def _set_sqlite_pragma(self, dbapi_connection, connection_record):
        """إعداد خصائص SQLite (إذا تم استخدامها)"""
        if 'sqlite' in str(dbapi_connection):
            cursor = dbapi_connection.cursor()
            cursor.execute("PRAGMA foreign_keys=ON")
            cursor.close()
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            # اختيار ملف SQL المناسب
            if DATABASE_CONFIG.get('type') == 'sqlite':
                sql_file_path = os.path.join(os.path.dirname(__file__), 'database_design_sqlite.sql')
            else:
                sql_file_path = os.path.join(os.path.dirname(__file__), 'database_design.sql')

            if os.path.exists(sql_file_path):
                with open(sql_file_path, 'r', encoding='utf-8') as file:
                    sql_content = file.read()
                
                # تقسيم الاستعلامات وتنفيذها
                statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
                
                with self.get_session() as session:
                    for statement in statements:
                        if statement and not statement.startswith('--'):
                            try:
                                session.execute(text(statement))
                                session.commit()
                            except Exception as e:
                                if "already exists" not in str(e).lower():
                                    logger.warning(f"تحذير في تنفيذ الاستعلام: {e}")
                
                logger.info("تم إنشاء جداول قاعدة البيانات بنجاح")
            else:
                logger.error("ملف تصميم قاعدة البيانات غير موجود")
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء جداول قاعدة البيانات: {e}")
            raise
    
    @contextmanager
    def get_session(self):
        """الحصول على جلسة قاعدة البيانات"""
        session = self.SessionLocal()
        try:
            yield session
        except Exception as e:
            session.rollback()
            logger.error(f"خطأ في جلسة قاعدة البيانات: {e}")
            raise
        finally:
            session.close()
    
    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            with self.get_session() as session:
                session.execute(text("SELECT 1"))
                logger.info("تم اختبار الاتصال بقاعدة البيانات بنجاح")
                return True
        except Exception as e:
            logger.error(f"فشل في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def backup_database(self, backup_path=None):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            if not backup_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"backup_{DATABASE_CONFIG['database']}_{timestamp}.sql"
            
            # استخدام pg_dump لإنشاء النسخة الاحتياطية
            os.system(f"""pg_dump -h {DATABASE_CONFIG['host']} -p {DATABASE_CONFIG['port']} -U {DATABASE_CONFIG['username']} -d {DATABASE_CONFIG['database']} > {backup_path}""")
            
            logger.info(f"تم إنشاء النسخة الاحتياطية: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return None
    
    def restore_database(self, backup_path):
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                logger.error(f"ملف النسخة الاحتياطية غير موجود: {backup_path}")
                return False
            
            # استخدام psql لاستعادة النسخة الاحتياطية
            os.system(f"""psql -h {DATABASE_CONFIG['host']} -p {DATABASE_CONFIG['port']} -U {DATABASE_CONFIG['username']} -d {DATABASE_CONFIG['database']} < {backup_path}""")
            
            logger.info(f"تم استعادة قاعدة البيانات من: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في استعادة قاعدة البيانات: {e}")
            return False
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام مخصص"""
        try:
            with self.get_session() as session:
                result = session.execute(text(query), params or {})
                session.commit()
                return result.fetchall()
        except Exception as e:
            logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise
    
    def get_table_info(self, table_name):
        """الحصول على معلومات جدول معين"""
        try:
            query = """
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns
                WHERE table_name = :table_name
                ORDER BY ordinal_position
            """
            return self.execute_query(query, {'table_name': table_name})
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات الجدول {table_name}: {e}")
            return []
    
    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.engine:
            self.engine.dispose()
            logger.info("تم إغلاق الاتصال بقاعدة البيانات")

# إنشاء مثيل مدير قاعدة البيانات
db_manager = DatabaseManager()

def get_db_session():
    """دالة مساعدة للحصول على جلسة قاعدة البيانات"""
    return db_manager.get_session()

def init_database():
    """تهيئة قاعدة البيانات"""
    try:
        db_manager.create_tables()
        logger.info("تم تهيئة قاعدة البيانات بنجاح")
        return True
    except Exception as e:
        logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    # اختبار قاعدة البيانات
    if db_manager.test_connection():
        print("✅ الاتصال بقاعدة البيانات ناجح")
        init_database()
    else:
        print("❌ فشل في الاتصال بقاعدة البيانات")
        sys.exit(1)
