#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للمهام اليومية والصيانة
"""

def test_system():
    """اختبار النظام"""
    try:
        print("🧪 اختبار استيراد النظام...")
        import rafea_complete_system
        print("✅ النظام يستورد بنجاح")
        
        print("🧪 اختبار استيراد النوافذ...")
        from dialogs import MaintenanceTaskDialog, DailyTaskDialog
        print("✅ النوافذ تستورد بنجاح")
        
        print("🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    test_system()
