#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل تطبيق شركة رافع للتطوير العقاري
Run Script for Rafea Real Estate Development Application
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_requirements():
    """التحقق من المتطلبات"""
    try:
        import PyQt5
        import psycopg2
        import bcrypt
        import loguru
        import reportlab
        print("✅ جميع المتطلبات متوفرة")
        return True
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("يرجى تثبيت المتطلبات باستخدام: pip install -r requirements.txt")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("نظام إدارة شركة رافع للتطوير العقاري")
    print("Rafea Real Estate Development Management System")
    print("=" * 50)
    
    # التحقق من المتطلبات
    if not check_requirements():
        return 1
    
    # تشغيل التطبيق
    try:
        from main import main as app_main
        return app_main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
