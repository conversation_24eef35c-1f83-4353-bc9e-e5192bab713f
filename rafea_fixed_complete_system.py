#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة شركة رافع للتطوير العقاري - النسخة المحسنة والمصححة
Rafea Real Estate Development Management System - Enhanced and Fixed Version
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sqlite3
import bcrypt
import logging
from datetime import datetime
import os

# استيراد مربعات الحوار المحسنة
from rafea_enhanced_dialogs import (
    ProjectDialog, CustomerDialog, UnitDialog, ContractDialog,
    PaymentDialog, TaskDialog, UserDialog, ContractorDialog, ExtractDialog,
    SupplierDialog, InvoiceDialog, PurchaseRequestDialog, MaintenanceTaskDialog,
    DailyTaskDialog, show_info_dialog, show_confirm_dialog
)

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SQLiteManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_path='data/rafea_system.db'):
        self.db_path = db_path
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            conn.execute("PRAGMA foreign_keys = OFF")
            cursor = conn.execute(query, params or ())
            if query.strip().upper().startswith('SELECT'):
                result = cursor.fetchall()
                conn.close()
                return result
            else:
                conn.commit()
                rowcount = cursor.rowcount
                conn.close()
                return rowcount
        except Exception as e:
            if conn:
                conn.close()
            logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise

# إنشاء مثيل مدير قاعدة البيانات
sqlite_manager = SQLiteManager()

class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.user_data = None
        self.setup_window()
        self.create_widgets()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.root.title("تسجيل الدخول - نظام إدارة شركة رافع")
        self.root.geometry("500x400")
        self.root.configure(bg='#2c3e50')
        self.root.resizable(False, False)
        
        # توسيط النافذة
        x = (self.root.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.root.winfo_screenheight() // 2) - (400 // 2)
        self.root.geometry(f"500x400+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg='#2c3e50')
        main_frame.pack(fill='both', expand=True, padx=30, pady=30)
        
        # العنوان
        title_label = tk.Label(main_frame, text="نظام إدارة شركة رافع للتطوير العقاري", 
                              font=('Arial', 18, 'bold'), bg='#2c3e50', fg='white')
        title_label.pack(pady=(0, 30))
        
        # إطار تسجيل الدخول
        login_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=3)
        login_frame.pack(pady=20, fill='both', expand=True)
        
        # عنوان تسجيل الدخول
        login_title = tk.Label(login_frame, text="تسجيل الدخول", 
                              font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        login_title.pack(pady=30)
        
        # اسم المستخدم
        tk.Label(login_frame, text="اسم المستخدم:", bg='white', 
                font=('Arial', 14, 'bold')).pack(pady=(15, 5))
        self.username_entry = tk.Entry(login_frame, font=('Arial', 14), width=20,
                                      relief='solid', bd=2)
        self.username_entry.pack(pady=5, ipady=5)
        
        # كلمة المرور
        tk.Label(login_frame, text="كلمة المرور:", bg='white', 
                font=('Arial', 14, 'bold')).pack(pady=(20, 5))
        self.password_entry = tk.Entry(login_frame, font=('Arial', 14), width=20, 
                                      show='*', relief='solid', bd=2)
        self.password_entry.pack(pady=5, ipady=5)
        
        # زر تسجيل الدخول
        login_btn = tk.Button(login_frame, text="تسجيل الدخول", bg='#3498db', fg='white',
                             font=('Arial', 14, 'bold'), command=self.login, 
                             width=18, height=2, relief='raised', bd=3)
        login_btn.pack(pady=30)
        
        # معلومات تسجيل الدخول
        info_frame = tk.Frame(login_frame, bg='#ecf0f1', relief='sunken', bd=2)
        info_frame.pack(fill='x', padx=20, pady=(0, 20))
        
        info_label = tk.Label(info_frame, text="بيانات تسجيل الدخول الافتراضية:", 
                             bg='#ecf0f1', fg='#2c3e50', font=('Arial', 11, 'bold'))
        info_label.pack(pady=(10, 5))
        
        tk.Label(info_frame, text="اسم المستخدم: admin", 
                bg='#ecf0f1', fg='#34495e', font=('Arial', 10)).pack()
        tk.Label(info_frame, text="كلمة المرور: admin123", 
                bg='#ecf0f1', fg='#34495e', font=('Arial', 10)).pack(pady=(0, 10))
        
        # ربط Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda e: self.login())
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
        
        # إدراج البيانات الافتراضية
        self.username_entry.insert(0, "admin")
        self.password_entry.insert(0, "admin123")
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        if not username or not password:
            show_info_dialog(self.root, "خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        try:
            query = "SELECT id, username, password_hash, full_name, user_type, is_active FROM users WHERE username = ?"
            result = sqlite_manager.execute_query(query, (username,))
            
            if not result:
                show_info_dialog(self.root, "خطأ", "اسم المستخدم غير صحيح")
                return
            
            user = result[0]
            
            if not user['is_active']:
                show_info_dialog(self.root, "خطأ", "الحساب غير مفعل")
                return
            
            if bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
                self.user_data = {
                    'id': user['id'],
                    'username': user['username'],
                    'full_name': user['full_name'],
                    'user_type': user['user_type']
                }
                
                logger.info(f"تم تسجيل دخول المستخدم: {username}")
                show_info_dialog(self.root, "نجح", f"مرحباً {user['full_name']}\nتم تسجيل الدخول بنجاح!")
                self.root.destroy()
            else:
                show_info_dialog(self.root, "خطأ", "كلمة المرور غير صحيحة")
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل الدخول: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في تسجيل الدخول:\n{str(e)}")
    
    def run(self):
        """تشغيل نافذة تسجيل الدخول"""
        self.root.mainloop()
        return self.user_data

class RafeaEnhancedSystem:
    """النظام المحسن لإدارة شركة رافع"""
    
    def __init__(self, user_data):
        self.user_data = user_data
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.load_all_data()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title(f"نظام إدارة شركة رافع - {self.user_data['full_name']}")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#f5f5f5')
        self.root.state('zoomed')  # ملء الشاشة
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # شريط العنوان
        self.create_header()
        
        # التبويبات الرئيسية
        self.create_main_tabs()
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_header(self):
        """إنشاء شريط العنوان"""
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # العنوان الرئيسي
        title_label = tk.Label(header_frame, text="🏢 نظام إدارة شركة رافع للتطوير العقاري",
                              font=('Arial', 22, 'bold'), bg='#2c3e50', fg='white')
        title_label.pack(side='left', padx=30, pady=25)
        
        # معلومات المستخدم
        user_frame = tk.Frame(header_frame, bg='#2c3e50')
        user_frame.pack(side='right', padx=30, pady=25)
        
        user_label = tk.Label(user_frame, text=f"👤 مرحباً، {self.user_data['full_name']}",
                             font=('Arial', 14, 'bold'), bg='#2c3e50', fg='white')
        user_label.pack()
        
        time_label = tk.Label(user_frame, text=f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                             font=('Arial', 11), bg='#2c3e50', fg='#bdc3c7')
        time_label.pack()
        
        logout_btn = tk.Button(user_frame, text="🚪 تسجيل الخروج", bg='#e74c3c', fg='white',
                              font=('Arial', 11, 'bold'), command=self.logout,
                              relief='raised', bd=2)
        logout_btn.pack(pady=(8, 0))
    
    def create_main_tabs(self):
        """إنشاء التبويبات الرئيسية"""
        # إنشاء التبويبات
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=15, pady=15)
        
        # التبويبات
        self.create_dashboard_tab()
        self.create_projects_tab()
        self.create_customers_tab()
        self.create_units_tab()
        self.create_sales_tab()
        self.create_contractors_tab()
        self.create_suppliers_tab()
        self.create_purchases_tab()
        self.create_maintenance_tab()
        self.create_daily_tasks_tab()
        self.create_users_tab()
        self.create_reports_tab()
    
    def create_dashboard_tab(self):
        """إنشاء تبويب لوحة التحكم"""
        dashboard_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(dashboard_frame, text='📊 لوحة التحكم')
        
        # عنوان لوحة التحكم
        title_label = tk.Label(dashboard_frame, text="📊 لوحة التحكم الرئيسية", 
                              font=('Arial', 26, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=30)
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(dashboard_frame, bg='white')
        stats_frame.pack(fill='x', padx=40, pady=30)
        
        # بطاقات الإحصائيات
        self.create_stat_card(stats_frame, "المشاريع", "0", "#3498db", 0, 0)
        self.create_stat_card(stats_frame, "العملاء", "0", "#e74c3c", 0, 1)
        self.create_stat_card(stats_frame, "الوحدات", "0", "#f39c12", 0, 2)
        self.create_stat_card(stats_frame, "العقود", "0", "#27ae60", 0, 3)
        
        # منطقة الأنشطة الحديثة
        activities_label = tk.Label(dashboard_frame, text="📈 الأنشطة الحديثة والإحصائيات التفصيلية", 
                                   font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        activities_label.pack(pady=(40, 20))
        
        # إطار النص مع شريط التمرير
        text_frame = tk.Frame(dashboard_frame, bg='white')
        text_frame.pack(fill='both', expand=True, padx=40, pady=20)
        
        self.activities_text = tk.Text(text_frame, height=18, font=('Arial', 12), 
                                      bg='#f8f9fa', relief='sunken', bd=3,
                                      wrap='word')
        scrollbar = tk.Scrollbar(text_frame, orient='vertical', command=self.activities_text.yview)
        self.activities_text.configure(yscrollcommand=scrollbar.set)
        
        self.activities_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
    
    def create_stat_card(self, parent, title, value, color, row, col):
        """إنشاء بطاقة إحصائية"""
        card_frame = tk.Frame(parent, bg=color, relief='raised', bd=4)
        card_frame.grid(row=row, column=col, padx=20, pady=20, sticky='ew', 
                       ipadx=30, ipady=20)
        parent.grid_columnconfigure(col, weight=1)
        
        title_label = tk.Label(card_frame, text=title, font=('Arial', 16, 'bold'), 
                              bg=color, fg='white')
        title_label.pack(pady=(15, 8))
        
        value_label = tk.Label(card_frame, text=value, font=('Arial', 28, 'bold'), 
                              bg=color, fg='white')
        value_label.pack(pady=(0, 15))
        
        # حفظ مرجع للتحديث لاحقاً
        setattr(self, f"{title}_value_label", value_label)

    def create_projects_tab(self):
        """إنشاء تبويب المشاريع"""
        projects_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(projects_frame, text='🏗️ المشاريع')

        # عنوان الصفحة
        title_label = tk.Label(projects_frame, text="🏗️ إدارة المشاريع",
                              font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=20)

        # شريط الأدوات
        toolbar = tk.Frame(projects_frame, bg='#ecf0f1', relief='raised', bd=2)
        toolbar.pack(fill='x', padx=20, pady=10)

        tk.Button(toolbar, text="➕ إضافة مشروع", bg='#27ae60', fg='white',
                 font=('Arial', 12, 'bold'), command=self.add_project,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=10, pady=10)
        tk.Button(toolbar, text="✏️ تعديل مشروع", bg='#f39c12', fg='white',
                 font=('Arial', 12, 'bold'), command=self.edit_project,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)
        tk.Button(toolbar, text="🗑️ حذف مشروع", bg='#e74c3c', fg='white',
                 font=('Arial', 12, 'bold'), command=self.delete_project,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)
        tk.Button(toolbar, text="🔄 تحديث", bg='#3498db', fg='white',
                 font=('Arial', 12, 'bold'), command=self.load_projects,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)

        # إطار الجدول
        table_frame = tk.Frame(projects_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # جدول المشاريع
        columns = ('الرقم', 'اسم المشروع', 'الموقع', 'الميزانية', 'الحالة', 'تاريخ البداية', 'الوصف')
        self.projects_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=22)

        # تنسيق الأعمدة
        for col in columns:
            self.projects_tree.heading(col, text=col)
            if col == 'الرقم':
                self.projects_tree.column(col, width=80, anchor='center')
            elif col == 'الميزانية':
                self.projects_tree.column(col, width=120, anchor='e')
            else:
                self.projects_tree.column(col, width=150, anchor='w')

        # شريط التمرير
        projects_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.projects_tree.yview)
        self.projects_tree.configure(yscrollcommand=projects_scrollbar.set)

        self.projects_tree.pack(side='left', fill='both', expand=True)
        projects_scrollbar.pack(side='right', fill='y')

    def create_customers_tab(self):
        """إنشاء تبويب العملاء"""
        customers_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(customers_frame, text='👥 العملاء')

        # عنوان الصفحة
        title_label = tk.Label(customers_frame, text="👥 إدارة العملاء",
                              font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=20)

        # شريط الأدوات
        toolbar = tk.Frame(customers_frame, bg='#ecf0f1', relief='raised', bd=2)
        toolbar.pack(fill='x', padx=20, pady=10)

        tk.Button(toolbar, text="➕ إضافة عميل", bg='#27ae60', fg='white',
                 font=('Arial', 12, 'bold'), command=self.add_customer,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=10, pady=10)
        tk.Button(toolbar, text="✏️ تعديل عميل", bg='#f39c12', fg='white',
                 font=('Arial', 12, 'bold'), command=self.edit_customer,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)
        tk.Button(toolbar, text="🗑️ حذف عميل", bg='#e74c3c', fg='white',
                 font=('Arial', 12, 'bold'), command=self.delete_customer,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)
        tk.Button(toolbar, text="🔄 تحديث", bg='#3498db', fg='white',
                 font=('Arial', 12, 'bold'), command=self.load_customers,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)

        # إطار الجدول
        table_frame = tk.Frame(customers_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # جدول العملاء
        columns = ('الرقم', 'الاسم', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'الهوية الوطنية')
        self.customers_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=22)

        # تنسيق الأعمدة
        for col in columns:
            self.customers_tree.heading(col, text=col)
            if col == 'الرقم':
                self.customers_tree.column(col, width=80, anchor='center')
            elif col == 'الهاتف':
                self.customers_tree.column(col, width=120, anchor='center')
            else:
                self.customers_tree.column(col, width=180, anchor='w')

        # شريط التمرير
        customers_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=customers_scrollbar.set)

        self.customers_tree.pack(side='left', fill='both', expand=True)
        customers_scrollbar.pack(side='right', fill='y')

    def create_units_tab(self):
        """إنشاء تبويب الوحدات"""
        units_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(units_frame, text='🏠 الوحدات')

        # عنوان الصفحة
        title_label = tk.Label(units_frame, text="🏠 إدارة الوحدات السكنية",
                              font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=20)

        # شريط الأدوات
        toolbar = tk.Frame(units_frame, bg='#ecf0f1', relief='raised', bd=2)
        toolbar.pack(fill='x', padx=20, pady=10)

        tk.Button(toolbar, text="➕ إضافة وحدة", bg='#27ae60', fg='white',
                 font=('Arial', 12, 'bold'), command=self.add_unit,
                 relief='raised', bd=3, padx=15, pady=8).pack(side='left', padx=8, pady=10)
        tk.Button(toolbar, text="✏️ تعديل وحدة", bg='#f39c12', fg='white',
                 font=('Arial', 12, 'bold'), command=self.edit_unit,
                 relief='raised', bd=3, padx=15, pady=8).pack(side='left', padx=3, pady=10)
        tk.Button(toolbar, text="🗑️ حذف وحدة", bg='#e74c3c', fg='white',
                 font=('Arial', 12, 'bold'), command=self.delete_unit,
                 relief='raised', bd=3, padx=15, pady=8).pack(side='left', padx=3, pady=10)
        tk.Button(toolbar, text="📋 حجز وحدة", bg='#9b59b6', fg='white',
                 font=('Arial', 12, 'bold'), command=self.reserve_unit,
                 relief='raised', bd=3, padx=15, pady=8).pack(side='left', padx=3, pady=10)
        tk.Button(toolbar, text="💰 بيع وحدة", bg='#e67e22', fg='white',
                 font=('Arial', 12, 'bold'), command=self.sell_unit,
                 relief='raised', bd=3, padx=15, pady=8).pack(side='left', padx=3, pady=10)
        tk.Button(toolbar, text="🔄 تحديث", bg='#3498db', fg='white',
                 font=('Arial', 12, 'bold'), command=self.load_units,
                 relief='raised', bd=3, padx=15, pady=8).pack(side='left', padx=3, pady=10)

        # إطار الجدول
        table_frame = tk.Frame(units_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # جدول الوحدات
        columns = ('الرقم', 'رقم الوحدة', 'النوع', 'المساحة', 'السعر', 'الحالة', 'المشروع')
        self.units_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=22)

        # تنسيق الأعمدة
        for col in columns:
            self.units_tree.heading(col, text=col)
            if col == 'الرقم':
                self.units_tree.column(col, width=80, anchor='center')
            elif col in ['المساحة', 'السعر']:
                self.units_tree.column(col, width=120, anchor='e')
            elif col == 'الحالة':
                self.units_tree.column(col, width=100, anchor='center')
            else:
                self.units_tree.column(col, width=140, anchor='w')

        # شريط التمرير
        units_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.units_tree.yview)
        self.units_tree.configure(yscrollcommand=units_scrollbar.set)

        self.units_tree.pack(side='left', fill='both', expand=True)
        units_scrollbar.pack(side='right', fill='y')

    def create_sales_tab(self):
        """إنشاء تبويب المبيعات والعقود"""
        sales_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(sales_frame, text='💰 المبيعات والعقود')

        # عنوان الصفحة
        title_label = tk.Label(sales_frame, text="💰 إدارة المبيعات والعقود",
                              font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=20)

        # تبويبات فرعية
        sales_notebook = ttk.Notebook(sales_frame)
        sales_notebook.pack(fill='both', expand=True, padx=20, pady=10)

        # تبويب العقود
        contracts_frame = tk.Frame(sales_notebook, bg='white')
        sales_notebook.add(contracts_frame, text='📄 العقود')

        # شريط أدوات العقود
        contracts_toolbar = tk.Frame(contracts_frame, bg='#ecf0f1', relief='raised', bd=2)
        contracts_toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(contracts_toolbar, text="➕ إضافة عقد", bg='#27ae60', fg='white',
                 font=('Arial', 11, 'bold'), command=self.add_contract,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=8, pady=8)
        tk.Button(contracts_toolbar, text="🔄 تحديث", bg='#3498db', fg='white',
                 font=('Arial', 11, 'bold'), command=self.load_contracts,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=3, pady=8)

        # جدول العقود
        contracts_columns = ('الرقم', 'رقم العقد', 'العميل', 'الوحدة', 'المبلغ الإجمالي', 'المبلغ المدفوع', 'المتبقي', 'النوع', 'الحالة')
        self.contracts_tree = ttk.Treeview(contracts_frame, columns=contracts_columns, show='headings', height=18)

        for col in contracts_columns:
            self.contracts_tree.heading(col, text=col)
            if col == 'الرقم':
                self.contracts_tree.column(col, width=60, anchor='center')
            elif col in ['المبلغ الإجمالي', 'المبلغ المدفوع', 'المتبقي']:
                self.contracts_tree.column(col, width=120, anchor='e')
            else:
                self.contracts_tree.column(col, width=120, anchor='w')

        self.contracts_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # تبويب المدفوعات
        payments_frame = tk.Frame(sales_notebook, bg='white')
        sales_notebook.add(payments_frame, text='💳 المدفوعات')

        # شريط أدوات المدفوعات
        payments_toolbar = tk.Frame(payments_frame, bg='#ecf0f1', relief='raised', bd=2)
        payments_toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(payments_toolbar, text="➕ إضافة دفعة", bg='#27ae60', fg='white',
                 font=('Arial', 11, 'bold'), command=self.add_payment,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=8, pady=8)
        tk.Button(payments_toolbar, text="🔄 تحديث", bg='#3498db', fg='white',
                 font=('Arial', 11, 'bold'), command=self.load_payments,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=3, pady=8)

        # جدول المدفوعات
        payments_columns = ('الرقم', 'العقد', 'المبلغ', 'تاريخ الدفع', 'طريقة الدفع', 'رقم المرجع', 'ملاحظات')
        self.payments_tree = ttk.Treeview(payments_frame, columns=payments_columns, show='headings', height=18)

        for col in payments_columns:
            self.payments_tree.heading(col, text=col)
            if col == 'الرقم':
                self.payments_tree.column(col, width=60, anchor='center')
            elif col == 'المبلغ':
                self.payments_tree.column(col, width=120, anchor='e')
            else:
                self.payments_tree.column(col, width=140, anchor='w')

        self.payments_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_contractors_tab(self):
        """إنشاء تبويب المقاولين والمستخلصات"""
        contractors_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(contractors_frame, text='👷 المقاولين والمستخلصات')

        # عنوان الصفحة
        title_label = tk.Label(contractors_frame, text="👷 إدارة المقاولين والمستخلصات",
                              font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=20)

        # تبويبات فرعية
        contractors_notebook = ttk.Notebook(contractors_frame)
        contractors_notebook.pack(fill='both', expand=True, padx=20, pady=10)

        # تبويب المقاولين
        contractors_list_frame = tk.Frame(contractors_notebook, bg='white')
        contractors_notebook.add(contractors_list_frame, text='👷 المقاولين')

        # شريط أدوات المقاولين
        contractors_toolbar = tk.Frame(contractors_list_frame, bg='#ecf0f1', relief='raised', bd=2)
        contractors_toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(contractors_toolbar, text="➕ إضافة مقاول", bg='#27ae60', fg='white',
                 font=('Arial', 11, 'bold'), command=self.add_contractor,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=8, pady=8)
        tk.Button(contractors_toolbar, text="✏️ تعديل مقاول", bg='#f39c12', fg='white',
                 font=('Arial', 11, 'bold'), command=self.edit_contractor,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=3, pady=8)
        tk.Button(contractors_toolbar, text="🗑️ حذف مقاول", bg='#e74c3c', fg='white',
                 font=('Arial', 11, 'bold'), command=self.delete_contractor,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=3, pady=8)
        tk.Button(contractors_toolbar, text="🔄 تحديث", bg='#3498db', fg='white',
                 font=('Arial', 11, 'bold'), command=self.load_contractors,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=3, pady=8)

        # جدول المقاولين
        contractors_columns = ('الرقم', 'اسم المقاول', 'التخصص', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'الحالة')
        self.contractors_tree = ttk.Treeview(contractors_list_frame, columns=contractors_columns, show='headings', height=18)

        for col in contractors_columns:
            self.contractors_tree.heading(col, text=col)
            if col == 'الرقم':
                self.contractors_tree.column(col, width=60, anchor='center')
            else:
                self.contractors_tree.column(col, width=140, anchor='w')

        self.contractors_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # تبويب المستخلصات
        extracts_frame = tk.Frame(contractors_notebook, bg='white')
        contractors_notebook.add(extracts_frame, text='📄 المستخلصات')

        # شريط أدوات المستخلصات
        extracts_toolbar = tk.Frame(extracts_frame, bg='#ecf0f1', relief='raised', bd=2)
        extracts_toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(extracts_toolbar, text="➕ إضافة مستخلص", bg='#27ae60', fg='white',
                 font=('Arial', 11, 'bold'), command=self.add_extract,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=8, pady=8)
        tk.Button(extracts_toolbar, text="✏️ تعديل مستخلص", bg='#f39c12', fg='white',
                 font=('Arial', 11, 'bold'), command=self.edit_extract,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=3, pady=8)
        tk.Button(extracts_toolbar, text="🗑️ حذف مستخلص", bg='#e74c3c', fg='white',
                 font=('Arial', 11, 'bold'), command=self.delete_extract,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=3, pady=8)
        tk.Button(extracts_toolbar, text="🔄 تحديث", bg='#3498db', fg='white',
                 font=('Arial', 11, 'bold'), command=self.load_extracts,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=3, pady=8)

        # جدول المستخلصات
        extracts_columns = ('الرقم', 'رقم المستخلص', 'المقاول', 'المشروع', 'المبلغ', 'التاريخ', 'الحالة', 'الوصف')
        self.extracts_tree = ttk.Treeview(extracts_frame, columns=extracts_columns, show='headings', height=18)

        for col in extracts_columns:
            self.extracts_tree.heading(col, text=col)
            if col == 'الرقم':
                self.extracts_tree.column(col, width=60, anchor='center')
            elif col == 'المبلغ':
                self.extracts_tree.column(col, width=120, anchor='e')
            else:
                self.extracts_tree.column(col, width=120, anchor='w')

        self.extracts_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_suppliers_tab(self):
        """إنشاء تبويب الموردين والفواتير"""
        suppliers_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(suppliers_frame, text='📦 الموردين والفواتير')

        # عنوان الصفحة
        title_label = tk.Label(suppliers_frame, text="📦 إدارة الموردين والفواتير",
                              font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=20)

        # تبويبات فرعية
        suppliers_notebook = ttk.Notebook(suppliers_frame)
        suppliers_notebook.pack(fill='both', expand=True, padx=20, pady=10)

        # تبويب الموردين
        suppliers_list_frame = tk.Frame(suppliers_notebook, bg='white')
        suppliers_notebook.add(suppliers_list_frame, text='📦 الموردين')

        # شريط أدوات الموردين
        suppliers_toolbar = tk.Frame(suppliers_list_frame, bg='#ecf0f1', relief='raised', bd=2)
        suppliers_toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(suppliers_toolbar, text="➕ إضافة مورد", bg='#27ae60', fg='white',
                 font=('Arial', 11, 'bold'), command=self.add_supplier,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=8, pady=8)
        tk.Button(suppliers_toolbar, text="✏️ تعديل مورد", bg='#f39c12', fg='white',
                 font=('Arial', 11, 'bold'), command=self.edit_supplier,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=3, pady=8)
        tk.Button(suppliers_toolbar, text="🗑️ حذف مورد", bg='#e74c3c', fg='white',
                 font=('Arial', 11, 'bold'), command=self.delete_supplier,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=3, pady=8)
        tk.Button(suppliers_toolbar, text="🔄 تحديث", bg='#3498db', fg='white',
                 font=('Arial', 11, 'bold'), command=self.load_suppliers,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=3, pady=8)

        # جدول الموردين
        suppliers_columns = ('الرقم', 'اسم المورد', 'نوع المواد', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'الحالة')
        self.suppliers_tree = ttk.Treeview(suppliers_list_frame, columns=suppliers_columns, show='headings', height=18)

        for col in suppliers_columns:
            self.suppliers_tree.heading(col, text=col)
            if col == 'الرقم':
                self.suppliers_tree.column(col, width=60, anchor='center')
            else:
                self.suppliers_tree.column(col, width=140, anchor='w')

        self.suppliers_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # تبويب الفواتير
        invoices_frame = tk.Frame(suppliers_notebook, bg='white')
        suppliers_notebook.add(invoices_frame, text='🧾 الفواتير')

        # شريط أدوات الفواتير
        invoices_toolbar = tk.Frame(invoices_frame, bg='#ecf0f1', relief='raised', bd=2)
        invoices_toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(invoices_toolbar, text="➕ إضافة فاتورة", bg='#27ae60', fg='white',
                 font=('Arial', 11, 'bold'), command=self.add_invoice,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=8, pady=8)
        tk.Button(invoices_toolbar, text="✏️ تعديل فاتورة", bg='#f39c12', fg='white',
                 font=('Arial', 11, 'bold'), command=self.edit_invoice,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=3, pady=8)
        tk.Button(invoices_toolbar, text="🗑️ حذف فاتورة", bg='#e74c3c', fg='white',
                 font=('Arial', 11, 'bold'), command=self.delete_invoice,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=3, pady=8)
        tk.Button(invoices_toolbar, text="🔄 تحديث", bg='#3498db', fg='white',
                 font=('Arial', 11, 'bold'), command=self.load_invoices,
                 relief='raised', bd=3, padx=15, pady=6).pack(side='left', padx=3, pady=8)

        # جدول الفواتير
        invoices_columns = ('الرقم', 'رقم الفاتورة', 'المورد', 'المبلغ', 'تاريخ الفاتورة', 'تاريخ الاستحقاق', 'الحالة')
        self.invoices_tree = ttk.Treeview(invoices_frame, columns=invoices_columns, show='headings', height=18)

        for col in invoices_columns:
            self.invoices_tree.heading(col, text=col)
            if col == 'الرقم':
                self.invoices_tree.column(col, width=60, anchor='center')
            elif col == 'المبلغ':
                self.invoices_tree.column(col, width=120, anchor='e')
            else:
                self.invoices_tree.column(col, width=130, anchor='w')

        self.invoices_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_purchases_tab(self):
        """إنشاء تبويب المشتريات"""
        purchases_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(purchases_frame, text='🛒 المشتريات')

        # عنوان الصفحة
        title_label = tk.Label(purchases_frame, text="🛒 إدارة طلبات الشراء",
                              font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=20)

        # شريط الأدوات
        toolbar = tk.Frame(purchases_frame, bg='#ecf0f1', relief='raised', bd=2)
        toolbar.pack(fill='x', padx=20, pady=10)

        tk.Button(toolbar, text="➕ إضافة طلب شراء", bg='#27ae60', fg='white',
                 font=('Arial', 12, 'bold'), command=self.add_purchase_request,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=10, pady=10)
        tk.Button(toolbar, text="✏️ تعديل طلب", bg='#f39c12', fg='white',
                 font=('Arial', 12, 'bold'), command=self.edit_purchase_request,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)
        tk.Button(toolbar, text="🗑️ حذف طلب", bg='#e74c3c', fg='white',
                 font=('Arial', 12, 'bold'), command=self.delete_purchase_request,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)
        tk.Button(toolbar, text="🔄 تحديث", bg='#3498db', fg='white',
                 font=('Arial', 12, 'bold'), command=self.load_purchase_requests,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)

        # إطار الجدول
        table_frame = tk.Frame(purchases_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # جدول طلبات الشراء
        columns = ('الرقم', 'رقم الطلب', 'المشروع', 'المواد', 'الكمية', 'التكلفة المقدرة', 'تاريخ الطلب', 'الحالة')
        self.purchase_requests_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=22)

        for col in columns:
            self.purchase_requests_tree.heading(col, text=col)
            if col == 'الرقم':
                self.purchase_requests_tree.column(col, width=80, anchor='center')
            elif col in ['الكمية', 'التكلفة المقدرة']:
                self.purchase_requests_tree.column(col, width=120, anchor='e')
            else:
                self.purchase_requests_tree.column(col, width=150, anchor='w')

        # شريط التمرير
        purchases_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.purchase_requests_tree.yview)
        self.purchase_requests_tree.configure(yscrollcommand=purchases_scrollbar.set)

        self.purchase_requests_tree.pack(side='left', fill='both', expand=True)
        purchases_scrollbar.pack(side='right', fill='y')

    def create_maintenance_tab(self):
        """إنشاء تبويب الصيانة والتشغيل"""
        maintenance_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(maintenance_frame, text='🔧 الصيانة والتشغيل')

        # عنوان الصفحة
        title_label = tk.Label(maintenance_frame, text="🔧 إدارة الصيانة والتشغيل",
                              font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=20)

        # شريط الأدوات
        toolbar = tk.Frame(maintenance_frame, bg='#ecf0f1', relief='raised', bd=2)
        toolbar.pack(fill='x', padx=20, pady=10)

        tk.Button(toolbar, text="➕ إضافة مهمة صيانة", bg='#27ae60', fg='white',
                 font=('Arial', 12, 'bold'), command=self.add_maintenance_task,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=10, pady=10)
        tk.Button(toolbar, text="✏️ تعديل مهمة", bg='#f39c12', fg='white',
                 font=('Arial', 12, 'bold'), command=self.edit_maintenance_task,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)
        tk.Button(toolbar, text="🗑️ حذف مهمة", bg='#e74c3c', fg='white',
                 font=('Arial', 12, 'bold'), command=self.delete_maintenance_task,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)
        tk.Button(toolbar, text="🔄 تحديث", bg='#3498db', fg='white',
                 font=('Arial', 12, 'bold'), command=self.load_maintenance_tasks,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)

        # إطار الجدول
        table_frame = tk.Frame(maintenance_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # جدول مهام الصيانة
        columns = ('الرقم', 'رقم المهمة', 'عنوان المهمة', 'نوع الصيانة', 'الموقع', 'الفني المسؤول', 'التاريخ', 'التكلفة', 'الحالة')
        self.maintenance_tasks_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=22)

        for col in columns:
            self.maintenance_tasks_tree.heading(col, text=col)
            if col == 'الرقم':
                self.maintenance_tasks_tree.column(col, width=80, anchor='center')
            elif col == 'التكلفة':
                self.maintenance_tasks_tree.column(col, width=120, anchor='e')
            else:
                self.maintenance_tasks_tree.column(col, width=140, anchor='w')

        # شريط التمرير
        maintenance_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.maintenance_tasks_tree.yview)
        self.maintenance_tasks_tree.configure(yscrollcommand=maintenance_scrollbar.set)

        self.maintenance_tasks_tree.pack(side='left', fill='both', expand=True)
        maintenance_scrollbar.pack(side='right', fill='y')

    def create_daily_tasks_tab(self):
        """إنشاء تبويب المهام اليومية"""
        tasks_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(tasks_frame, text='📋 المهام اليومية')

        # عنوان الصفحة
        title_label = tk.Label(tasks_frame, text="📋 إدارة المهام اليومية",
                              font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=20)

        # شريط الأدوات
        toolbar = tk.Frame(tasks_frame, bg='#ecf0f1', relief='raised', bd=2)
        toolbar.pack(fill='x', padx=20, pady=10)

        tk.Button(toolbar, text="➕ إضافة مهمة", bg='#27ae60', fg='white',
                 font=('Arial', 12, 'bold'), command=self.add_daily_task,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=10, pady=10)
        tk.Button(toolbar, text="✏️ تعديل مهمة", bg='#f39c12', fg='white',
                 font=('Arial', 12, 'bold'), command=self.edit_daily_task,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)
        tk.Button(toolbar, text="🗑️ حذف مهمة", bg='#e74c3c', fg='white',
                 font=('Arial', 12, 'bold'), command=self.delete_daily_task,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)
        tk.Button(toolbar, text="✅ إكمال مهمة", bg='#9b59b6', fg='white',
                 font=('Arial', 12, 'bold'), command=self.complete_daily_task,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)
        tk.Button(toolbar, text="🔄 تحديث", bg='#3498db', fg='white',
                 font=('Arial', 12, 'bold'), command=self.load_daily_tasks,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)

        # إطار الجدول
        table_frame = tk.Frame(tasks_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # جدول المهام اليومية
        columns = ('الرقم', 'عنوان المهمة', 'الوصف', 'المسؤول', 'الأولوية', 'تاريخ الاستحقاق', 'الحالة', 'تاريخ الإنشاء')
        self.daily_tasks_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=22)

        for col in columns:
            self.daily_tasks_tree.heading(col, text=col)
            if col == 'الرقم':
                self.daily_tasks_tree.column(col, width=80, anchor='center')
            elif col in ['الأولوية', 'الحالة']:
                self.daily_tasks_tree.column(col, width=100, anchor='center')
            else:
                self.daily_tasks_tree.column(col, width=150, anchor='w')

        # شريط التمرير
        tasks_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.daily_tasks_tree.yview)
        self.daily_tasks_tree.configure(yscrollcommand=tasks_scrollbar.set)

        self.daily_tasks_tree.pack(side='left', fill='both', expand=True)
        tasks_scrollbar.pack(side='right', fill='y')

    def create_users_tab(self):
        """إنشاء تبويب المستخدمين"""
        users_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(users_frame, text='👤 المستخدمين')

        # عنوان الصفحة
        title_label = tk.Label(users_frame, text="👤 إدارة المستخدمين والصلاحيات",
                              font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=20)

        # شريط الأدوات
        toolbar = tk.Frame(users_frame, bg='#ecf0f1', relief='raised', bd=2)
        toolbar.pack(fill='x', padx=20, pady=10)

        tk.Button(toolbar, text="➕ إضافة مستخدم", bg='#27ae60', fg='white',
                 font=('Arial', 12, 'bold'), command=self.add_user,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=10, pady=10)
        tk.Button(toolbar, text="✏️ تعديل مستخدم", bg='#f39c12', fg='white',
                 font=('Arial', 12, 'bold'), command=self.edit_user,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)
        tk.Button(toolbar, text="🗑️ حذف مستخدم", bg='#e74c3c', fg='white',
                 font=('Arial', 12, 'bold'), command=self.delete_user,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)
        tk.Button(toolbar, text="🔒 تغيير كلمة المرور", bg='#9b59b6', fg='white',
                 font=('Arial', 12, 'bold'), command=self.change_password,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)
        tk.Button(toolbar, text="🔄 تحديث", bg='#3498db', fg='white',
                 font=('Arial', 12, 'bold'), command=self.load_users,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)

        # إطار الجدول
        table_frame = tk.Frame(users_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # جدول المستخدمين
        columns = ('الرقم', 'اسم المستخدم', 'الاسم الكامل', 'البريد الإلكتروني', 'نوع المستخدم', 'الحالة', 'تاريخ الإنشاء')
        self.users_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=22)

        for col in columns:
            self.users_tree.heading(col, text=col)
            if col == 'الرقم':
                self.users_tree.column(col, width=80, anchor='center')
            elif col in ['نوع المستخدم', 'الحالة']:
                self.users_tree.column(col, width=120, anchor='center')
            else:
                self.users_tree.column(col, width=150, anchor='w')

        # شريط التمرير
        users_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=users_scrollbar.set)

        self.users_tree.pack(side='left', fill='both', expand=True)
        users_scrollbar.pack(side='right', fill='y')

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        reports_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(reports_frame, text='📊 التقارير')

        # عنوان التقارير
        title_label = tk.Label(reports_frame, text="📊 التقارير والإحصائيات التفصيلية",
                              font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=20)

        # أزرار التقارير
        reports_toolbar = tk.Frame(reports_frame, bg='#ecf0f1', relief='raised', bd=2)
        reports_toolbar.pack(fill='x', padx=20, pady=10)

        tk.Button(reports_toolbar, text="📋 تقرير المشاريع", bg='#3498db', fg='white',
                 font=('Arial', 12, 'bold'), command=self.generate_projects_report,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=10, pady=10)
        tk.Button(reports_toolbar, text="💰 تقرير المبيعات", bg='#27ae60', fg='white',
                 font=('Arial', 12, 'bold'), command=self.generate_sales_report,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)
        tk.Button(reports_toolbar, text="📊 التقرير المالي", bg='#f39c12', fg='white',
                 font=('Arial', 12, 'bold'), command=self.generate_financial_report,
                 relief='raised', bd=3, padx=20, pady=8).pack(side='left', padx=5, pady=10)

        # منطقة عرض التقارير
        text_frame = tk.Frame(reports_frame, bg='white')
        text_frame.pack(fill='both', expand=True, padx=20, pady=10)

        self.reports_text = tk.Text(text_frame, height=25, font=('Arial', 12),
                                   bg='#f8f9fa', relief='sunken', bd=3, wrap='word')
        reports_scrollbar = tk.Scrollbar(text_frame, orient='vertical', command=self.reports_text.yview)
        self.reports_text.configure(yscrollcommand=reports_scrollbar.set)

        self.reports_text.pack(side='left', fill='both', expand=True)
        reports_scrollbar.pack(side='right', fill='y')

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = tk.Frame(self.root, bg='#34495e', height=35)
        self.status_frame.pack(fill='x', side='bottom')
        self.status_frame.pack_propagate(False)

        self.status_label = tk.Label(self.status_frame, text="🟢 النظام جاهز",
                                    bg='#34495e', fg='white', font=('Arial', 11, 'bold'))
        self.status_label.pack(side='left', padx=15, pady=8)

        # معلومات إضافية
        info_label = tk.Label(self.status_frame,
                             text=f"📅 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                             bg='#34495e', fg='#bdc3c7', font=('Arial', 10))
        info_label.pack(side='right', padx=15, pady=8)

    # دوال تحميل البيانات
    def load_all_data(self):
        """تحميل جميع البيانات"""
        try:
            self.update_status("🔄 جاري تحميل البيانات...")

            self.load_projects()
            self.load_customers()
            self.load_units()
            self.load_contracts()
            self.load_payments()
            self.load_contractors()
            self.load_extracts()
            self.load_suppliers()
            self.load_invoices()
            self.load_purchase_requests()
            self.load_maintenance_tasks()
            self.load_daily_tasks()
            self.load_users()
            self.update_dashboard()

            self.update_status("✅ تم تحميل جميع البيانات بنجاح")
        except Exception as e:
            logger.error(f"خطأ في تحميل البيانات: {e}")
            self.update_status(f"❌ خطأ في تحميل البيانات: {str(e)}")

    def load_projects(self):
        """تحميل المشاريع"""
        try:
            # مسح البيانات القديمة
            for item in self.projects_tree.get_children():
                self.projects_tree.delete(item)

            # تحميل المشاريع
            query = "SELECT id, name, location, budget, status, start_date, description FROM projects ORDER BY id DESC"
            projects = sqlite_manager.execute_query(query)

            for project in projects:
                budget_text = f"{project['budget']:,.0f} ريال" if project['budget'] else "غير محدد"
                status_text = {
                    'active': '🟢 نشط',
                    'inactive': '🔴 غير نشط',
                    'completed': '✅ مكتمل'
                }.get(project['status'], project['status'] or 'غير محدد')

                self.projects_tree.insert('', 'end', values=(
                    project['id'],
                    project['name'] or '',
                    project['location'] or '',
                    budget_text,
                    status_text,
                    project['start_date'] or '',
                    project['description'] or ''
                ))

            self.update_status(f"📋 تم تحميل {len(projects)} مشروع")

        except Exception as e:
            logger.error(f"خطأ في تحميل المشاريع: {e}")
            self.update_status(f"❌ خطأ في تحميل المشاريع: {str(e)}")

    def load_customers(self):
        """تحميل العملاء"""
        try:
            # مسح البيانات القديمة
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            # تحميل العملاء
            query = "SELECT id, name, phone, email, address, national_id FROM customers ORDER BY id DESC"
            customers = sqlite_manager.execute_query(query)

            for customer in customers:
                self.customers_tree.insert('', 'end', values=(
                    customer['id'],
                    customer['name'] or '',
                    customer['phone'] or '',
                    customer['email'] or '',
                    customer['address'] or '',
                    customer['national_id'] or ''
                ))

            self.update_status(f"👥 تم تحميل {len(customers)} عميل")

        except Exception as e:
            logger.error(f"خطأ في تحميل العملاء: {e}")
            self.update_status(f"❌ خطأ في تحميل العملاء: {str(e)}")

    def load_units(self):
        """تحميل الوحدات"""
        try:
            # مسح البيانات القديمة
            for item in self.units_tree.get_children():
                self.units_tree.delete(item)

            # تحميل الوحدات
            query = """
                SELECT u.id, u.unit_number, u.unit_type, u.area, u.price, u.status,
                       COALESCE(p.name, 'غير محدد') as project_name
                FROM units u
                LEFT JOIN projects p ON u.project_id = p.id
                ORDER BY u.id DESC
            """
            units = sqlite_manager.execute_query(query)

            for unit in units:
                area_text = f"{unit['area']:.1f} م²" if unit['area'] else "غير محدد"
                price_text = f"{unit['price']:,.0f} ريال" if unit['price'] else "غير محدد"

                status_text = {
                    'available': '🟢 متاح',
                    'reserved': '🟡 محجوز',
                    'sold': '🔴 مباع'
                }.get(unit['status'], unit['status'] or 'غير محدد')

                unit_type_text = {
                    'apartment': '🏠 شقة',
                    'villa': '🏘️ فيلا',
                    'office': '🏢 مكتب',
                    'shop': '🏪 محل'
                }.get(unit['unit_type'], unit['unit_type'] or 'غير محدد')

                self.units_tree.insert('', 'end', values=(
                    unit['id'],
                    unit['unit_number'] or '',
                    unit_type_text,
                    area_text,
                    price_text,
                    status_text,
                    unit['project_name']
                ))

            self.update_status(f"🏠 تم تحميل {len(units)} وحدة")

        except Exception as e:
            logger.error(f"خطأ في تحميل الوحدات: {e}")
            self.update_status(f"❌ خطأ في تحميل الوحدات: {str(e)}")

    def load_contracts(self):
        """تحميل العقود"""
        try:
            # مسح البيانات القديمة
            for item in self.contracts_tree.get_children():
                self.contracts_tree.delete(item)

            # تحميل العقود
            query = """
                SELECT c.id, c.contract_number,
                       COALESCE(cu.name, 'غير محدد') as customer_name,
                       COALESCE(u.unit_number, 'غير محدد') as unit_number,
                       c.total_amount, c.paid_amount, c.remaining_amount,
                       c.contract_type, c.status
                FROM contracts c
                LEFT JOIN customers cu ON c.customer_id = cu.id
                LEFT JOIN units u ON c.unit_id = u.id
                ORDER BY c.id DESC
            """
            contracts = sqlite_manager.execute_query(query)

            for contract in contracts:
                total_text = f"{contract['total_amount']:,.0f} ريال" if contract['total_amount'] else "0"
                paid_text = f"{contract['paid_amount']:,.0f} ريال" if contract['paid_amount'] else "0"
                remaining_text = f"{contract['remaining_amount']:,.0f} ريال" if contract['remaining_amount'] else "0"

                type_text = {
                    'sale': '💰 بيع',
                    'reservation': '📋 حجز'
                }.get(contract['contract_type'], contract['contract_type'] or 'غير محدد')

                status_text = {
                    'active': '🟢 نشط',
                    'completed': '✅ مكتمل',
                    'cancelled': '❌ ملغي'
                }.get(contract['status'], contract['status'] or 'غير محدد')

                self.contracts_tree.insert('', 'end', values=(
                    contract['id'],
                    contract['contract_number'] or '',
                    contract['customer_name'],
                    contract['unit_number'],
                    total_text,
                    paid_text,
                    remaining_text,
                    type_text,
                    status_text
                ))

            self.update_status(f"📄 تم تحميل {len(contracts)} عقد")

        except Exception as e:
            logger.error(f"خطأ في تحميل العقود: {e}")
            self.update_status(f"❌ خطأ في تحميل العقود: {str(e)}")

    def load_payments(self):
        """تحميل المدفوعات"""
        try:
            # مسح البيانات القديمة
            for item in self.payments_tree.get_children():
                self.payments_tree.delete(item)

            # تحميل المدفوعات
            query = """
                SELECT p.id, c.contract_number, p.amount, p.payment_date,
                       p.payment_method, p.reference_number, p.notes
                FROM payments p
                LEFT JOIN contracts c ON p.contract_id = c.id
                ORDER BY p.id DESC
            """
            payments = sqlite_manager.execute_query(query)

            for payment in payments:
                amount_text = f"{payment['amount']:,.0f} ريال" if payment['amount'] else "0"

                method_text = {
                    'cash': '💵 نقد',
                    'bank_transfer': '🏦 تحويل بنكي',
                    'check': '📝 شيك',
                    'card': '💳 بطاقة'
                }.get(payment['payment_method'], payment['payment_method'] or 'غير محدد')

                self.payments_tree.insert('', 'end', values=(
                    payment['id'],
                    payment['contract_number'] or '',
                    amount_text,
                    payment['payment_date'] or '',
                    method_text,
                    payment['reference_number'] or '',
                    payment['notes'] or ''
                ))

            self.update_status(f"💳 تم تحميل {len(payments)} دفعة")

        except Exception as e:
            logger.error(f"خطأ في تحميل المدفوعات: {e}")
            self.update_status(f"❌ خطأ في تحميل المدفوعات: {str(e)}")

    def load_contractors(self):
        """تحميل المقاولين"""
        try:
            # مسح البيانات القديمة
            for item in self.contractors_tree.get_children():
                self.contractors_tree.delete(item)

            # تحميل المقاولين
            query = "SELECT id, name, phone, email, address FROM contractors ORDER BY id DESC"
            contractors = sqlite_manager.execute_query(query)

            for contractor in contractors:
                self.contractors_tree.insert('', 'end', values=(
                    contractor['id'],
                    contractor['name'] or '',
                    'أعمال عامة',  # التخصص
                    contractor['phone'] or '',
                    contractor['email'] or '',
                    contractor['address'] or '',
                    '🟢 نشط'  # الحالة
                ))

            self.update_status(f"👷 تم تحميل {len(contractors)} مقاول")

        except Exception as e:
            logger.error(f"خطأ في تحميل المقاولين: {e}")
            self.update_status(f"❌ خطأ في تحميل المقاولين: {str(e)}")

    def load_extracts(self):
        """تحميل المستخلصات"""
        try:
            # مسح البيانات القديمة
            for item in self.extracts_tree.get_children():
                self.extracts_tree.delete(item)

            # تحميل المستخلصات
            query = """
                SELECT e.id, e.extract_number, e.contractor, e.project,
                       e.total_amount, e.extract_date, e.status, e.description
                FROM extracts e
                ORDER BY e.id DESC
            """
            extracts = sqlite_manager.execute_query(query)

            for extract in extracts:
                amount_text = f"{extract['total_amount']:,.0f} ريال" if extract['total_amount'] else "0"

                status_text = {
                    'pending': '🟡 معلق',
                    'approved': '🟢 معتمد',
                    'paid': '✅ مدفوع',
                    'rejected': '❌ مرفوض'
                }.get(extract['status'], extract['status'] or 'غير محدد')

                self.extracts_tree.insert('', 'end', values=(
                    extract['id'],
                    extract['extract_number'] or '',
                    extract['contractor'] or '',
                    extract['project'] or '',
                    amount_text,
                    extract['extract_date'] or '',
                    status_text,
                    extract['description'] or ''
                ))

            self.update_status(f"📄 تم تحميل {len(extracts)} مستخلص")

        except Exception as e:
            logger.error(f"خطأ في تحميل المستخلصات: {e}")
            self.update_status(f"❌ خطأ في تحميل المستخلصات: {str(e)}")

    def load_suppliers(self):
        """تحميل الموردين"""
        try:
            # مسح البيانات القديمة
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)

            # تحميل الموردين
            query = "SELECT id, name, phone, email, address FROM suppliers ORDER BY id DESC"
            suppliers = sqlite_manager.execute_query(query)

            for supplier in suppliers:
                self.suppliers_tree.insert('', 'end', values=(
                    supplier['id'],
                    supplier['name'] or '',
                    'مواد عامة',  # نوع المواد
                    supplier['phone'] or '',
                    supplier['email'] or '',
                    supplier['address'] or '',
                    '🟢 نشط'  # الحالة
                ))

            self.update_status(f"📦 تم تحميل {len(suppliers)} مورد")

        except Exception as e:
            logger.error(f"خطأ في تحميل الموردين: {e}")
            self.update_status(f"❌ خطأ في تحميل الموردين: {str(e)}")

    def load_invoices(self):
        """تحميل الفواتير"""
        try:
            # مسح البيانات القديمة
            for item in self.invoices_tree.get_children():
                self.invoices_tree.delete(item)

            # تحميل الفواتير
            query = """
                SELECT i.id, i.invoice_number, i.supplier, i.total_amount,
                       i.invoice_date, i.due_date, i.status
                FROM invoices i
                ORDER BY i.id DESC
            """
            invoices = sqlite_manager.execute_query(query)

            for invoice in invoices:
                amount_text = f"{invoice['total_amount']:,.0f} ريال" if invoice['total_amount'] else "0"

                status_text = {
                    'pending': '🟡 معلقة',
                    'approved': '🟢 معتمدة',
                    'paid': '✅ مدفوعة',
                    'overdue': '🔴 متأخرة'
                }.get(invoice['status'], invoice['status'] or 'غير محدد')

                self.invoices_tree.insert('', 'end', values=(
                    invoice['id'],
                    invoice['invoice_number'] or '',
                    invoice['supplier'] or '',
                    amount_text,
                    invoice['invoice_date'] or '',
                    invoice['due_date'] or '',
                    status_text
                ))

            self.update_status(f"🧾 تم تحميل {len(invoices)} فاتورة")

        except Exception as e:
            logger.error(f"خطأ في تحميل الفواتير: {e}")
            self.update_status(f"❌ خطأ في تحميل الفواتير: {str(e)}")

    def load_purchase_requests(self):
        """تحميل طلبات الشراء"""
        try:
            # مسح البيانات القديمة
            for item in self.purchase_requests_tree.get_children():
                self.purchase_requests_tree.delete(item)

            # تحميل طلبات الشراء
            query = """
                SELECT p.id, p.request_number, p.project, p.materials,
                       p.quantity, p.estimated_cost, p.request_date, p.status
                FROM purchase_requests p
                ORDER BY p.id DESC
            """
            requests = sqlite_manager.execute_query(query)

            for request in requests:
                cost_text = f"{request['estimated_cost']:,.0f} ريال" if request['estimated_cost'] else "0"

                status_text = {
                    'pending': '🟡 معلق',
                    'approved': '🟢 معتمد',
                    'completed': '✅ مكتمل',
                    'cancelled': '❌ ملغي'
                }.get(request['status'], request['status'] or 'غير محدد')

                self.purchase_requests_tree.insert('', 'end', values=(
                    request['id'],
                    request['request_number'] or '',
                    request['project'] or '',
                    request['materials'] or '',
                    request['quantity'] or 0,
                    cost_text,
                    request['request_date'] or '',
                    status_text
                ))

            self.update_status(f"🛒 تم تحميل {len(requests)} طلب شراء")

        except Exception as e:
            logger.error(f"خطأ في تحميل طلبات الشراء: {e}")
            self.update_status(f"❌ خطأ في تحميل طلبات الشراء: {str(e)}")

    def load_maintenance_tasks(self):
        """تحميل مهام الصيانة"""
        try:
            # مسح البيانات القديمة
            for item in self.maintenance_tasks_tree.get_children():
                self.maintenance_tasks_tree.delete(item)

            # تحميل مهام الصيانة
            query = """
                SELECT m.id, m.task_number, m.task_title, m.maintenance_type,
                       m.location, m.technician, m.date, m.cost, m.status
                FROM maintenance_tasks m
                ORDER BY m.id DESC
            """
            tasks = sqlite_manager.execute_query(query)

            for task in tasks:
                cost_text = f"{task['cost']:,.0f} ريال" if task['cost'] else "0"

                status_text = {
                    'pending': '🟡 معلق',
                    'in_progress': '🔄 قيد التنفيذ',
                    'completed': '✅ مكتمل',
                    'cancelled': '❌ ملغي'
                }.get(task['status'], task['status'] or 'غير محدد')

                self.maintenance_tasks_tree.insert('', 'end', values=(
                    task['id'],
                    task['task_number'] or '',
                    task['task_title'] or '',
                    task['maintenance_type'] or '',
                    task['location'] or '',
                    task['technician'] or '',
                    task['date'] or '',
                    cost_text,
                    status_text
                ))

            self.update_status(f"🔧 تم تحميل {len(tasks)} مهمة صيانة")

        except Exception as e:
            logger.error(f"خطأ في تحميل مهام الصيانة: {e}")
            self.update_status(f"❌ خطأ في تحميل مهام الصيانة: {str(e)}")

    def load_daily_tasks(self):
        """تحميل المهام اليومية"""
        try:
            # مسح البيانات القديمة
            for item in self.daily_tasks_tree.get_children():
                self.daily_tasks_tree.delete(item)

            # تحميل المهام اليومية
            query = """
                SELECT d.id, d.task_title, d.description, d.assigned_to,
                       d.priority, d.due_date, d.status, d.created_date
                FROM daily_tasks d
                ORDER BY d.id DESC
            """
            tasks = sqlite_manager.execute_query(query)

            for task in tasks:
                priority_text = {
                    'high': '🔴 عالية',
                    'medium': '🟡 متوسطة',
                    'low': '🟢 منخفضة'
                }.get(task['priority'], task['priority'] or 'غير محدد')

                status_text = {
                    'pending': '🟡 معلق',
                    'in_progress': '🔄 قيد التنفيذ',
                    'completed': '✅ مكتمل',
                    'cancelled': '❌ ملغي'
                }.get(task['status'], task['status'] or 'غير محدد')

                self.daily_tasks_tree.insert('', 'end', values=(
                    task['id'],
                    task['task_title'] or '',
                    task['description'] or '',
                    task['assigned_to'] or '',
                    priority_text,
                    task['due_date'] or '',
                    status_text,
                    task['created_date'] or datetime.now().strftime('%Y-%m-%d')
                ))

            self.update_status(f"📋 تم تحميل {len(tasks)} مهمة يومية")

        except Exception as e:
            logger.error(f"خطأ في تحميل المهام اليومية: {e}")
            self.update_status(f"❌ خطأ في تحميل المهام اليومية: {str(e)}")

    def load_users(self):
        """تحميل المستخدمين"""
        try:
            # مسح البيانات القديمة
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            # تحميل المستخدمين
            query = """
                SELECT u.id, u.username, u.full_name, u.email,
                       u.user_type, u.is_active, u.created_date
                FROM users u
                ORDER BY u.id DESC
            """
            users = sqlite_manager.execute_query(query)

            for user in users:
                user_type_text = {
                    'admin': '👑 مدير عام',
                    'manager': '👔 مدير قسم',
                    'employee': '👤 موظف',
                    'viewer': '👁️ مشاهد'
                }.get(user['user_type'], user['user_type'] or 'غير محدد')

                status_text = '🟢 مفعل' if user['is_active'] else '🔴 غير مفعل'

                self.users_tree.insert('', 'end', values=(
                    user['id'],
                    user['username'] or '',
                    user['full_name'] or '',
                    user['email'] or '',
                    user_type_text,
                    status_text,
                    user['created_date'] or datetime.now().strftime('%Y-%m-%d')
                ))

            self.update_status(f"👤 تم تحميل {len(users)} مستخدم")

        except Exception as e:
            logger.error(f"خطأ في تحميل المستخدمين: {e}")
            self.update_status(f"❌ خطأ في تحميل المستخدمين: {str(e)}")

    def update_status(self, message):
        """تحديث شريط الحالة"""
        if hasattr(self, 'status_label'):
            self.status_label.config(text=message)
            self.root.update_idletasks()

    def update_dashboard(self):
        """تحديث لوحة التحكم"""
        try:
            # إحصائيات المشاريع
            projects_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM projects")[0][0]
            if hasattr(self, 'المشاريع_value_label'):
                self.المشاريع_value_label.config(text=str(projects_count))

            # إحصائيات العملاء
            customers_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM customers")[0][0]
            if hasattr(self, 'العملاء_value_label'):
                self.العملاء_value_label.config(text=str(customers_count))

            # إحصائيات الوحدات
            units_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM units")[0][0]
            if hasattr(self, 'الوحدات_value_label'):
                self.الوحدات_value_label.config(text=str(units_count))

            # إحصائيات العقود
            contracts_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM contracts")[0][0]
            if hasattr(self, 'العقود_value_label'):
                self.العقود_value_label.config(text=str(contracts_count))

            # تحديث الأنشطة
            self.update_activities_display()

        except Exception as e:
            logger.error(f"خطأ في تحديث لوحة التحكم: {e}")

    def update_activities_display(self):
        """تحديث عرض الأنشطة"""
        try:
            # جمع الإحصائيات
            projects_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM projects")[0][0]
            customers_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM customers")[0][0]
            units_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM units")[0][0]
            contracts_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM contracts")[0][0]

            # إحصائيات الوحدات حسب الحالة
            available_units = sqlite_manager.execute_query("SELECT COUNT(*) FROM units WHERE status='available'")[0][0]
            reserved_units = sqlite_manager.execute_query("SELECT COUNT(*) FROM units WHERE status='reserved'")[0][0]
            sold_units = sqlite_manager.execute_query("SELECT COUNT(*) FROM units WHERE status='sold'")[0][0]

            # إحصائيات مالية
            total_sales = sqlite_manager.execute_query("SELECT COALESCE(SUM(total_amount), 0) FROM contracts WHERE contract_type='sale'")[0][0]
            total_paid = sqlite_manager.execute_query("SELECT COALESCE(SUM(paid_amount), 0) FROM contracts")[0][0]

            activities = f"""
🎉 مرحباً بك في نظام إدارة شركة رافع للتطوير العقاري المحسن!

📊 إحصائيات النظام الحالية:
{'='*60}
🏗️ المشاريع: {projects_count} مشروع
👥 العملاء: {customers_count} عميل
🏠 الوحدات: {units_count} وحدة
📄 العقود: {contracts_count} عقد

🏠 تفاصيل الوحدات:
{'='*35}
🟢 متاحة: {available_units} وحدة
🟡 محجوزة: {reserved_units} وحدة
🔴 مباعة: {sold_units} وحدة

💰 الإحصائيات المالية:
{'='*35}
💵 إجمالي المبيعات: {total_sales:,.0f} ريال
💳 إجمالي المدفوعات: {total_paid:,.0f} ريال
💰 المبالغ المتبقية: {total_sales - total_paid:,.0f} ريال
📊 نسبة التحصيل: {(total_paid/total_sales*100) if total_sales > 0 else 0:.1f}%

📅 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

✨ الوظائف المتاحة في النظام المحسن:
{'='*50}
🏗️ إدارة المشاريع مع مربعات حوار محسنة
👥 إدارة العملاء مع واجهات سهلة الاستخدام
🏠 إدارة الوحدات السكنية (إضافة، حجز، بيع)
💰 إدارة العقود والمبيعات
💳 تتبع المدفوعات بطرق مختلفة
📊 تقارير مفصلة وإحصائيات دقيقة

🎯 التحسينات الجديدة:
{'='*35}
✅ مربعات حوار واضحة ومحسنة
✅ واجهات مستخدم جميلة ومنظمة
✅ تحديث تلقائي للبيانات
✅ رموز تعبيرية لسهولة التمييز
✅ تنسيق محسن للجداول
✅ شريط حالة تفاعلي
✅ معالجة أخطاء محسنة

🚀 النظام جاهز للاستخدام الإنتاجي بكفاءة عالية!
"""

            self.activities_text.delete('1.0', tk.END)
            self.activities_text.insert('1.0', activities)

        except Exception as e:
            logger.error(f"خطأ في تحديث الأنشطة: {e}")

    # دوال الإضافة والتعديل والحذف
    def add_project(self):
        """إضافة مشروع جديد"""
        try:
            dialog = ProjectDialog(self.root, "إضافة مشروع جديد")
            self.root.wait_window(dialog.dialog)

            if dialog.result:
                query = """
                    INSERT INTO projects (name, location, budget, status, start_date, description)
                    VALUES (?, ?, ?, ?, ?, ?)
                """

                budget = float(dialog.result['budget']) if dialog.result['budget'] else 0

                sqlite_manager.execute_query(query, (
                    dialog.result['name'],
                    dialog.result['location'],
                    budget,
                    dialog.result['status'] or 'active',
                    dialog.result['start_date'] or datetime.now().strftime('%Y-%m-%d'),
                    dialog.result['description']
                ))

                show_info_dialog(self.root, "نجح", "تم إضافة المشروع بنجاح!")
                self.load_projects()
                self.update_dashboard()

        except Exception as e:
            logger.error(f"خطأ في إضافة المشروع: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في إضافة المشروع:\n{str(e)}")

    def edit_project(self):
        """تعديل مشروع"""
        show_info_dialog(self.root, "قريباً", "سيتم تطوير تعديل المشاريع قريباً")

    def delete_project(self):
        """حذف مشروع"""
        selection = self.projects_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار مشروع للحذف")
            return

        item = self.projects_tree.item(selection[0])
        project_id = item['values'][0]
        project_name = item['values'][1]

        if show_confirm_dialog(self.root, "تأكيد الحذف", f"هل تريد حذف المشروع '{project_name}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM projects WHERE id = ?", (project_id,))
                show_info_dialog(self.root, "نجح", "تم حذف المشروع بنجاح!")
                self.load_projects()
                self.update_dashboard()
            except Exception as e:
                logger.error(f"خطأ في حذف المشروع: {e}")
                show_info_dialog(self.root, "خطأ", f"فشل في حذف المشروع:\n{str(e)}")

    def add_customer(self):
        """إضافة عميل جديد"""
        try:
            dialog = CustomerDialog(self.root, "إضافة عميل جديد")
            self.root.wait_window(dialog.dialog)

            if dialog.result:
                query = """
                    INSERT INTO customers (name, phone, email, address, national_id)
                    VALUES (?, ?, ?, ?, ?)
                """

                sqlite_manager.execute_query(query, (
                    dialog.result['name'],
                    dialog.result['phone'],
                    dialog.result['email'],
                    dialog.result['address'],
                    dialog.result['national_id']
                ))

                show_info_dialog(self.root, "نجح", "تم إضافة العميل بنجاح!")
                self.load_customers()
                self.update_dashboard()

        except Exception as e:
            logger.error(f"خطأ في إضافة العميل: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في إضافة العميل:\n{str(e)}")

    def edit_customer(self):
        """تعديل عميل"""
        show_info_dialog(self.root, "قريباً", "سيتم تطوير تعديل العملاء قريباً")

    def delete_customer(self):
        """حذف عميل"""
        selection = self.customers_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار عميل للحذف")
            return

        item = self.customers_tree.item(selection[0])
        customer_id = item['values'][0]
        customer_name = item['values'][1]

        if show_confirm_dialog(self.root, "تأكيد الحذف", f"هل تريد حذف العميل '{customer_name}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM customers WHERE id = ?", (customer_id,))
                show_info_dialog(self.root, "نجح", "تم حذف العميل بنجاح!")
                self.load_customers()
                self.update_dashboard()
            except Exception as e:
                logger.error(f"خطأ في حذف العميل: {e}")
                show_info_dialog(self.root, "خطأ", f"فشل في حذف العميل:\n{str(e)}")

    def add_unit(self):
        """إضافة وحدة جديدة"""
        try:
            dialog = UnitDialog(self.root, "إضافة وحدة جديدة")
            self.root.wait_window(dialog.dialog)

            if dialog.result:
                query = """
                    INSERT INTO units (unit_number, unit_type, area, price, status, description)
                    VALUES (?, ?, ?, ?, 'available', ?)
                """

                area = float(dialog.result['area']) if dialog.result['area'] else 0
                price = float(dialog.result['price']) if dialog.result['price'] else 0

                sqlite_manager.execute_query(query, (
                    dialog.result['unit_number'],
                    dialog.result['unit_type'],
                    area,
                    price,
                    dialog.result['description']
                ))

                show_info_dialog(self.root, "نجح", "تم إضافة الوحدة بنجاح!")
                self.load_units()
                self.update_dashboard()

        except Exception as e:
            logger.error(f"خطأ في إضافة الوحدة: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في إضافة الوحدة:\n{str(e)}")

    def edit_unit(self):
        """تعديل وحدة"""
        show_info_dialog(self.root, "قريباً", "سيتم تطوير تعديل الوحدات قريباً")

    def delete_unit(self):
        """حذف وحدة"""
        selection = self.units_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار وحدة للحذف")
            return

        item = self.units_tree.item(selection[0])
        unit_id = item['values'][0]
        unit_number = item['values'][1]

        if show_confirm_dialog(self.root, "تأكيد الحذف", f"هل تريد حذف الوحدة '{unit_number}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM units WHERE id = ?", (unit_id,))
                show_info_dialog(self.root, "نجح", "تم حذف الوحدة بنجاح!")
                self.load_units()
                self.update_dashboard()
            except Exception as e:
                logger.error(f"خطأ في حذف الوحدة: {e}")
                show_info_dialog(self.root, "خطأ", f"فشل في حذف الوحدة:\n{str(e)}")

    def reserve_unit(self):
        """حجز وحدة"""
        selection = self.units_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار وحدة للحجز")
            return

        item = self.units_tree.item(selection[0])
        unit_id = item['values'][0]
        unit_number = item['values'][1]
        current_status = item['values'][5]

        if '🔴 مباع' in current_status:
            show_info_dialog(self.root, "تحذير", "لا يمكن حجز وحدة مباعة")
            return

        if '🟡 محجوز' in current_status:
            show_info_dialog(self.root, "تحذير", "الوحدة محجوزة بالفعل")
            return

        if show_confirm_dialog(self.root, "تأكيد الحجز", f"هل تريد حجز الوحدة '{unit_number}'؟"):
            try:
                sqlite_manager.execute_query("UPDATE units SET status='reserved' WHERE id=?", (unit_id,))
                show_info_dialog(self.root, "نجح", f"تم حجز الوحدة {unit_number} بنجاح!")
                self.load_units()
                self.update_dashboard()
            except Exception as e:
                logger.error(f"خطأ في حجز الوحدة: {e}")
                show_info_dialog(self.root, "خطأ", f"فشل في حجز الوحدة:\n{str(e)}")

    def sell_unit(self):
        """بيع وحدة"""
        selection = self.units_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار وحدة للبيع")
            return

        item = self.units_tree.item(selection[0])
        unit_id = item['values'][0]
        unit_number = item['values'][1]
        current_status = item['values'][5]

        if '🔴 مباع' in current_status:
            show_info_dialog(self.root, "تحذير", "الوحدة مباعة بالفعل")
            return

        if show_confirm_dialog(self.root, "تأكيد البيع", f"هل تريد بيع الوحدة '{unit_number}'؟"):
            try:
                sqlite_manager.execute_query("UPDATE units SET status='sold' WHERE id=?", (unit_id,))
                show_info_dialog(self.root, "نجح", f"تم بيع الوحدة {unit_number} بنجاح!")
                self.load_units()
                self.update_dashboard()
            except Exception as e:
                logger.error(f"خطأ في بيع الوحدة: {e}")
                show_info_dialog(self.root, "خطأ", f"فشل في بيع الوحدة:\n{str(e)}")

    def add_contract(self):
        """إضافة عقد جديد"""
        try:
            dialog = ContractDialog(self.root, "إضافة عقد جديد")
            self.root.wait_window(dialog.dialog)

            if dialog.result:
                query = """
                    INSERT INTO contracts (contract_number, customer_id, unit_id, total_amount,
                                         paid_amount, remaining_amount, contract_type, status,
                                         contract_date, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, 'active', ?, ?)
                """

                total_amount = float(dialog.result['total_amount']) if dialog.result['total_amount'] else 0
                paid_amount = float(dialog.result['paid_amount']) if dialog.result['paid_amount'] else 0
                remaining_amount = total_amount - paid_amount

                sqlite_manager.execute_query(query, (
                    dialog.result['contract_number'],
                    int(dialog.result['customer_id']),
                    int(dialog.result['unit_id']),
                    total_amount,
                    paid_amount,
                    remaining_amount,
                    dialog.result['contract_type'],
                    datetime.now().strftime('%Y-%m-%d'),
                    dialog.result['notes']
                ))

                show_info_dialog(self.root, "نجح", "تم إضافة العقد بنجاح!")
                self.load_contracts()
                self.update_dashboard()

        except Exception as e:
            logger.error(f"خطأ في إضافة العقد: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في إضافة العقد:\n{str(e)}")

    def add_payment(self):
        """إضافة دفعة جديدة"""
        try:
            dialog = PaymentDialog(self.root, "إضافة دفعة جديدة")
            self.root.wait_window(dialog.dialog)

            if dialog.result:
                query = """
                    INSERT INTO payments (contract_id, amount, payment_date, payment_method,
                                        reference_number, notes)
                    VALUES (?, ?, ?, ?, ?, ?)
                """

                amount = float(dialog.result['amount']) if dialog.result['amount'] else 0

                sqlite_manager.execute_query(query, (
                    int(dialog.result['contract_id']),
                    amount,
                    datetime.now().strftime('%Y-%m-%d'),
                    dialog.result['payment_method'],
                    dialog.result['reference_number'],
                    dialog.result['notes']
                ))

                show_info_dialog(self.root, "نجح", "تم إضافة الدفعة بنجاح!")
                self.load_payments()
                self.update_dashboard()

        except Exception as e:
            logger.error(f"خطأ في إضافة الدفعة: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في إضافة الدفعة:\n{str(e)}")

    # دوال المقاولين والمستخلصات
    def add_contractor(self):
        """إضافة مقاول جديد"""
        try:
            dialog = ContractorDialog(self.root, "إضافة مقاول جديد")
            self.root.wait_window(dialog.dialog)

            if dialog.result:
                query = """
                    INSERT INTO contractors (name, phone, email, address)
                    VALUES (?, ?, ?, ?)
                """

                sqlite_manager.execute_query(query, (
                    dialog.result['name'],
                    dialog.result['phone'],
                    dialog.result['email'],
                    dialog.result['address']
                ))

                show_info_dialog(self.root, "نجح", "تم إضافة المقاول بنجاح!")
                self.load_contractors()
                self.update_dashboard()

        except Exception as e:
            logger.error(f"خطأ في إضافة المقاول: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في إضافة المقاول:\n{str(e)}")

    def edit_contractor(self):
        """تعديل مقاول"""
        selection = self.contractors_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار مقاول للتعديل")
            return

        item = self.contractors_tree.item(selection[0])
        contractor_id = item['values'][0]

        try:
            # جلب بيانات المقاول الحالية
            query = "SELECT * FROM contractors WHERE id = ?"
            result = sqlite_manager.execute_query(query, (contractor_id,))

            if result:
                contractor = result[0]

                dialog = ContractorDialog(self.root, "تعديل بيانات المقاول")

                # ملء البيانات الحالية
                dialog.entries['name'].insert(0, contractor['name'] or '')
                dialog.entries['phone'].insert(0, contractor['phone'] or '')
                dialog.entries['email'].insert(0, contractor['email'] or '')
                dialog.entries['address'].insert(0, contractor['address'] or '')

                self.root.wait_window(dialog.dialog)

                if dialog.result:
                    update_query = """
                        UPDATE contractors
                        SET name=?, phone=?, email=?, address=?
                        WHERE id=?
                    """

                    sqlite_manager.execute_query(update_query, (
                        dialog.result['name'],
                        dialog.result['phone'],
                        dialog.result['email'],
                        dialog.result['address'],
                        contractor_id
                    ))

                    show_info_dialog(self.root, "نجح", "تم تحديث بيانات المقاول بنجاح!")
                    self.load_contractors()

        except Exception as e:
            logger.error(f"خطأ في تعديل المقاول: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في تعديل المقاول:\n{str(e)}")

    def delete_contractor(self):
        """حذف مقاول"""
        selection = self.contractors_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار مقاول للحذف")
            return

        item = self.contractors_tree.item(selection[0])
        contractor_id = item['values'][0]
        contractor_name = item['values'][1]

        if show_confirm_dialog(self.root, "تأكيد الحذف", f"هل تريد حذف المقاول '{contractor_name}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM contractors WHERE id = ?", (contractor_id,))
                show_info_dialog(self.root, "نجح", "تم حذف المقاول بنجاح!")
                self.load_contractors()
                self.update_dashboard()
            except Exception as e:
                logger.error(f"خطأ في حذف المقاول: {e}")
                show_info_dialog(self.root, "خطأ", f"فشل في حذف المقاول:\n{str(e)}")

    def add_extract(self):
        """إضافة مستخلص جديد"""
        try:
            dialog = ExtractDialog(self.root, "إضافة مستخلص جديد")
            self.root.wait_window(dialog.dialog)

            if dialog.result:
                query = """
                    INSERT INTO extracts (extract_number, contractor, project, total_amount,
                                        extract_date, status, description)
                    VALUES (?, ?, ?, ?, ?, 'pending', ?)
                """

                total_amount = float(dialog.result['total_amount']) if dialog.result['total_amount'] else 0
                extract_date = dialog.result['extract_date'] or datetime.now().strftime('%Y-%m-%d')

                sqlite_manager.execute_query(query, (
                    dialog.result['extract_number'],
                    dialog.result['contractor'],
                    dialog.result['project'],
                    total_amount,
                    extract_date,
                    dialog.result['description']
                ))

                show_info_dialog(self.root, "نجح", "تم إضافة المستخلص بنجاح!")
                self.load_extracts()
                self.update_dashboard()

        except Exception as e:
            logger.error(f"خطأ في إضافة المستخلص: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في إضافة المستخلص:\n{str(e)}")

    def edit_extract(self):
        """تعديل مستخلص"""
        selection = self.extracts_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار مستخلص للتعديل")
            return

        item = self.extracts_tree.item(selection[0])
        extract_id = item['values'][0]

        try:
            # جلب بيانات المستخلص الحالية
            query = "SELECT * FROM extracts WHERE id = ?"
            result = sqlite_manager.execute_query(query, (extract_id,))

            if result:
                extract = result[0]

                dialog = ExtractDialog(self.root, "تعديل بيانات المستخلص")

                # ملء البيانات الحالية
                dialog.entries['extract_number'].insert(0, extract['extract_number'] or '')
                dialog.entries['contractor'].insert(0, extract['contractor'] or '')
                dialog.entries['project'].insert(0, extract['project'] or '')
                dialog.entries['total_amount'].insert(0, str(extract['total_amount'] or 0))
                dialog.entries['extract_date'].insert(0, extract['extract_date'] or '')
                if isinstance(dialog.entries['description'], tk.Text):
                    dialog.entries['description'].insert('1.0', extract['description'] or '')

                self.root.wait_window(dialog.dialog)

                if dialog.result:
                    total_amount = float(dialog.result['total_amount']) if dialog.result['total_amount'] else 0

                    update_query = """
                        UPDATE extracts
                        SET extract_number=?, contractor=?, project=?, total_amount=?,
                            extract_date=?, description=?
                        WHERE id=?
                    """

                    sqlite_manager.execute_query(update_query, (
                        dialog.result['extract_number'],
                        dialog.result['contractor'],
                        dialog.result['project'],
                        total_amount,
                        dialog.result['extract_date'],
                        dialog.result['description'],
                        extract_id
                    ))

                    show_info_dialog(self.root, "نجح", "تم تحديث بيانات المستخلص بنجاح!")
                    self.load_extracts()

        except Exception as e:
            logger.error(f"خطأ في تعديل المستخلص: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في تعديل المستخلص:\n{str(e)}")

    def delete_extract(self):
        """حذف مستخلص"""
        selection = self.extracts_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار مستخلص للحذف")
            return

        item = self.extracts_tree.item(selection[0])
        extract_id = item['values'][0]
        extract_number = item['values'][1]

        if show_confirm_dialog(self.root, "تأكيد الحذف", f"هل تريد حذف المستخلص '{extract_number}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM extracts WHERE id = ?", (extract_id,))
                show_info_dialog(self.root, "نجح", "تم حذف المستخلص بنجاح!")
                self.load_extracts()
                self.update_dashboard()
            except Exception as e:
                logger.error(f"خطأ في حذف المستخلص: {e}")
                show_info_dialog(self.root, "خطأ", f"فشل في حذف المستخلص:\n{str(e)}")

    def add_supplier(self):
        """إضافة مورد جديد"""
        try:
            dialog = SupplierDialog(self.root, "إضافة مورد جديد")
            self.root.wait_window(dialog.dialog)

            if dialog.result:
                query = """
                    INSERT INTO suppliers (name, phone, email, address)
                    VALUES (?, ?, ?, ?)
                """

                sqlite_manager.execute_query(query, (
                    dialog.result['name'],
                    dialog.result['phone'],
                    dialog.result['email'],
                    dialog.result['address']
                ))

                show_info_dialog(self.root, "نجح", "تم إضافة المورد بنجاح!")
                self.load_suppliers()
                self.update_dashboard()

        except Exception as e:
            logger.error(f"خطأ في إضافة المورد: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في إضافة المورد:\n{str(e)}")

    def edit_supplier(self):
        """تعديل مورد"""
        selection = self.suppliers_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار مورد للتعديل")
            return

        item = self.suppliers_tree.item(selection[0])
        supplier_id = item['values'][0]

        try:
            # جلب بيانات المورد الحالية
            query = "SELECT * FROM suppliers WHERE id = ?"
            result = sqlite_manager.execute_query(query, (supplier_id,))

            if result:
                supplier = result[0]

                dialog = SupplierDialog(self.root, "تعديل بيانات المورد")

                # ملء البيانات الحالية
                dialog.entries['name'].insert(0, supplier['name'] or '')
                dialog.entries['phone'].insert(0, supplier['phone'] or '')
                dialog.entries['email'].insert(0, supplier['email'] or '')
                dialog.entries['address'].insert(0, supplier['address'] or '')

                self.root.wait_window(dialog.dialog)

                if dialog.result:
                    update_query = """
                        UPDATE suppliers
                        SET name=?, phone=?, email=?, address=?
                        WHERE id=?
                    """

                    sqlite_manager.execute_query(update_query, (
                        dialog.result['name'],
                        dialog.result['phone'],
                        dialog.result['email'],
                        dialog.result['address'],
                        supplier_id
                    ))

                    show_info_dialog(self.root, "نجح", "تم تحديث بيانات المورد بنجاح!")
                    self.load_suppliers()

        except Exception as e:
            logger.error(f"خطأ في تعديل المورد: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في تعديل المورد:\n{str(e)}")

    def delete_supplier(self):
        """حذف مورد"""
        selection = self.suppliers_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار مورد للحذف")
            return

        item = self.suppliers_tree.item(selection[0])
        supplier_id = item['values'][0]
        supplier_name = item['values'][1]

        if show_confirm_dialog(self.root, "تأكيد الحذف", f"هل تريد حذف المورد '{supplier_name}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM suppliers WHERE id = ?", (supplier_id,))
                show_info_dialog(self.root, "نجح", "تم حذف المورد بنجاح!")
                self.load_suppliers()
                self.update_dashboard()
            except Exception as e:
                logger.error(f"خطأ في حذف المورد: {e}")
                show_info_dialog(self.root, "خطأ", f"فشل في حذف المورد:\n{str(e)}")

    def add_invoice(self):
        """إضافة فاتورة جديدة"""
        try:
            dialog = InvoiceDialog(self.root, "إضافة فاتورة جديدة")
            self.root.wait_window(dialog.dialog)

            if dialog.result:
                query = """
                    INSERT INTO invoices (invoice_number, supplier, total_amount,
                                        invoice_date, due_date, status)
                    VALUES (?, ?, ?, ?, ?, 'pending')
                """

                total_amount = float(dialog.result['total_amount']) if dialog.result['total_amount'] else 0
                invoice_date = dialog.result['invoice_date'] or datetime.now().strftime('%Y-%m-%d')
                due_date = dialog.result['due_date'] or datetime.now().strftime('%Y-%m-%d')

                sqlite_manager.execute_query(query, (
                    dialog.result['invoice_number'],
                    dialog.result['supplier'],
                    total_amount,
                    invoice_date,
                    due_date
                ))

                show_info_dialog(self.root, "نجح", "تم إضافة الفاتورة بنجاح!")
                self.load_invoices()
                self.update_dashboard()

        except Exception as e:
            logger.error(f"خطأ في إضافة الفاتورة: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في إضافة الفاتورة:\n{str(e)}")

    def edit_invoice(self):
        """تعديل فاتورة"""
        selection = self.invoices_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار فاتورة للتعديل")
            return

        item = self.invoices_tree.item(selection[0])
        invoice_id = item['values'][0]

        try:
            # جلب بيانات الفاتورة الحالية
            query = "SELECT * FROM invoices WHERE id = ?"
            result = sqlite_manager.execute_query(query, (invoice_id,))

            if result:
                invoice = result[0]

                dialog = InvoiceDialog(self.root, "تعديل بيانات الفاتورة")

                # ملء البيانات الحالية
                dialog.entries['invoice_number'].insert(0, invoice['invoice_number'] or '')
                dialog.entries['supplier'].insert(0, invoice['supplier'] or '')
                dialog.entries['total_amount'].insert(0, str(invoice['total_amount'] or 0))
                dialog.entries['invoice_date'].insert(0, invoice['invoice_date'] or '')
                dialog.entries['due_date'].insert(0, invoice['due_date'] or '')

                self.root.wait_window(dialog.dialog)

                if dialog.result:
                    total_amount = float(dialog.result['total_amount']) if dialog.result['total_amount'] else 0

                    update_query = """
                        UPDATE invoices
                        SET invoice_number=?, supplier=?, total_amount=?,
                            invoice_date=?, due_date=?
                        WHERE id=?
                    """

                    sqlite_manager.execute_query(update_query, (
                        dialog.result['invoice_number'],
                        dialog.result['supplier'],
                        total_amount,
                        dialog.result['invoice_date'],
                        dialog.result['due_date'],
                        invoice_id
                    ))

                    show_info_dialog(self.root, "نجح", "تم تحديث بيانات الفاتورة بنجاح!")
                    self.load_invoices()

        except Exception as e:
            logger.error(f"خطأ في تعديل الفاتورة: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في تعديل الفاتورة:\n{str(e)}")

    def delete_invoice(self):
        """حذف فاتورة"""
        selection = self.invoices_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار فاتورة للحذف")
            return

        item = self.invoices_tree.item(selection[0])
        invoice_id = item['values'][0]
        invoice_number = item['values'][1]

        if show_confirm_dialog(self.root, "تأكيد الحذف", f"هل تريد حذف الفاتورة '{invoice_number}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM invoices WHERE id = ?", (invoice_id,))
                show_info_dialog(self.root, "نجح", "تم حذف الفاتورة بنجاح!")
                self.load_invoices()
                self.update_dashboard()
            except Exception as e:
                logger.error(f"خطأ في حذف الفاتورة: {e}")
                show_info_dialog(self.root, "خطأ", f"فشل في حذف الفاتورة:\n{str(e)}")

    def add_purchase_request(self):
        """إضافة طلب شراء جديد"""
        try:
            dialog = PurchaseRequestDialog(self.root, "إضافة طلب شراء جديد")
            self.root.wait_window(dialog.dialog)

            if dialog.result:
                query = """
                    INSERT INTO purchase_requests (request_number, project, materials,
                                                 quantity, estimated_cost, request_date, status)
                    VALUES (?, ?, ?, ?, ?, ?, 'pending')
                """

                quantity = float(dialog.result['quantity']) if dialog.result['quantity'] else 0
                estimated_cost = float(dialog.result['estimated_cost']) if dialog.result['estimated_cost'] else 0

                sqlite_manager.execute_query(query, (
                    dialog.result['request_number'],
                    dialog.result['project'],
                    dialog.result['materials'],
                    quantity,
                    estimated_cost,
                    datetime.now().strftime('%Y-%m-%d')
                ))

                show_info_dialog(self.root, "نجح", "تم إضافة طلب الشراء بنجاح!")
                self.load_purchase_requests()
                self.update_dashboard()

        except Exception as e:
            logger.error(f"خطأ في إضافة طلب الشراء: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في إضافة طلب الشراء:\n{str(e)}")

    def edit_purchase_request(self):
        """تعديل طلب شراء"""
        show_info_dialog(self.root, "قريباً", "سيتم تطوير تعديل طلبات الشراء قريباً")

    def delete_purchase_request(self):
        """حذف طلب شراء"""
        selection = self.purchase_requests_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار طلب شراء للحذف")
            return

        item = self.purchase_requests_tree.item(selection[0])
        request_id = item['values'][0]
        request_number = item['values'][1]

        if show_confirm_dialog(self.root, "تأكيد الحذف", f"هل تريد حذف طلب الشراء '{request_number}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM purchase_requests WHERE id = ?", (request_id,))
                show_info_dialog(self.root, "نجح", "تم حذف طلب الشراء بنجاح!")
                self.load_purchase_requests()
                self.update_dashboard()
            except Exception as e:
                logger.error(f"خطأ في حذف طلب الشراء: {e}")
                show_info_dialog(self.root, "خطأ", f"فشل في حذف طلب الشراء:\n{str(e)}")

    def add_maintenance_task(self):
        """إضافة مهمة صيانة جديدة"""
        try:
            dialog = MaintenanceTaskDialog(self.root, "إضافة مهمة صيانة جديدة")
            self.root.wait_window(dialog.dialog)

            if dialog.result:
                query = """
                    INSERT INTO maintenance_tasks (task_number, task_title, maintenance_type,
                                                 location, technician, cost, date, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, 'pending')
                """

                cost = float(dialog.result['cost']) if dialog.result['cost'] else 0

                sqlite_manager.execute_query(query, (
                    dialog.result['task_number'],
                    dialog.result['task_title'],
                    dialog.result['maintenance_type'],
                    dialog.result['location'],
                    dialog.result['technician'],
                    cost,
                    datetime.now().strftime('%Y-%m-%d')
                ))

                show_info_dialog(self.root, "نجح", "تم إضافة مهمة الصيانة بنجاح!")
                self.load_maintenance_tasks()
                self.update_dashboard()

        except Exception as e:
            logger.error(f"خطأ في إضافة مهمة الصيانة: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في إضافة مهمة الصيانة:\n{str(e)}")

    def edit_maintenance_task(self):
        """تعديل مهمة صيانة"""
        show_info_dialog(self.root, "قريباً", "سيتم تطوير تعديل مهام الصيانة قريباً")

    def delete_maintenance_task(self):
        """حذف مهمة صيانة"""
        selection = self.maintenance_tasks_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار مهمة صيانة للحذف")
            return

        item = self.maintenance_tasks_tree.item(selection[0])
        task_id = item['values'][0]
        task_title = item['values'][2]

        if show_confirm_dialog(self.root, "تأكيد الحذف", f"هل تريد حذف مهمة الصيانة '{task_title}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM maintenance_tasks WHERE id = ?", (task_id,))
                show_info_dialog(self.root, "نجح", "تم حذف مهمة الصيانة بنجاح!")
                self.load_maintenance_tasks()
                self.update_dashboard()
            except Exception as e:
                logger.error(f"خطأ في حذف مهمة الصيانة: {e}")
                show_info_dialog(self.root, "خطأ", f"فشل في حذف مهمة الصيانة:\n{str(e)}")

    def add_maintenance_task(self):
        """إضافة مهمة صيانة جديدة"""
        show_info_dialog(self.root, "قريباً", "سيتم تطوير إضافة مهام الصيانة قريباً")

    def edit_maintenance_task(self):
        """تعديل مهمة صيانة"""
        show_info_dialog(self.root, "قريباً", "سيتم تطوير تعديل مهام الصيانة قريباً")

    def delete_maintenance_task(self):
        """حذف مهمة صيانة"""
        show_info_dialog(self.root, "قريباً", "سيتم تطوير حذف مهام الصيانة قريباً")

    def add_daily_task(self):
        """إضافة مهمة يومية جديدة"""
        try:
            dialog = DailyTaskDialog(self.root, "إضافة مهمة يومية جديدة")
            self.root.wait_window(dialog.dialog)

            if dialog.result:
                query = """
                    INSERT INTO daily_tasks (task_title, description, assigned_to,
                                           priority, due_date, status, created_date)
                    VALUES (?, ?, ?, ?, ?, 'pending', ?)
                """

                priority = dialog.result['priority'] or 'medium'
                due_date = dialog.result['due_date'] or datetime.now().strftime('%Y-%m-%d')

                sqlite_manager.execute_query(query, (
                    dialog.result['task_title'],
                    dialog.result['description'],
                    dialog.result['assigned_to'],
                    priority,
                    due_date,
                    datetime.now().strftime('%Y-%m-%d')
                ))

                show_info_dialog(self.root, "نجح", "تم إضافة المهمة اليومية بنجاح!")
                self.load_daily_tasks()
                self.update_dashboard()

        except Exception as e:
            logger.error(f"خطأ في إضافة المهمة اليومية: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في إضافة المهمة اليومية:\n{str(e)}")

    def edit_daily_task(self):
        """تعديل مهمة يومية"""
        show_info_dialog(self.root, "قريباً", "سيتم تطوير تعديل المهام اليومية قريباً")

    def delete_daily_task(self):
        """حذف مهمة يومية"""
        selection = self.daily_tasks_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار مهمة للحذف")
            return

        item = self.daily_tasks_tree.item(selection[0])
        task_id = item['values'][0]
        task_title = item['values'][1]

        if show_confirm_dialog(self.root, "تأكيد الحذف", f"هل تريد حذف المهمة '{task_title}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM daily_tasks WHERE id = ?", (task_id,))
                show_info_dialog(self.root, "نجح", "تم حذف المهمة بنجاح!")
                self.load_daily_tasks()
                self.update_dashboard()
            except Exception as e:
                logger.error(f"خطأ في حذف المهمة: {e}")
                show_info_dialog(self.root, "خطأ", f"فشل في حذف المهمة:\n{str(e)}")

    def complete_daily_task(self):
        """إكمال مهمة يومية"""
        selection = self.daily_tasks_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار مهمة لإكمالها")
            return

        item = self.daily_tasks_tree.item(selection[0])
        task_id = item['values'][0]
        task_title = item['values'][1]

        if show_confirm_dialog(self.root, "تأكيد الإكمال", f"هل تريد إكمال المهمة '{task_title}'؟"):
            try:
                query = "UPDATE daily_tasks SET status='completed' WHERE id=?"
                sqlite_manager.execute_query(query, (task_id,))
                show_info_dialog(self.root, "نجح", f"تم إكمال المهمة '{task_title}' بنجاح!")
                self.load_daily_tasks()
                self.update_dashboard()
            except Exception as e:
                logger.error(f"خطأ في إكمال المهمة: {e}")
                show_info_dialog(self.root, "خطأ", f"فشل في إكمال المهمة:\n{str(e)}")

    def add_user(self):
        """إضافة مستخدم جديد"""
        try:
            dialog = UserDialog(self.root, "إضافة مستخدم جديد")
            self.root.wait_window(dialog.dialog)

            if dialog.result:
                query = """
                    INSERT INTO users (username, password, full_name, email,
                                     user_type, is_active, created_date)
                    VALUES (?, ?, ?, ?, ?, 1, ?)
                """

                sqlite_manager.execute_query(query, (
                    dialog.result['username'],
                    dialog.result['password'],  # في التطبيق الحقيقي يجب تشفير كلمة المرور
                    dialog.result['full_name'],
                    dialog.result['email'],
                    dialog.result['user_type'],
                    datetime.now().strftime('%Y-%m-%d')
                ))

                show_info_dialog(self.root, "نجح", "تم إضافة المستخدم بنجاح!")
                self.load_users()
                self.update_dashboard()

        except Exception as e:
            logger.error(f"خطأ في إضافة المستخدم: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في إضافة المستخدم:\n{str(e)}")

    def edit_user(self):
        """تعديل مستخدم"""
        selection = self.users_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار مستخدم للتعديل")
            return

        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]

        try:
            # جلب بيانات المستخدم الحالية
            query = "SELECT * FROM users WHERE id = ?"
            result = sqlite_manager.execute_query(query, (user_id,))

            if result:
                user = result[0]

                dialog = UserDialog(self.root, "تعديل بيانات المستخدم")

                # ملء البيانات الحالية
                dialog.entries['username'].insert(0, user['username'] or '')
                dialog.entries['full_name'].insert(0, user['full_name'] or '')
                dialog.entries['email'].insert(0, user['email'] or '')
                if 'user_type' in dialog.entries:
                    dialog.entries['user_type'].set(user['user_type'] or 'employee')

                self.root.wait_window(dialog.dialog)

                if dialog.result:
                    update_query = """
                        UPDATE users
                        SET username=?, full_name=?, email=?, user_type=?
                        WHERE id=?
                    """

                    sqlite_manager.execute_query(update_query, (
                        dialog.result['username'],
                        dialog.result['full_name'],
                        dialog.result['email'],
                        dialog.result['user_type'],
                        user_id
                    ))

                    show_info_dialog(self.root, "نجح", "تم تحديث بيانات المستخدم بنجاح!")
                    self.load_users()

        except Exception as e:
            logger.error(f"خطأ في تعديل المستخدم: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في تعديل المستخدم:\n{str(e)}")

    def delete_user(self):
        """حذف مستخدم"""
        selection = self.users_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار مستخدم للحذف")
            return

        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]
        username = item['values'][1]

        if username == 'admin':
            show_info_dialog(self.root, "تحذير", "لا يمكن حذف المستخدم الرئيسي!")
            return

        if show_confirm_dialog(self.root, "تأكيد الحذف", f"هل تريد حذف المستخدم '{username}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM users WHERE id = ?", (user_id,))
                show_info_dialog(self.root, "نجح", "تم حذف المستخدم بنجاح!")
                self.load_users()
                self.update_dashboard()
            except Exception as e:
                logger.error(f"خطأ في حذف المستخدم: {e}")
                show_info_dialog(self.root, "خطأ", f"فشل في حذف المستخدم:\n{str(e)}")

    def change_password(self):
        """تغيير كلمة المرور"""
        selection = self.users_tree.selection()
        if not selection:
            show_info_dialog(self.root, "تحذير", "يرجى اختيار مستخدم لتغيير كلمة المرور")
            return

        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]
        username = item['values'][1]

        # مربع حوار بسيط لكلمة المرور الجديدة
        new_password = simpledialog.askstring("تغيير كلمة المرور",
                                            f"أدخل كلمة المرور الجديدة للمستخدم '{username}':",
                                            show='*')

        if new_password:
            try:
                query = "UPDATE users SET password = ? WHERE id = ?"
                sqlite_manager.execute_query(query, (new_password, user_id))
                show_info_dialog(self.root, "نجح", f"تم تغيير كلمة مرور المستخدم '{username}' بنجاح!")
            except Exception as e:
                logger.error(f"خطأ في تغيير كلمة المرور: {e}")
                show_info_dialog(self.root, "خطأ", f"فشل في تغيير كلمة المرور:\n{str(e)}")

    # دوال التقارير
    def generate_projects_report(self):
        """إنشاء تقرير المشاريع"""
        try:
            projects = sqlite_manager.execute_query("SELECT * FROM projects ORDER BY id DESC")

            report = f"""
📊 تقرير المشاريع التفصيلي
{'='*60}
📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📋 عدد المشاريع: {len(projects)}

تفاصيل المشاريع:
{'='*40}
"""

            for i, project in enumerate(projects, 1):
                budget_text = f"{project['budget']:,.0f} ريال" if project['budget'] else "غير محدد"
                status_text = {
                    'active': '🟢 نشط',
                    'inactive': '🔴 غير نشط',
                    'completed': '✅ مكتمل'
                }.get(project['status'], project['status'] or 'غير محدد')

                report += f"""
{i}. 🏗️ {project['name']}
   📍 الموقع: {project['location'] or 'غير محدد'}
   💰 الميزانية: {budget_text}
   📊 الحالة: {status_text}
   📅 تاريخ البداية: {project['start_date'] or 'غير محدد'}
   📝 الوصف: {project['description'] or 'لا يوجد وصف'}
   {'─' * 50}
"""

            self.reports_text.delete('1.0', tk.END)
            self.reports_text.insert('1.0', report)
            self.update_status("📊 تم إنشاء تقرير المشاريع")

        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير المشاريع: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في إنشاء تقرير المشاريع:\n{str(e)}")

    def generate_sales_report(self):
        """إنشاء تقرير المبيعات"""
        try:
            contracts = sqlite_manager.execute_query("SELECT * FROM contracts ORDER BY id DESC")
            total_sales = sum(contract['total_amount'] or 0 for contract in contracts)
            total_paid = sum(contract['paid_amount'] or 0 for contract in contracts)

            report = f"""
💰 تقرير المبيعات التفصيلي
{'='*60}
📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📋 عدد العقود: {len(contracts)}
💵 إجمالي المبيعات: {total_sales:,.0f} ريال
💳 إجمالي المدفوعات: {total_paid:,.0f} ريال
💰 المبالغ المتبقية: {total_sales - total_paid:,.0f} ريال
📊 نسبة التحصيل: {(total_paid/total_sales*100) if total_sales > 0 else 0:.1f}%

تفاصيل العقود:
{'='*40}
"""

            for i, contract in enumerate(contracts, 1):
                total_text = f"{contract['total_amount']:,.0f} ريال" if contract['total_amount'] else "0"
                paid_text = f"{contract['paid_amount']:,.0f} ريال" if contract['paid_amount'] else "0"
                remaining_text = f"{contract['remaining_amount']:,.0f} ريال" if contract['remaining_amount'] else "0"

                type_text = {
                    'sale': '💰 بيع',
                    'reservation': '📋 حجز'
                }.get(contract['contract_type'], contract['contract_type'] or 'غير محدد')

                report += f"""
{i}. 📄 عقد رقم: {contract['contract_number']}
   💰 المبلغ الإجمالي: {total_text}
   💳 المبلغ المدفوع: {paid_text}
   💰 المتبقي: {remaining_text}
   📊 النوع: {type_text}
   📅 تاريخ العقد: {contract['contract_date'] or 'غير محدد'}
   {'─' * 50}
"""

            self.reports_text.delete('1.0', tk.END)
            self.reports_text.insert('1.0', report)
            self.update_status("💰 تم إنشاء تقرير المبيعات")

        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير المبيعات: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في إنشاء تقرير المبيعات:\n{str(e)}")

    def generate_financial_report(self):
        """إنشاء التقرير المالي"""
        try:
            # إحصائيات العقود
            contracts_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM contracts")[0][0]
            total_sales = sqlite_manager.execute_query("SELECT COALESCE(SUM(total_amount), 0) FROM contracts")[0][0]
            total_paid = sqlite_manager.execute_query("SELECT COALESCE(SUM(paid_amount), 0) FROM contracts")[0][0]

            # إحصائيات المدفوعات
            payments_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM payments")[0][0]
            cash_payments = sqlite_manager.execute_query("SELECT COALESCE(SUM(amount), 0) FROM payments WHERE payment_method='cash'")[0][0]
            transfer_payments = sqlite_manager.execute_query("SELECT COALESCE(SUM(amount), 0) FROM payments WHERE payment_method='bank_transfer'")[0][0]

            report = f"""
📊 التقرير المالي الشامل
{'='*60}
📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

💰 ملخص العقود:
{'='*40}
📋 عدد العقود: {contracts_count}
💵 إجمالي قيمة العقود: {total_sales:,.0f} ريال
💳 إجمالي المدفوعات: {total_paid:,.0f} ريال
💰 المبالغ المتبقية: {total_sales - total_paid:,.0f} ريال
📊 نسبة التحصيل: {(total_paid/total_sales*100) if total_sales > 0 else 0:.1f}%

💳 تفاصيل المدفوعات:
{'='*40}
📋 عدد المدفوعات: {payments_count}
💵 المدفوعات النقدية: {cash_payments:,.0f} ريال
🏦 التحويلات البنكية: {transfer_payments:,.0f} ريال

📈 مؤشرات الأداء:
{'='*40}
💰 متوسط قيمة العقد: {total_sales/contracts_count if contracts_count > 0 else 0:,.0f} ريال
💳 متوسط الدفعة: {total_paid/payments_count if payments_count > 0 else 0:,.0f} ريال

🎯 التوصيات:
{'='*40}
• متابعة تحصيل المبالغ المتبقية البالغة {total_sales - total_paid:,.0f} ريال
• تحسين طرق الدفع المتاحة لزيادة التحصيل
• زيادة عدد العقود الجديدة لتحسين الإيرادات
• تطوير استراتيجيات تسويقية جديدة
"""

            self.reports_text.delete('1.0', tk.END)
            self.reports_text.insert('1.0', report)
            self.update_status("📊 تم إنشاء التقرير المالي")

        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير المالي: {e}")
            show_info_dialog(self.root, "خطأ", f"فشل في إنشاء التقرير المالي:\n{str(e)}")

    def logout(self):
        """تسجيل الخروج"""
        if show_confirm_dialog(self.root, "تأكيد الخروج", "هل تريد تسجيل الخروج من النظام؟"):
            self.root.destroy()

    def run(self):
        """تشغيل النظام"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        # نافذة تسجيل الدخول
        login_window = LoginWindow()
        user_data = login_window.run()

        if user_data:
            # النظام الرئيسي المحسن
            main_system = RafeaEnhancedSystem(user_data)
            main_system.run()
        else:
            print("تم إلغاء تسجيل الدخول")

    except Exception as e:
        logger.error(f"خطأ في تشغيل النظام: {e}")
        messagebox.showerror("خطأ", f"فشل في تشغيل النظام:\n{str(e)}")

if __name__ == "__main__":
    main()
