#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لأزرار مربعات الحوار
"""

import tkinter as tk
from tkinter import ttk, messagebox
from rafea_enhanced_dialogs import ProjectDialog

def test_button_functionality():
    """اختبار وظائف الأزرار"""
    
    def on_add_project():
        """اختبار زر إضافة مشروع"""
        try:
            dialog = ProjectDialog(root, "اختبار إضافة مشروع")
            root.wait_window(dialog.dialog)
            
            if dialog.result:
                result_text = "تم حفظ البيانات بنجاح:\n"
                for key, value in dialog.result.items():
                    result_text += f"• {key}: {value}\n"
                messagebox.showinfo("نجح", result_text)
            else:
                messagebox.showinfo("إلغاء", "تم إلغاء العملية")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
    
    # إنشاء النافذة الرئيسية
    root = tk.Tk()
    root.title("اختبار أزرار مربعات الحوار")
    root.geometry("400x300")
    root.configure(bg='white')
    
    # عنوان
    title_label = tk.Label(root, text="اختبار أزرار مربعات الحوار", 
                          font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
    title_label.pack(pady=20)
    
    # زر الاختبار
    test_btn = tk.Button(root, text="🧪 اختبار مربع حوار المشروع", 
                        bg='#3498db', fg='white', font=('Arial', 14, 'bold'),
                        command=on_add_project, width=25, height=2,
                        relief='raised', bd=3, cursor='hand2')
    test_btn.pack(pady=20)
    
    # تعليمات
    instructions = tk.Label(root, 
                           text="انقر على الزر أعلاه لاختبار مربع الحوار\n"
                                "تأكد من أن أزرار الحفظ والإلغاء تعمل بشكل صحيح",
                           font=('Arial', 12), bg='white', fg='#7f8c8d',
                           justify='center')
    instructions.pack(pady=20)
    
    # زر الخروج
    exit_btn = tk.Button(root, text="❌ خروج", bg='#e74c3c', fg='white',
                        font=('Arial', 12, 'bold'), command=root.quit,
                        width=15, height=1, relief='raised', bd=2)
    exit_btn.pack(pady=20)
    
    # تشغيل النافذة
    root.mainloop()

if __name__ == "__main__":
    test_button_functionality()
