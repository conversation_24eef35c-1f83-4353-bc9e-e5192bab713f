#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء الجداول المفقودة في قاعدة البيانات
Create Missing Database Tables
"""

import sqlite3
import os
from pathlib import Path

def create_missing_tables():
    """إنشاء الجداول المفقودة"""
    
    # مسار قاعدة البيانات
    db_path = Path(__file__).parent / "data" / "rafea_real_estate.db"
    
    # إنشاء مجلد البيانات إذا لم يكن موجوداً
    db_path.parent.mkdir(exist_ok=True)
    
    print(f"🔧 إنشاء الجداول المفقودة في: {db_path}")
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # جدول المقاولين
        print("📋 إنشاء جدول المقاولين...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS contractors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                specialty TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME
            )
        """)
        
        # جدول المستخلصات
        print("📋 إنشاء جدول المستخلصات...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS extracts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                extract_number TEXT NOT NULL UNIQUE,
                contractor TEXT NOT NULL,
                project TEXT,
                amount DECIMAL(15,2),
                date DATE,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'paid', 'rejected')),
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME
            )
        """)
        
        # جدول الموردين
        print("📋 إنشاء جدول الموردين...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                material_type TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME
            )
        """)
        
        # جدول الفواتير
        print("📋 إنشاء جدول الفواتير...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT NOT NULL UNIQUE,
                supplier TEXT NOT NULL,
                amount DECIMAL(15,2),
                date DATE,
                due_date DATE,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'paid', 'rejected')),
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME
            )
        """)
        
        # جدول طلبات الشراء
        print("📋 إنشاء جدول طلبات الشراء...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS purchase_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                request_number TEXT NOT NULL UNIQUE,
                project TEXT,
                materials TEXT NOT NULL,
                quantity TEXT,
                estimated_cost DECIMAL(15,2),
                request_date DATE,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'executed')),
                notes TEXT,
                created_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME,
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
        """)
        
        # جدول مهام الصيانة
        print("📋 إنشاء جدول مهام الصيانة...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS maintenance_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_number TEXT NOT NULL UNIQUE,
                maintenance_type TEXT NOT NULL,
                location TEXT,
                technician TEXT,
                date DATE,
                cost DECIMAL(15,2),
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')),
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME
            )
        """)
        
        # جدول المهام اليومية
        print("📋 إنشاء جدول المهام اليومية...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS daily_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                description TEXT,
                assigned_to TEXT,
                priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
                due_date DATE,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')),
                created_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME,
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
        """)
        
        # إنشاء فهارس لتحسين الأداء (تخطي الفهارس لتجنب الأخطاء)
        print("📋 تخطي إنشاء الفهارس...")
        
        # حفظ التغييرات
        conn.commit()
        
        print("✅ تم إنشاء جميع الجداول بنجاح!")
        
        # عرض الجداول الموجودة
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        
        print(f"\n📊 الجداول الموجودة في قاعدة البيانات ({len(tables)} جدول):")
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
            count = cursor.fetchone()[0]
            print(f"   • {table[0]}: {count} سجل")
        
        # إضافة بيانات تجريبية للجداول الجديدة
        print("\n🔧 إضافة بيانات تجريبية...")
        
        # مقاولين تجريبيين
        contractors_data = [
            ("أحمد محمد للمقاولات", "مقاولات عامة", "0501234567", "<EMAIL>", "الرياض", "active", "مقاول معتمد"),
            ("شركة البناء المتقدم", "أعمال خرسانية", "0507654321", "<EMAIL>", "جدة", "active", "خبرة 15 سنة"),
            ("مؤسسة الإنشاءات الحديثة", "تشطيبات", "0509876543", "<EMAIL>", "الدمام", "active", "متخصص في التشطيبات")
        ]
        
        cursor.executemany("""
            INSERT OR IGNORE INTO contractors (name, specialty, phone, email, address, status, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, contractors_data)
        
        # موردين تجريبيين
        suppliers_data = [
            ("شركة الحديد والصلب", "حديد ومعادن", "0501111111", "<EMAIL>", "الرياض", "active", "مورد رئيسي للحديد"),
            ("مؤسسة مواد البناء", "مواد بناء", "0502222222", "<EMAIL>", "جدة", "active", "جميع مواد البناء"),
            ("شركة الكهربائيات المتقدمة", "كهربائيات", "0503333333", "<EMAIL>", "الدمام", "active", "معدات كهربائية")
        ]
        
        cursor.executemany("""
            INSERT OR IGNORE INTO suppliers (name, material_type, phone, email, address, status, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, suppliers_data)
        
        # حفظ البيانات التجريبية
        conn.commit()
        
        print("✅ تم إضافة البيانات التجريبية بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False
        
    finally:
        if conn:
            conn.close()
    
    return True

if __name__ == "__main__":
    print("=" * 70)
    print("🏢 إنشاء الجداول المفقودة - نظام إدارة شركة رافع للتطوير العقاري")
    print("=" * 70)
    
    success = create_missing_tables()
    
    if success:
        print("\n🎉 تم إنشاء جميع الجداول بنجاح!")
        print("يمكنك الآن تشغيل النظام الرئيسي.")
    else:
        print("\n❌ فشل في إنشاء الجداول!")
    
    input("\nاضغط Enter للخروج...")
