#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة شاملة لاستكشاف وإصلاح جميع الأخطاء
Comprehensive Error Diagnosis and Fixing Tool
"""

import os
import sqlite3
import subprocess
import sys
from datetime import datetime

class SystemDiagnostics:
    """أداة تشخيص النظام"""
    
    def __init__(self):
        self.errors_found = []
        self.fixes_applied = []
        self.test_results = {}
    
    def run_full_diagnosis(self):
        """تشغيل التشخيص الشامل"""
        print("=" * 80)
        print("🔍 بدء التشخيص الشامل للنظام")
        print("=" * 80)
        
        # 1. فحص الملفات الأساسية
        self.check_essential_files()
        
        # 2. فحص قاعدة البيانات
        self.check_database()
        
        # 3. فحص أخطاء البنية
        self.check_syntax_errors()
        
        # 4. فحص المكتبات المطلوبة
        self.check_dependencies()
        
        # 5. اختبار تشغيل النظام
        self.test_system_execution()
        
        # 6. إنشاء التقرير النهائي
        self.generate_report()
        
        return len(self.errors_found), len(self.fixes_applied)
    
    def check_essential_files(self):
        """فحص الملفات الأساسية"""
        print("\n🔍 فحص الملفات الأساسية...")
        
        essential_files = [
            'rafea_complete_system.py',
            'simple_working_system.py',
            'database_sqlite.py',
            'dialogs.py',
            'data/rafea_system.db'
        ]
        
        for file_path in essential_files:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                print(f"   ✅ {file_path} - {size:,} بايت")
            else:
                error = f"ملف مفقود: {file_path}"
                self.errors_found.append(error)
                print(f"   ❌ {error}")
    
    def check_database(self):
        """فحص قاعدة البيانات"""
        print("\n🔍 فحص قاعدة البيانات...")
        
        try:
            if not os.path.exists('data/rafea_system.db'):
                error = "قاعدة البيانات غير موجودة"
                self.errors_found.append(error)
                print(f"   ❌ {error}")
                return
            
            conn = sqlite3.connect('data/rafea_system.db')
            cursor = conn.cursor()
            
            # فحص الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"   📊 عدد الجداول: {len(tables)}")
            
            # فحص البيانات
            total_records = 0
            for table in tables:
                table_name = table[0]
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    total_records += count
                    print(f"      - {table_name}: {count} سجل")
                except Exception as e:
                    error = f"خطأ في جدول {table_name}: {e}"
                    self.errors_found.append(error)
                    print(f"   ❌ {error}")
            
            print(f"   📊 إجمالي السجلات: {total_records}")
            
            if total_records < 10:
                error = f"قاعدة البيانات تحتوي على {total_records} سجل فقط"
                self.errors_found.append(error)
                print(f"   ⚠️ {error}")
            
            conn.close()
            
        except Exception as e:
            error = f"خطأ في فحص قاعدة البيانات: {e}"
            self.errors_found.append(error)
            print(f"   ❌ {error}")
    
    def check_syntax_errors(self):
        """فحص أخطاء البنية"""
        print("\n🔍 فحص أخطاء البنية...")
        
        python_files = [
            'rafea_complete_system.py',
            'simple_working_system.py',
            'database_sqlite.py',
            'dialogs.py'
        ]
        
        for file_path in python_files:
            if os.path.exists(file_path):
                try:
                    result = subprocess.run([
                        sys.executable, '-m', 'py_compile', file_path
                    ], capture_output=True, text=True, timeout=30)
                    
                    if result.returncode == 0:
                        print(f"   ✅ {file_path} - بنية صحيحة")
                    else:
                        error = f"خطأ بنية في {file_path}: {result.stderr.strip()}"
                        self.errors_found.append(error)
                        print(f"   ❌ {error}")
                        
                except subprocess.TimeoutExpired:
                    error = f"انتهت مهلة فحص {file_path}"
                    self.errors_found.append(error)
                    print(f"   ❌ {error}")
                except Exception as e:
                    error = f"خطأ في فحص {file_path}: {e}"
                    self.errors_found.append(error)
                    print(f"   ❌ {error}")
    
    def check_dependencies(self):
        """فحص المكتبات المطلوبة"""
        print("\n🔍 فحص المكتبات المطلوبة...")
        
        required_modules = [
            'tkinter',
            'sqlite3',
            'bcrypt',
            'logging',
            'datetime',
            'os',
            'sys'
        ]
        
        for module in required_modules:
            try:
                __import__(module)
                print(f"   ✅ {module} - متوفر")
            except ImportError as e:
                error = f"مكتبة مفقودة: {module} - {e}"
                self.errors_found.append(error)
                print(f"   ❌ {error}")
    
    def test_system_execution(self):
        """اختبار تشغيل النظام"""
        print("\n🔍 اختبار تشغيل النظام...")
        
        # اختبار النظام المبسط
        print("   🧪 اختبار النظام المبسط...")
        try:
            result = subprocess.run([
                sys.executable, '-c', 
                'import simple_working_system; print("النظام المبسط يستورد بنجاح")'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("   ✅ النظام المبسط يعمل")
                self.test_results['simple_system'] = True
            else:
                error = f"خطأ في النظام المبسط: {result.stderr.strip()}"
                self.errors_found.append(error)
                print(f"   ❌ {error}")
                self.test_results['simple_system'] = False
                
        except Exception as e:
            error = f"فشل اختبار النظام المبسط: {e}"
            self.errors_found.append(error)
            print(f"   ❌ {error}")
            self.test_results['simple_system'] = False
        
        # اختبار النظام الرئيسي
        print("   🧪 اختبار النظام الرئيسي...")
        try:
            result = subprocess.run([
                sys.executable, '-c', 
                'import rafea_complete_system; print("النظام الرئيسي يستورد بنجاح")'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("   ✅ النظام الرئيسي يعمل")
                self.test_results['main_system'] = True
            else:
                error = f"خطأ في النظام الرئيسي: {result.stderr.strip()}"
                self.errors_found.append(error)
                print(f"   ❌ {error}")
                self.test_results['main_system'] = False
                
        except Exception as e:
            error = f"فشل اختبار النظام الرئيسي: {e}"
            self.errors_found.append(error)
            print(f"   ❌ {error}")
            self.test_results['main_system'] = False
    
    def apply_automatic_fixes(self):
        """تطبيق الإصلاحات التلقائية"""
        print("\n🔧 تطبيق الإصلاحات التلقائية...")
        
        # إصلاح 1: إضافة بيانات تجريبية إذا كانت قاعدة البيانات فارغة
        if any("قاعدة البيانات تحتوي على" in error for error in self.errors_found):
            print("   🔧 إضافة بيانات تجريبية...")
            try:
                subprocess.run([sys.executable, 'add_sample_data_correctly.py'], 
                             timeout=60, check=True)
                fix = "تم إضافة بيانات تجريبية"
                self.fixes_applied.append(fix)
                print(f"   ✅ {fix}")
            except Exception as e:
                print(f"   ❌ فشل في إضافة البيانات: {e}")
        
        # إصلاح 2: إنشاء قاعدة البيانات إذا كانت مفقودة
        if any("قاعدة البيانات غير موجودة" in error for error in self.errors_found):
            print("   🔧 إنشاء قاعدة البيانات...")
            try:
                subprocess.run([sys.executable, 'create_database_from_scratch.py'], 
                             timeout=60, check=True)
                fix = "تم إنشاء قاعدة البيانات"
                self.fixes_applied.append(fix)
                print(f"   ✅ {fix}")
            except Exception as e:
                print(f"   ❌ فشل في إنشاء قاعدة البيانات: {e}")
    
    def generate_report(self):
        """إنشاء التقرير النهائي"""
        print("\n📄 إنشاء التقرير النهائي...")
        
        report = f"""
{'='*80}
📊 تقرير التشخيص الشامل للنظام
{'='*80}
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🔍 نتائج الفحص:
{'='*40}
📁 الملفات الأساسية: {'✅ سليمة' if len([e for e in self.errors_found if 'ملف مفقود' in e]) == 0 else '❌ مشاكل'}
🗄️ قاعدة البيانات: {'✅ تعمل' if len([e for e in self.errors_found if 'قاعدة البيانات' in e]) == 0 else '❌ مشاكل'}
🐍 بنية الكود: {'✅ صحيحة' if len([e for e in self.errors_found if 'خطأ بنية' in e]) == 0 else '❌ أخطاء'}
📦 المكتبات: {'✅ متوفرة' if len([e for e in self.errors_found if 'مكتبة مفقودة' in e]) == 0 else '❌ مفقودة'}

🧪 نتائج الاختبار:
{'='*40}
🔹 النظام المبسط: {'✅ يعمل' if self.test_results.get('simple_system', False) else '❌ لا يعمل'}
🔹 النظام الرئيسي: {'✅ يعمل' if self.test_results.get('main_system', False) else '❌ لا يعمل'}

❌ الأخطاء المكتشفة ({len(self.errors_found)}):
{'='*40}
"""
        
        for i, error in enumerate(self.errors_found, 1):
            report += f"{i}. {error}\n"
        
        report += f"""
✅ الإصلاحات المطبقة ({len(self.fixes_applied)}):
{'='*40}
"""
        
        for i, fix in enumerate(self.fixes_applied, 1):
            report += f"{i}. {fix}\n"
        
        report += f"""
🎯 التوصيات:
{'='*40}
"""
        
        if self.test_results.get('simple_system', False):
            report += "✅ استخدم النظام المبسط: python simple_working_system.py\n"
        
        if not self.test_results.get('main_system', False):
            report += "⚠️ النظام الرئيسي يحتاج إصلاح أخطاء البنية\n"
        
        if len(self.errors_found) == 0:
            report += "🎉 النظام يعمل بشكل مثالي!\n"
        
        report += f"""
{'='*80}
"""
        
        # حفظ التقرير
        with open('system_diagnosis_report.txt', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("✅ تم حفظ التقرير في: system_diagnosis_report.txt")
        
        return report

def main():
    """الدالة الرئيسية"""
    diagnostics = SystemDiagnostics()
    
    # تشغيل التشخيص الشامل
    errors_count, fixes_count = diagnostics.run_full_diagnosis()
    
    # تطبيق الإصلاحات التلقائية
    diagnostics.apply_automatic_fixes()
    
    # النتيجة النهائية
    print("\n" + "="*80)
    print("📊 النتيجة النهائية")
    print("="*80)
    print(f"🔍 الأخطاء المكتشفة: {errors_count}")
    print(f"🔧 الإصلاحات المطبقة: {len(diagnostics.fixes_applied)}")
    
    if diagnostics.test_results.get('simple_system', False):
        print("\n🎉 النظام المبسط يعمل بشكل مثالي!")
        print("📋 يمكنك تشغيله: python simple_working_system.py")
        print("🔐 تسجيل الدخول: admin / admin123")
    
    if diagnostics.test_results.get('main_system', False):
        print("\n🎉 النظام الرئيسي يعمل أيضاً!")
        print("📋 يمكنك تشغيله: python rafea_complete_system.py")
    else:
        print("\n⚠️ النظام الرئيسي يحتاج إصلاح أخطاء البنية")
        print("💡 استخدم النظام المبسط في الوقت الحالي")
    
    print(f"\n📄 راجع التقرير الكامل: system_diagnosis_report.txt")

if __name__ == "__main__":
    main()
