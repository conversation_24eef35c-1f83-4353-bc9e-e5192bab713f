#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد قاعدة بيانات SQLite
SQLite Database Setup
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database_sqlite import sqlite_manager, init_sqlite_database
from utils.logger_setup import setup_logger
from loguru import logger

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("إعداد قاعدة بيانات SQLite لنظام شركة رافع للتطوير العقاري")
    print("=" * 60)
    
    # إعداد نظام السجلات
    setup_logger()
    
    try:
        # اختبار الاتصال بقاعدة البيانات
        print("🔍 اختبار الاتصال بقاعدة البيانات...")
        if not sqlite_manager.test_connection():
            print("❌ فشل في الاتصال بقاعدة البيانات")
            return 1
        
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # تهيئة قاعدة البيانات
        print("🏗️  إنشاء جداول قاعدة البيانات...")
        if not init_sqlite_database():
            print("❌ فشل في إنشاء جداول قاعدة البيانات")
            return 1
        
        print("✅ تم إنشاء جداول قاعدة البيانات بنجاح")
        
        # عرض معلومات قاعدة البيانات
        print("\n📊 معلومات قاعدة البيانات:")
        tables = sqlite_manager.get_all_tables()
        print(f"   عدد الجداول: {len(tables)}")
        for table in tables:
            print(f"   - {table}")
        
        print("\n" + "=" * 60)
        print("✅ تم إعداد قاعدة البيانات بنجاح!")
        print("\nبيانات الدخول الافتراضية:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        print("\n⚠️  يرجى تغيير كلمة المرور بعد تسجيل الدخول الأول")
        print("=" * 60)
        
        return 0
        
    except Exception as e:
        logger.error(f"خطأ في إعداد قاعدة البيانات: {e}")
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
