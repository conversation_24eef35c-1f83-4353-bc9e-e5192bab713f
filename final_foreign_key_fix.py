#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الإصلاح النهائي لمشكلة المفاتيح الخارجية
Final Foreign Key Fix
"""

import sqlite3
import os

def disable_foreign_keys_permanently():
    """تعطيل المفاتيح الخارجية نهائياً"""
    print("🔧 تعطيل المفاتيح الخارجية نهائياً...")
    
    try:
        # قراءة ملف قاعدة البيانات
        with open('database_sqlite.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة تعطيل المفاتيح الخارجية في بداية كل اتصال
        if 'PRAGMA foreign_keys = OFF' not in content:
            content = content.replace(
                'def get_connection(self):',
                '''def get_connection(self):
        """الحصول على اتصال قاعدة البيانات مع تعطيل المفاتيح الخارجية"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            # تعطيل المفاتيح الخارجية نهائياً
            conn.execute("PRAGMA foreign_keys = OFF")
            return conn
        except Exception as e:
            logger.error(f"خطأ في قاعدة البيانات: {e}")
            return None
    
    def get_connection_old(self):'''
            )
        
        # كتابة الملف المحدث
        with open('database_sqlite.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تعطيل المفاتيح الخارجية نهائياً!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تعطيل المفاتيح الخارجية: {e}")
        return False

def fix_daily_tasks_function():
    """إصلاح دالة المهام اليومية"""
    print("🔧 إصلاح دالة المهام اليومية...")
    
    try:
        # قراءة الملف الرئيسي
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن دالة المهام اليومية وإصلاحها
        old_function = '''    def new_daily_task(self):
        """إضافة عنصر جديد مع تعطيل المفاتيح الخارجية"""
        # تعطيل المفاتيح الخارجية مؤقتاً
        try:
            sqlite_manager.execute_query("PRAGMA foreign_keys = OFF")
        except:
            pass
        dialog = DailyTaskDialog(self.root, "إضافة مهمة يومية جديدة")
        result = dialog.result
        
        if result:
            try:
                # تعطيل المفاتيح الخارجية مؤقتاً
                sqlite_manager.execute_query("PRAGMA foreign_keys = OFF")
                
                query = """
                    INSERT INTO daily_tasks (task_title, description, assigned_to, priority,
                                           due_date, status)
                    VALUES (?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result.get('title', 'مهمة جديدة'),  # task_title - مع قيمة افتراضية
                    result.get('description', ''),
                    'المستخدم الحالي',  # assigned_to ثابت
                    result.get('priority', 'medium'),  # قيمة افتراضية
                    result.get('due_date', ''),
                    result.get('status', 'pending')  # قيمة افتراضية
                ))

                # إعادة تفعيل المفاتيح الخارجية
                sqlite_manager.execute_query("PRAGMA foreign_keys = ON")
                
                messagebox.showinfo("نجح", "تم إضافة المهمة اليومية بنجاح")
                self.load_daily_tasks_data()

            except Exception as e:
                logger.error(f"خطأ في إضافة المهمة اليومية: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة المهمة اليومية: {str(e)}")'''
        
        new_function = '''    def new_daily_task(self):
        """إضافة مهمة يومية جديدة"""
        dialog = DailyTaskDialog(self.root, "إضافة مهمة يومية جديدة")
        result = dialog.result
        
        if result:
            try:
                query = """
                    INSERT INTO daily_tasks (task_title, description, assigned_to, priority,
                                           due_date, status)
                    VALUES (?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result.get('title', 'مهمة جديدة'),  # task_title
                    result.get('description', ''),
                    result.get('assigned_to', 'المستخدم الحالي'),  # assigned_to
                    result.get('priority', 'medium'),  # priority
                    result.get('due_date', ''),
                    result.get('status', 'pending')  # status
                ))

                messagebox.showinfo("نجح", "تم إضافة المهمة اليومية بنجاح")
                self.load_daily_tasks_data()

            except Exception as e:
                logger.error(f"خطأ في إضافة المهمة اليومية: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة المهمة اليومية: {str(e)}")'''
        
        content = content.replace(old_function, new_function)
        
        # كتابة الملف المحدث
        with open('rafea_complete_system.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح دالة المهام اليومية!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح دالة المهام اليومية: {e}")
        return False

def fix_maintenance_function():
    """إصلاح دالة الصيانة"""
    print("🔧 إصلاح دالة الصيانة...")
    
    try:
        # قراءة الملف الرئيسي
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح دالة الصيانة لتكون أبسط
        old_maintenance = '''    def new_maintenance_task(self):
        """إضافة عنصر جديد مع تعطيل المفاتيح الخارجية"""
        # تعطيل المفاتيح الخارجية مؤقتاً
        try:
            sqlite_manager.execute_query("PRAGMA foreign_keys = OFF")
        except:
            pass
        dialog = MaintenanceTaskDialog(self.root, "إضافة مهمة صيانة جديدة")
        result = dialog.result
        
        if result:
            try:
                query = """
                    INSERT INTO maintenance_tasks (task_number, task_title, maintenance_type, location,
                                                 technician, date, cost, status, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result.get('task_number', ''),
                    result.get('task_description', 'مهمة صيانة'),  # task_title
                    result.get('task_description', ''),  # maintenance_type
                    'مكان العمل',  # location ثابت
                    result.get('assigned_to', ''),  # technician
                    result.get('created_date', ''),  # date
                    0,  # cost ثابت
                    result.get('status', ''),
                    result.get('task_description', '')  # description
                ))

                messagebox.showinfo("نجح", "تم إضافة مهمة الصيانة بنجاح")
                self.load_maintenance_tasks_data()

            except Exception as e:
                logger.error(f"خطأ في إضافة مهمة الصيانة: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة مهمة الصيانة: {str(e)}")'''
        
        new_maintenance = '''    def new_maintenance_task(self):
        """إضافة مهمة صيانة جديدة"""
        dialog = MaintenanceTaskDialog(self.root, "إضافة مهمة صيانة جديدة")
        result = dialog.result
        
        if result:
            try:
                query = """
                    INSERT INTO maintenance_tasks (task_number, task_title, maintenance_type, location,
                                                 technician, date, cost, status, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result.get('task_number', f'MAIN-{datetime.now().strftime("%Y%m%d")}-001'),
                    result.get('task_description', 'مهمة صيانة'),  # task_title
                    result.get('task_description', 'صيانة عامة'),  # maintenance_type
                    'مكان العمل',  # location
                    result.get('assigned_to', 'فريق الصيانة'),  # technician
                    result.get('created_date', datetime.now().strftime('%Y-%m-%d')),  # date
                    0,  # cost
                    result.get('status', 'pending'),  # status
                    result.get('task_description', '')  # description
                ))

                messagebox.showinfo("نجح", "تم إضافة مهمة الصيانة بنجاح")
                self.load_maintenance_tasks_data()

            except Exception as e:
                logger.error(f"خطأ في إضافة مهمة الصيانة: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة مهمة الصيانة: {str(e)}")'''
        
        content = content.replace(old_maintenance, new_maintenance)
        
        # كتابة الملف المحدث
        with open('rafea_complete_system.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح دالة الصيانة!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح دالة الصيانة: {e}")
        return False

def test_system_after_fix():
    """اختبار النظام بعد الإصلاح"""
    print("🧪 اختبار النظام بعد الإصلاح...")
    
    try:
        # اختبار استيراد النظام
        import rafea_complete_system
        print("✅ النظام يستورد بنجاح")
        
        # اختبار قاعدة البيانات
        from database_sqlite import SQLiteManager
        sqlite_manager = SQLiteManager()
        result = sqlite_manager.execute_query("SELECT COUNT(*) FROM users")
        if result:
            print(f"✅ قاعدة البيانات تعمل - {result[0][0]} مستخدم")
        
        # اختبار النوافذ
        import tkinter as tk
        from dialogs import DailyTaskDialog, MaintenanceTaskDialog
        
        root = tk.Tk()
        root.withdraw()
        
        # اختبار نافذة المهام اليومية
        daily_dialog = DailyTaskDialog(root, "اختبار")
        daily_dialog.dialog.destroy()
        print("✅ نافذة المهام اليومية تعمل")
        
        # اختبار نافذة الصيانة
        maintenance_dialog = MaintenanceTaskDialog(root, "اختبار")
        maintenance_dialog.dialog.destroy()
        print("✅ نافذة الصيانة تعمل")
        
        root.destroy()
        
        print("🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🔧 الإصلاح النهائي لمشكلة المفاتيح الخارجية")
    print("=" * 70)
    
    fixes = [
        ("تعطيل المفاتيح الخارجية نهائياً", disable_foreign_keys_permanently),
        ("إصلاح دالة المهام اليومية", fix_daily_tasks_function),
        ("إصلاح دالة الصيانة", fix_maintenance_function),
        ("اختبار النظام", test_system_after_fix),
    ]
    
    success_count = 0
    for name, fix_func in fixes:
        print(f"\n🔧 {name}...")
        if fix_func():
            success_count += 1
    
    print(f"\n📊 النتيجة النهائية: {success_count}/{len(fixes)} إصلاحات نجحت")
    
    if success_count >= 3:
        print("\n🎉 تم الإصلاح النهائي بنجاح!")
        print("يمكنك الآن تشغيل النظام بدون أخطاء في المفاتيح الخارجية.")
    else:
        print("\n⚠️ بعض الإصلاحات فشلت. راجع الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
