#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مربعات الحوار
"""

import tkinter as tk
from tkinter import ttk, messagebox
from rafea_enhanced_dialogs import ProjectDialog, CustomerDialog, UnitDialog

def test_project_dialog():
    """اختبار مربع حوار المشاريع"""
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    dialog = ProjectDialog(root, "اختبار مربع حوار المشروع")
    root.wait_window(dialog.dialog)
    
    if dialog.result:
        print("نتيجة مربع حوار المشروع:")
        for key, value in dialog.result.items():
            print(f"  {key}: {value}")
    else:
        print("تم إلغاء مربع حوار المشروع")
    
    root.destroy()

def test_customer_dialog():
    """اختبار مربع حوار العملاء"""
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    dialog = CustomerDialog(root, "اختبار مربع حوار العميل")
    root.wait_window(dialog.dialog)
    
    if dialog.result:
        print("نتيجة مربع حوار العميل:")
        for key, value in dialog.result.items():
            print(f"  {key}: {value}")
    else:
        print("تم إلغاء مربع حوار العميل")
    
    root.destroy()

def test_unit_dialog():
    """اختبار مربع حوار الوحدات"""
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    dialog = UnitDialog(root, "اختبار مربع حوار الوحدة")
    root.wait_window(dialog.dialog)
    
    if dialog.result:
        print("نتيجة مربع حوار الوحدة:")
        for key, value in dialog.result.items():
            print(f"  {key}: {value}")
    else:
        print("تم إلغاء مربع حوار الوحدة")
    
    root.destroy()

def main():
    """الدالة الرئيسية للاختبار"""
    print("=== اختبار مربعات الحوار ===")
    
    # اختبار مربع حوار المشاريع
    print("\n1. اختبار مربع حوار المشاريع...")
    test_project_dialog()
    
    # اختبار مربع حوار العملاء
    print("\n2. اختبار مربع حوار العملاء...")
    test_customer_dialog()
    
    # اختبار مربع حوار الوحدات
    print("\n3. اختبار مربع حوار الوحدات...")
    test_unit_dialog()
    
    print("\n=== انتهى الاختبار ===")

if __name__ == "__main__":
    main()
