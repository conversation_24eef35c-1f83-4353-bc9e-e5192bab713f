# -*- coding: utf-8 -*-
"""
ملف إعداد التثبيت
Setup Script
"""

from setuptools import setup, find_packages
import os

# قراءة ملف README
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# قراءة المتطلبات
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="rafea-real-estate-system",
    version="1.0.0",
    author="فريق التطوير",
    author_email="<EMAIL>",
    description="نظام إدارة شامل لشركة رافع للتطوير العقاري",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/rafea/real-estate-system",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: Microsoft :: Windows",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business",
        "Topic :: Database :: Front-Ends",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    entry_points={
        "console_scripts": [
            "rafea-system=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.sql", "*.qss", "*.png", "*.ico"],
    },
    zip_safe=False,
)
