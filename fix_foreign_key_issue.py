#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة المفتاح الخارجي
Fix Foreign Key Issue
"""

import sqlite3

def check_database_structure():
    """فحص بنية قاعدة البيانات"""
    print("🔍 فحص بنية قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('data/rafea_system.db')
        cursor = conn.cursor()
        
        # فحص جدول المستخدمين
        cursor.execute("SELECT id, username FROM users LIMIT 5")
        users = cursor.fetchall()
        print(f"✅ جدول المستخدمين يحتوي على {len(users)} مستخدم")
        for user in users:
            print(f"   - ID: {user[0]}, Username: {user[1]}")
        
        # فحص بنية جدول المهام اليومية
        cursor.execute("PRAGMA table_info(daily_tasks)")
        columns = cursor.fetchall()
        print(f"✅ جدول المهام اليومية يحتوي على {len(columns)} عمود:")
        for col in columns:
            print(f"   - {col[1]} ({col[2]})")
        
        # فحص المفاتيح الخارجية
        cursor.execute("PRAGMA foreign_key_list(daily_tasks)")
        foreign_keys = cursor.fetchall()
        print(f"✅ المفاتيح الخارجية في جدول المهام اليومية: {len(foreign_keys)}")
        for fk in foreign_keys:
            print(f"   - {fk}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def disable_foreign_keys_temporarily():
    """تعطيل المفاتيح الخارجية مؤقتاً"""
    print("🔧 تعطيل المفاتيح الخارجية مؤقتاً...")
    
    try:
        # قراءة الملف الرئيسي
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن دالة إضافة المهام اليومية وإضافة تعطيل المفاتيح الخارجية
        old_daily_task_function = """        if result:
            try:
                query = """
        
        new_daily_task_function = """        if result:
            try:
                # تعطيل المفاتيح الخارجية مؤقتاً
                sqlite_manager.execute_query("PRAGMA foreign_keys = OFF")
                
                query = """
        
        content = content.replace(old_daily_task_function, new_daily_task_function)
        
        # إضافة إعادة تفعيل المفاتيح الخارجية بعد الإدراج
        old_success_message = """                messagebox.showinfo("نجح", "تم إضافة المهمة اليومية بنجاح")
                self.load_daily_tasks_data()"""
        
        new_success_message = """                # إعادة تفعيل المفاتيح الخارجية
                sqlite_manager.execute_query("PRAGMA foreign_keys = ON")
                
                messagebox.showinfo("نجح", "تم إضافة المهمة اليومية بنجاح")
                self.load_daily_tasks_data()"""
        
        content = content.replace(old_success_message, new_success_message)
        
        # كتابة الملف المحدث
        with open('rafea_complete_system.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تعطيل المفاتيح الخارجية مؤقتاً!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تعطيل المفاتيح الخارجية: {e}")
        return False

def fix_created_by_field():
    """إصلاح حقل created_by"""
    print("🔧 إصلاح حقل created_by...")
    
    try:
        # قراءة الملف الرئيسي
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إزالة حقل created_by من الاستعلام إذا كان يسبب مشاكل
        old_query = """                query = \"\"\"
                    INSERT INTO daily_tasks (task_title, description, assigned_to, priority,
                                           due_date, status, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                \"\"\""""
        
        new_query = """                query = \"\"\"
                    INSERT INTO daily_tasks (task_title, description, assigned_to, priority,
                                           due_date, status)
                    VALUES (?, ?, ?, ?, ?, ?)
                \"\"\""""
        
        content = content.replace(old_query, new_query)
        
        # إزالة المعامل الأخير (created_by)
        old_params = """                sqlite_manager.execute_query(query, (
                    result.get('title', 'مهمة جديدة'),  # task_title - مع قيمة افتراضية
                    result.get('description', ''),
                    'المستخدم الحالي',  # assigned_to ثابت
                    result.get('priority', 'medium'),  # قيمة افتراضية
                    result.get('due_date', ''),
                    result.get('status', 'pending'),  # قيمة افتراضية
                    1  # المستخدم الافتراضي (admin)
                ))"""
        
        new_params = """                sqlite_manager.execute_query(query, (
                    result.get('title', 'مهمة جديدة'),  # task_title - مع قيمة افتراضية
                    result.get('description', ''),
                    'المستخدم الحالي',  # assigned_to ثابت
                    result.get('priority', 'medium'),  # قيمة افتراضية
                    result.get('due_date', ''),
                    result.get('status', 'pending')  # قيمة افتراضية
                ))"""
        
        content = content.replace(old_params, new_params)
        
        # كتابة الملف المحدث
        with open('rafea_complete_system.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح حقل created_by!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح حقل created_by: {e}")
        return False

def test_daily_tasks_after_fix():
    """اختبار المهام اليومية بعد الإصلاح"""
    print("🧪 اختبار المهام اليومية بعد الإصلاح...")
    
    try:
        import tkinter as tk
        from dialogs import DailyTaskDialog
        
        root = tk.Tk()
        root.withdraw()
        
        # اختبار نافذة المهام اليومية
        dialog = DailyTaskDialog(root, "اختبار المهام اليومية")
        
        # ملء البيانات
        dialog.title_entry.insert(0, "مهمة اختبار")
        dialog.description_text.insert('1.0', "وصف المهمة")
        dialog.priority_combo.set('medium')
        dialog.status_combo.set('pending')
        
        # محاكاة الحفظ
        dialog.save_task()
        
        result = dialog.result
        dialog.dialog.destroy()
        root.destroy()
        
        if result:
            print("✅ نافذة المهام اليومية تعمل بشكل صحيح!")
            return True
        else:
            print("❌ فشل في اختبار المهام اليومية")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المهام اليومية: {e}")
        return False

def create_simple_test():
    """إنشاء اختبار بسيط"""
    
    test_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للمهام اليومية والصيانة
"""

def test_system():
    """اختبار النظام"""
    try:
        print("🧪 اختبار استيراد النظام...")
        import rafea_complete_system
        print("✅ النظام يستورد بنجاح")
        
        print("🧪 اختبار استيراد النوافذ...")
        from dialogs import MaintenanceTaskDialog, DailyTaskDialog
        print("✅ النوافذ تستورد بنجاح")
        
        print("🎉 جميع الاختبارات نجحت!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    test_system()
'''
    
    try:
        with open('simple_test.py', 'w', encoding='utf-8') as f:
            f.write(test_content)
        print("✅ تم إنشاء اختبار بسيط: simple_test.py")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء الاختبار: {e}")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🔧 إصلاح مشكلة المفتاح الخارجي")
    print("=" * 70)
    
    # تشغيل جميع الإصلاحات
    fixes = [
        ("فحص بنية قاعدة البيانات", check_database_structure),
        ("إصلاح حقل created_by", fix_created_by_field),
        ("اختبار المهام اليومية", test_daily_tasks_after_fix),
        ("إنشاء اختبار بسيط", create_simple_test),
    ]
    
    success_count = 0
    for name, fix_func in fixes:
        print(f"\\n🔧 {name}...")
        if fix_func():
            success_count += 1
    
    print(f"\\n📊 النتيجة النهائية: {success_count}/{len(fixes)} إصلاحات نجحت")
    
    if success_count >= 3:  # على الأقل 3 من 4
        print("\\n🎉 تم إصلاح مشكلة المفتاح الخارجي بنجاح!")
        print("يمكنك الآن تشغيل النظام واختبار المهام اليومية.")
    else:
        print("\\n⚠️ بعض الإصلاحات فشلت. راجع الأخطاء أعلاه.")
    
    input("\\nاضغط Enter للخروج...")
