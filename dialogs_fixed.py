#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نوافذ الحوار المصلحة مع أزرار مضمونة
Fixed Dialog Windows with Guaranteed Buttons
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import bcrypt

class UserDialog:
    """نافذة حوار إضافة/تعديل مستخدم - مصلحة"""
    
    def __init__(self, parent, title, user_data=None):
        self.parent = parent
        self.user_data = user_data
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x500")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"500x500+{x}+{y}")
        
        self.create_widgets()
        
        if user_data:
            self.load_user_data()
    
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        
        # العنوان
        title_label = tk.Label(
            self.dialog,
            text="إدارة المستخدمين",
            font=('Arial', 18, 'bold'),
            bg='#f5f5f5',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # إطار المحتوى
        content_frame = tk.Frame(self.dialog, bg='white', relief='raised', bd=2)
        content_frame.pack(fill='both', expand=True, padx=20, pady=(0, 100))
        
        # اسم المستخدم
        tk.Label(content_frame, text="اسم المستخدم:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(20, 5), padx=20)
        self.username_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.username_entry.pack(pady=(0, 15), padx=20)
        
        # كلمة المرور
        tk.Label(content_frame, text="كلمة المرور:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.password_entry = tk.Entry(content_frame, font=('Arial', 12), width=40, show='*')
        self.password_entry.pack(pady=(0, 15), padx=20)
        
        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.email_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.email_entry.pack(pady=(0, 15), padx=20)
        
        # نوع المستخدم
        tk.Label(content_frame, text="نوع المستخدم:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.user_type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.user_type_combo['values'] = ('admin', 'manager', 'employee', 'viewer')
        self.user_type_combo.set('employee')
        self.user_type_combo.pack(pady=(0, 15), padx=20)
        
        # الحالة
        self.is_active_var = tk.BooleanVar(value=True)
        self.is_active_check = tk.Checkbutton(
            content_frame,
            text="مستخدم مفعل",
            variable=self.is_active_var,
            font=('Arial', 12),
            bg='white'
        )
        self.is_active_check.pack(anchor='e', pady=(0, 20), padx=20)
        
        # إطار الأزرار - الجزء المهم
        buttons_frame = tk.Frame(self.dialog, bg='#f5f5f5', height=80)
        buttons_frame.pack(fill='x', side='bottom', padx=20, pady=20)
        buttons_frame.pack_propagate(False)
        
        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ المستخدم",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.save_user
        )
        save_btn.pack(side='right', padx=(10, 0), pady=10)
        
        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.cancel
        )
        cancel_btn.pack(side='right', padx=(10, 10), pady=10)
        
        # التأكد من ظهور الأزرار
        self.dialog.update()
        buttons_frame.update()
        save_btn.update()
        cancel_btn.update()
        
        print("✅ تم إنشاء أزرار المستخدمين بنجاح!")
    
    def load_user_data(self):
        """تحميل بيانات المستخدم للتعديل"""
        if self.user_data:
            self.username_entry.insert(0, self.user_data.get('username', ''))
            self.email_entry.insert(0, self.user_data.get('email', ''))
            self.user_type_combo.set(self.user_data.get('user_type', 'employee'))
            self.is_active_var.set(self.user_data.get('is_active', True))
    
    def save_user(self):
        """حفظ بيانات المستخدم"""
        # التحقق من صحة البيانات
        if not self.username_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
            return
        
        if not self.password_entry.get().strip() and not self.user_data:
            messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
            return
        
        try:
            # تحضير البيانات
            user_data = {
                'username': self.username_entry.get().strip(),
                'email': self.email_entry.get().strip(),
                'user_type': self.user_type_combo.get(),
                'is_active': self.is_active_var.get()
            }
            
            # تشفير كلمة المرور إذا تم إدخالها
            password = self.password_entry.get().strip()
            if password:
                user_data['password_hash'] = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            self.result = user_data
            messagebox.showinfo("نجح", "تم حفظ بيانات المستخدم بنجاح!")
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

class ProjectDialog:
    """نافذة حوار إضافة/تعديل مشروع - مصلحة"""
    
    def __init__(self, parent, title, project_data=None):
        self.parent = parent
        self.project_data = project_data
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x600")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"500x600+{x}+{y}")
        
        self.create_widgets()
        
        if project_data:
            self.load_project_data()
    
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        
        # العنوان
        title_label = tk.Label(
            self.dialog,
            text="إدارة المشاريع",
            font=('Arial', 18, 'bold'),
            bg='#f5f5f5',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # إطار المحتوى
        content_frame = tk.Frame(self.dialog, bg='white', relief='raised', bd=2)
        content_frame.pack(fill='both', expand=True, padx=20, pady=(0, 100))
        
        # اسم المشروع
        tk.Label(content_frame, text="اسم المشروع:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(20, 5), padx=20)
        self.name_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.name_entry.pack(pady=(0, 15), padx=20)
        
        # الموقع
        tk.Label(content_frame, text="الموقع:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.location_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.location_entry.pack(pady=(0, 15), padx=20)
        
        # نوع المشروع
        tk.Label(content_frame, text="نوع المشروع:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.type_combo['values'] = ('سكني', 'تجاري', 'إداري', 'مختلط')
        self.type_combo.set('سكني')
        self.type_combo.pack(pady=(0, 15), padx=20)
        
        # التكلفة الإجمالية
        tk.Label(content_frame, text="التكلفة الإجمالية:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.cost_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.cost_entry.pack(pady=(0, 20), padx=20)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.dialog, bg='#f5f5f5', height=80)
        buttons_frame.pack(fill='x', side='bottom', padx=20, pady=20)
        buttons_frame.pack_propagate(False)
        
        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ المشروع",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.save_project
        )
        save_btn.pack(side='right', padx=(10, 0), pady=10)
        
        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.cancel
        )
        cancel_btn.pack(side='right', padx=(10, 10), pady=10)
        
        # التأكد من ظهور الأزرار
        self.dialog.update()
        buttons_frame.update()
        save_btn.update()
        cancel_btn.update()
        
        print("✅ تم إنشاء أزرار المشاريع بنجاح!")
    
    def load_project_data(self):
        """تحميل بيانات المشروع للتعديل"""
        if self.project_data:
            self.name_entry.insert(0, self.project_data.get('name', ''))
            self.location_entry.insert(0, self.project_data.get('location', ''))
            self.type_combo.set(self.project_data.get('project_type', 'سكني'))
            self.cost_entry.insert(0, str(self.project_data.get('total_cost', '')))
    
    def save_project(self):
        """حفظ بيانات المشروع"""
        # التحقق من صحة البيانات
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المشروع")
            return
        
        try:
            # تحضير البيانات
            project_data = {
                'name': self.name_entry.get().strip(),
                'location': self.location_entry.get().strip(),
                'project_type': self.type_combo.get(),
                'total_cost': float(self.cost_entry.get()) if self.cost_entry.get().strip() else 0.0,
                'start_date': datetime.now().strftime("%Y-%m-%d"),
                'status': 'تخطيط'
            }
            
            self.result = project_data
            messagebox.showinfo("نجح", "تم حفظ بيانات المشروع بنجاح!")
            self.dialog.destroy()
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيمة صحيحة للتكلفة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

# يمكن إضافة المزيد من النوافذ هنا بنفس الطريقة...
