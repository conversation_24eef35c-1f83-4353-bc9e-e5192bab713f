#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع وظائف النظام
Comprehensive System Test
"""

import sqlite3
import os
from datetime import datetime

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        conn = sqlite3.connect('data/rafea_system.db')
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        conn.close()
        
        print(f"✅ قاعدة البيانات متصلة - {len(tables)} جدول موجود")
        return True
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def test_all_modules():
    """اختبار جميع الوحدات"""
    modules = [
        'rafea_complete_system',
        'dialogs', 
        'database_sqlite',
        'user_activity_monitor',
        'multi_user_manager'
    ]
    
    success_count = 0
    for module in modules:
        try:
            __import__(module)
            print(f"✅ وحدة {module} تعمل بشكل صحيح")
            success_count += 1
        except Exception as e:
            print(f"❌ خطأ في وحدة {module}: {e}")
    
    print(f"📊 النتيجة: {success_count}/{len(modules)} وحدات تعمل")
    return success_count == len(modules)

def main():
    """الاختبار الرئيسي"""
    print("=" * 60)
    print("🧪 اختبار شامل لنظام رافع")
    print("=" * 60)
    
    # اختبار قاعدة البيانات
    db_ok = test_database_connection()
    
    # اختبار الوحدات
    modules_ok = test_all_modules()
    
    # النتيجة النهائية
    if db_ok and modules_ok:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
    else:
        print("\n⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه.")

if __name__ == "__main__":
    main()
