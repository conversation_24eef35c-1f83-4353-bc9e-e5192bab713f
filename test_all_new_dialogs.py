#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار جميع النوافذ الجديدة
Test All New Dialogs
"""

import tkinter as tk
from dialogs import (
    UserDialog, ProjectDialog, CustomerDialog, UnitDialog,
    ContractorDialog, SupplierDialog, ExtractDialog, InvoiceDialog,
    PurchaseRequestDialog, MaintenanceTaskDialog, DailyTaskDialog
)

class AllDialogsTester:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("اختبار جميع النوافذ الجديدة")
        self.root.geometry("700x600")
        self.root.configure(bg='#ecf0f1')
        
        self.create_widgets()
        
    def create_widgets(self):
        """إنشاء واجهة الاختبار"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="اختبار جميع النوافذ الجديدة",
            font=('Arial', 20, 'bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # وصف
        desc_label = tk.Label(
            self.root,
            text="جميع النوافذ مع أزرار واضحة ومرئية",
            font=('Arial', 14),
            bg='#ecf0f1',
            fg='#7f8c8d'
        )
        desc_label.pack(pady=10)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.root, bg='#ecf0f1')
        buttons_frame.pack(expand=True, fill='both', padx=20, pady=20)
        
        # قائمة النوافذ
        dialogs = [
            ("👤 المستخدمين", self.test_user_dialog, '#3498db'),
            ("🏗️ المشاريع", self.test_project_dialog, '#e67e22'),
            ("👥 العملاء", self.test_customer_dialog, '#9b59b6'),
            ("🏠 الوحدات", self.test_unit_dialog, '#1abc9c'),
            ("👷 المقاولين", self.test_contractor_dialog, '#f39c12'),
            ("📦 الموردين", self.test_supplier_dialog, '#e74c3c'),
            ("📋 المستخلصات", self.test_extract_dialog, '#34495e'),
            ("🧾 الفواتير", self.test_invoice_dialog, '#16a085'),
            ("🛒 المشتريات", self.test_purchase_dialog, '#8e44ad'),
            ("🔧 الصيانة", self.test_maintenance_dialog, '#d35400'),
            ("📅 المهام اليومية", self.test_daily_task_dialog, '#27ae60'),
        ]
        
        # إنشاء أزرار الاختبار
        row = 0
        col = 0
        for name, command, color in dialogs:
            btn = tk.Button(
                buttons_frame,
                text=f"اختبار {name}",
                font=('Arial', 12, 'bold'),
                bg=color,
                fg='white',
                width=25,
                height=2,
                command=command
            )
            btn.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
            
            col += 1
            if col > 2:  # ثلاثة أعمدة
                col = 0
                row += 1
        
        # تكوين الأعمدة
        for i in range(3):
            buttons_frame.columnconfigure(i, weight=1)
        
        # زر اختبار جميع النوافذ
        test_all_btn = tk.Button(
            self.root,
            text="🧪 اختبار جميع النوافذ",
            font=('Arial', 16, 'bold'),
            bg='#2c3e50',
            fg='white',
            width=30,
            height=2,
            command=self.test_all_dialogs
        )
        test_all_btn.pack(pady=20)
        
    def test_user_dialog(self):
        """اختبار نافذة المستخدمين"""
        print("🧪 اختبار نافذة المستخدمين...")
        dialog = UserDialog(self.root, "إضافة مستخدم جديد")
        self.root.wait_window(dialog.dialog)
        self.show_result("المستخدمين", dialog.result)
        
    def test_project_dialog(self):
        """اختبار نافذة المشاريع"""
        print("🧪 اختبار نافذة المشاريع...")
        dialog = ProjectDialog(self.root, "إضافة مشروع جديد")
        self.root.wait_window(dialog.dialog)
        self.show_result("المشاريع", dialog.result)
        
    def test_customer_dialog(self):
        """اختبار نافذة العملاء"""
        print("🧪 اختبار نافذة العملاء...")
        dialog = CustomerDialog(self.root, "إضافة عميل جديد")
        self.root.wait_window(dialog.dialog)
        self.show_result("العملاء", dialog.result)
        
    def test_unit_dialog(self):
        """اختبار نافذة الوحدات"""
        print("🧪 اختبار نافذة الوحدات...")
        dialog = UnitDialog(self.root, "إضافة وحدة جديدة")
        self.root.wait_window(dialog.dialog)
        self.show_result("الوحدات", dialog.result)
        
    def test_contractor_dialog(self):
        """اختبار نافذة المقاولين"""
        print("🧪 اختبار نافذة المقاولين...")
        dialog = ContractorDialog(self.root, "إضافة مقاول جديد")
        self.root.wait_window(dialog.dialog)
        self.show_result("المقاولين", dialog.result)
        
    def test_supplier_dialog(self):
        """اختبار نافذة الموردين"""
        print("🧪 اختبار نافذة الموردين...")
        dialog = SupplierDialog(self.root, "إضافة مورد جديد")
        self.root.wait_window(dialog.dialog)
        self.show_result("الموردين", dialog.result)
        
    def test_extract_dialog(self):
        """اختبار نافذة المستخلصات"""
        print("🧪 اختبار نافذة المستخلصات...")
        dialog = ExtractDialog(self.root, "إضافة مستخلص جديد")
        self.root.wait_window(dialog.dialog)
        self.show_result("المستخلصات", dialog.result)
        
    def test_invoice_dialog(self):
        """اختبار نافذة الفواتير"""
        print("🧪 اختبار نافذة الفواتير...")
        dialog = InvoiceDialog(self.root, "إضافة فاتورة جديدة")
        self.root.wait_window(dialog.dialog)
        self.show_result("الفواتير", dialog.result)
        
    def test_purchase_dialog(self):
        """اختبار نافذة المشتريات"""
        print("🧪 اختبار نافذة المشتريات...")
        dialog = PurchaseRequestDialog(self.root, "إضافة طلب شراء جديد")
        # النوافذ المبسطة لا تحتاج wait_window
        self.show_result("المشتريات", dialog.result)
        
    def test_maintenance_dialog(self):
        """اختبار نافذة الصيانة"""
        print("🧪 اختبار نافذة الصيانة...")
        dialog = MaintenanceTaskDialog(self.root, "إضافة مهمة صيانة جديدة")
        # النوافذ المبسطة لا تحتاج wait_window
        self.show_result("الصيانة", dialog.result)
        
    def test_daily_task_dialog(self):
        """اختبار نافذة المهام اليومية"""
        print("🧪 اختبار نافذة المهام اليومية...")
        dialog = DailyTaskDialog(self.root, "إضافة مهمة يومية جديدة")
        # النوافذ المبسطة لا تحتاج wait_window
        self.show_result("المهام اليومية", dialog.result)
        
    def show_result(self, dialog_name, result):
        """عرض نتيجة الاختبار"""
        if result:
            print(f"✅ نافذة {dialog_name}: تم الحفظ بنجاح")
            print(f"   البيانات: {result}")
        else:
            print(f"❌ نافذة {dialog_name}: تم الإلغاء")
        
    def test_all_dialogs(self):
        """اختبار جميع النوافذ تلقائياً"""
        print("🧪 اختبار جميع النوافذ...")
        
        # اختبار النوافذ المبسطة أولاً
        simple_dialogs = [
            ("المشتريات", lambda: PurchaseRequestDialog(self.root, "اختبار")),
            ("الصيانة", lambda: MaintenanceTaskDialog(self.root, "اختبار")),
            ("المهام اليومية", lambda: DailyTaskDialog(self.root, "اختبار")),
        ]
        
        for name, dialog_func in simple_dialogs:
            try:
                print(f"🔍 اختبار نافذة {name}...")
                dialog = dialog_func()
                print(f"✅ نافذة {name} تعمل بشكل صحيح")
            except Exception as e:
                print(f"❌ خطأ في نافذة {name}: {e}")
        
        print("🎉 انتهى اختبار جميع النوافذ!")
        
    def run(self):
        """تشغيل الاختبار"""
        print("🧪 بدء اختبار جميع النوافذ الجديدة...")
        print("اضغط على أي زر لاختبار النافذة المقابلة")
        self.root.mainloop()

if __name__ == "__main__":
    tester = AllDialogsTester()
    tester.run()
