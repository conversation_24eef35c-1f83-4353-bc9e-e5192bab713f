# -*- coding: utf-8 -*-
"""
ويدجت لوحة التحكم الرئيسية
Dashboard Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QFrame, QPushButton, QProgressBar,
                            QScrollArea, QSizePolicy, QSpacerItem)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPalette, QColor
from PyQt5.QtChart import QChart, QChartView, QPieSeries, QBarSeries, QBarSet
from loguru import logger

from database import get_db_session
from config import PROJECT_STATUSES, UNIT_STATUSES

class StatCard(QFrame):
    """بطاقة إحصائية"""
    
    def __init__(self, title, value, color="#3498db", icon=None):
        super().__init__()
        self.title = title
        self.value = value
        self.color = color
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة البطاقة"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {self.color};
                border-radius: 10px;
                padding: 15px;
            }}
            QFrame:hover {{
                background-color: #f8f9fa;
                border-color: {self.color};
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # العنوان
        title_label = QLabel(self.title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setStyleSheet(f"color: {self.color}; margin: 0;")
        title_label.setAlignment(Qt.AlignCenter)
        
        # القيمة
        value_label = QLabel(str(self.value))
        value_label.setFont(QFont("Arial", 24, QFont.Bold))
        value_label.setStyleSheet("color: #2c3e50; margin: 0;")
        value_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        self.setMinimumHeight(120)
        self.setMaximumHeight(120)
    
    def update_value(self, new_value):
        """تحديث القيمة"""
        self.value = new_value
        # البحث عن label القيمة وتحديثها
        for child in self.children():
            if isinstance(child, QLabel) and child.font().pointSize() == 24:
                child.setText(str(new_value))
                break

class DashboardWidget(QWidget):
    """ويدجت لوحة التحكم"""
    
    def __init__(self, user_data):
        super().__init__()
        self.user_data = user_data
        self.stats_cards = {}
        self.setup_ui()
        self.setup_timer()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(15)
        
        # عنوان لوحة التحكم
        title_label = QLabel("لوحة التحكم الرئيسية")
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(20)
        
        # البطاقات الإحصائية
        self.create_stats_section(scroll_layout)
        
        # الرسوم البيانية
        self.create_charts_section(scroll_layout)
        
        # الأنشطة الأخيرة
        self.create_activities_section(scroll_layout)
        
        scroll_area.setWidget(scroll_widget)
        main_layout.addWidget(scroll_area)
        
        # أزرار الإجراءات السريعة
        self.create_quick_actions(main_layout)
    
    def create_stats_section(self, layout):
        """إنشاء قسم الإحصائيات"""
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.StyledPanel)
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        stats_layout = QGridLayout(stats_frame)
        stats_layout.setSpacing(15)
        
        # إنشاء البطاقات الإحصائية
        self.stats_cards['projects'] = StatCard("إجمالي المشاريع", 0, "#e74c3c")
        self.stats_cards['active_projects'] = StatCard("المشاريع النشطة", 0, "#27ae60")
        self.stats_cards['total_units'] = StatCard("إجمالي الوحدات", 0, "#3498db")
        self.stats_cards['sold_units'] = StatCard("الوحدات المباعة", 0, "#f39c12")
        self.stats_cards['available_units'] = StatCard("الوحدات المتاحة", 0, "#9b59b6")
        self.stats_cards['total_revenue'] = StatCard("إجمالي الإيرادات", "0 ريال", "#1abc9c")
        
        # ترتيب البطاقات في الشبكة
        row, col = 0, 0
        for card in self.stats_cards.values():
            stats_layout.addWidget(card, row, col)
            col += 1
            if col >= 3:
                col = 0
                row += 1
        
        layout.addWidget(stats_frame)
    
    def create_charts_section(self, layout):
        """إنشاء قسم الرسوم البيانية"""
        charts_frame = QFrame()
        charts_frame.setFrameStyle(QFrame.StyledPanel)
        charts_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        charts_layout = QHBoxLayout(charts_frame)
        charts_layout.setSpacing(20)
        
        # رسم بياني دائري لحالة المشاريع
        self.create_projects_pie_chart(charts_layout)
        
        # رسم بياني عمودي للوحدات
        self.create_units_bar_chart(charts_layout)
        
        layout.addWidget(charts_frame)
    
    def create_projects_pie_chart(self, layout):
        """إنشاء رسم بياني دائري للمشاريع"""
        chart_frame = QFrame()
        chart_layout = QVBoxLayout(chart_frame)
        
        title = QLabel("توزيع المشاريع حسب الحالة")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        chart_layout.addWidget(title)
        
        # إنشاء الرسم البياني
        self.projects_chart = QChart()
        self.projects_chart.setTitle("")
        self.projects_chart.setAnimationOptions(QChart.SeriesAnimations)
        
        chart_view = QChartView(self.projects_chart)
        chart_view.setRenderHint(chart_view.Antialiasing)
        chart_view.setMinimumHeight(250)
        
        chart_layout.addWidget(chart_view)
        layout.addWidget(chart_frame)
    
    def create_units_bar_chart(self, layout):
        """إنشاء رسم بياني عمودي للوحدات"""
        chart_frame = QFrame()
        chart_layout = QVBoxLayout(chart_frame)
        
        title = QLabel("توزيع الوحدات حسب الحالة")
        title.setFont(QFont("Arial", 12, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        chart_layout.addWidget(title)
        
        # إنشاء الرسم البياني
        self.units_chart = QChart()
        self.units_chart.setTitle("")
        self.units_chart.setAnimationOptions(QChart.SeriesAnimations)
        
        chart_view = QChartView(self.units_chart)
        chart_view.setRenderHint(chart_view.Antialiasing)
        chart_view.setMinimumHeight(250)
        
        chart_layout.addWidget(chart_view)
        layout.addWidget(chart_frame)
    
    def create_activities_section(self, layout):
        """إنشاء قسم الأنشطة الأخيرة"""
        activities_frame = QFrame()
        activities_frame.setFrameStyle(QFrame.StyledPanel)
        activities_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        activities_layout = QVBoxLayout(activities_frame)
        
        title = QLabel("الأنشطة الأخيرة")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        activities_layout.addWidget(title)
        
        # قائمة الأنشطة
        self.activities_list = QVBoxLayout()
        activities_layout.addLayout(self.activities_list)
        
        layout.addWidget(activities_frame)
    
    def create_quick_actions(self, layout):
        """إنشاء أزرار الإجراءات السريعة"""
        actions_frame = QFrame()
        actions_frame.setMaximumHeight(80)
        actions_frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        
        actions_layout = QHBoxLayout(actions_frame)
        actions_layout.setSpacing(15)
        
        # أزرار الإجراءات
        actions = [
            ("مشروع جديد", self.new_project),
            ("عميل جديد", self.new_customer),
            ("عقد جديد", self.new_contract),
            ("تقرير سريع", self.quick_report),
            ("تحديث البيانات", self.refresh_data)
        ]
        
        for text, callback in actions:
            btn = QPushButton(text)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            btn.clicked.connect(callback)
            actions_layout.addWidget(btn)
        
        layout.addWidget(actions_frame)
    
    def setup_timer(self):
        """إعداد مؤقت التحديث التلقائي"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_data)
        self.refresh_timer.start(300000)  # تحديث كل 5 دقائق
    
    def load_data(self):
        """تحميل البيانات"""
        try:
            self.load_statistics()
            self.load_charts_data()
            self.load_recent_activities()
            logger.info("تم تحديث بيانات لوحة التحكم")
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات لوحة التحكم: {e}")
    
    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            with get_db_session() as session:
                # إحصائيات المشاريع
                projects_result = session.execute("SELECT COUNT(*) FROM projects")
                total_projects = projects_result.fetchone()[0]
                
                active_projects_result = session.execute(
                    "SELECT COUNT(*) FROM projects WHERE status = 'in_progress'"
                )
                active_projects = active_projects_result.fetchone()[0]
                
                # إحصائيات الوحدات
                units_result = session.execute("SELECT COUNT(*) FROM units")
                total_units = units_result.fetchone()[0]
                
                sold_units_result = session.execute(
                    "SELECT COUNT(*) FROM units WHERE status = 'sold'"
                )
                sold_units = sold_units_result.fetchone()[0]
                
                available_units_result = session.execute(
                    "SELECT COUNT(*) FROM units WHERE status = 'available'"
                )
                available_units = available_units_result.fetchone()[0]
                
                # إجمالي الإيرادات
                revenue_result = session.execute(
                    "SELECT COALESCE(SUM(paid_amount), 0) FROM contracts"
                )
                total_revenue = revenue_result.fetchone()[0]
                
                # تحديث البطاقات
                self.stats_cards['projects'].update_value(total_projects)
                self.stats_cards['active_projects'].update_value(active_projects)
                self.stats_cards['total_units'].update_value(total_units)
                self.stats_cards['sold_units'].update_value(sold_units)
                self.stats_cards['available_units'].update_value(available_units)
                self.stats_cards['total_revenue'].update_value(f"{total_revenue:,.0f} ريال")
                
        except Exception as e:
            logger.error(f"خطأ في تحميل الإحصائيات: {e}")
    
    def load_charts_data(self):
        """تحميل بيانات الرسوم البيانية"""
        # سيتم تنفيذها لاحقاً
        pass
    
    def load_recent_activities(self):
        """تحميل الأنشطة الأخيرة"""
        # سيتم تنفيذها لاحقاً
        pass
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()
    
    # معالجات الإجراءات السريعة
    def new_project(self):
        """مشروع جديد"""
        # سيتم تنفيذها لاحقاً
        pass
    
    def new_customer(self):
        """عميل جديد"""
        # سيتم تنفيذها لاحقاً
        pass
    
    def new_contract(self):
        """عقد جديد"""
        # سيتم تنفيذها لاحقاً
        pass
    
    def quick_report(self):
        """تقرير سريع"""
        # سيتم تنفيذها لاحقاً
        pass
