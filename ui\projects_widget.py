# -*- coding: utf-8 -*-
"""
ويدجت إدارة المشاريع
Projects Management Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                            QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                            QComboBox, QDateEdit, QTextEdit, QFormLayout,
                            QDialog, QDialogButtonBox, QMessageBox, QHeaderView,
                            QFrame, QSpinBox, QDoubleSpinBox)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont
from loguru import logger

from database import get_db_session
from config import PROJECT_STATUSES

class ProjectDialog(QDialog):
    """نافذة إضافة/تعديل مشروع"""
    
    def __init__(self, project_data=None, parent=None):
        super().__init__(parent)
        self.project_data = project_data
        self.setup_ui()
        if project_data:
            self.load_project_data()
    
    def setup_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle("إضافة مشروع جديد" if not self.project_data else "تعديل المشروع")
        self.setModal(True)
        self.resize(500, 600)
        
        layout = QVBoxLayout(self)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # اسم المشروع
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم المشروع")
        form_layout.addRow("اسم المشروع:", self.name_edit)
        
        # الموقع
        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("أدخل موقع المشروع")
        form_layout.addRow("الموقع:", self.location_edit)
        
        # نوع المشروع
        self.type_combo = QComboBox()
        self.type_combo.addItems(["سكني", "تجاري", "إداري", "مختلط", "صناعي"])
        form_layout.addRow("نوع المشروع:", self.type_combo)
        
        # التكلفة الإجمالية
        self.cost_spinbox = QDoubleSpinBox()
        self.cost_spinbox.setRange(0, 999999999)
        self.cost_spinbox.setSuffix(" ريال")
        form_layout.addRow("التكلفة الإجمالية:", self.cost_spinbox)
        
        # نسبة الإنجاز
        self.completion_spinbox = QDoubleSpinBox()
        self.completion_spinbox.setRange(0, 100)
        self.completion_spinbox.setSuffix("%")
        form_layout.addRow("نسبة الإنجاز:", self.completion_spinbox)
        
        # تاريخ البداية
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate())
        self.start_date.setCalendarPopup(True)
        form_layout.addRow("تاريخ البداية:", self.start_date)
        
        # تاريخ الانتهاء المتوقع
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate().addYears(1))
        self.end_date.setCalendarPopup(True)
        form_layout.addRow("تاريخ الانتهاء المتوقع:", self.end_date)
        
        # الحالة
        self.status_combo = QComboBox()
        for key, value in PROJECT_STATUSES.items():
            self.status_combo.addItem(value, key)
        form_layout.addRow("الحالة:", self.status_combo)
        
        # الوصف
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("أدخل وصف المشروع")
        form_layout.addRow("الوصف:", self.description_edit)
        
        layout.addLayout(form_layout)
        
        # أزرار التحكم
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
    
    def load_project_data(self):
        """تحميل بيانات المشروع للتعديل"""
        if self.project_data:
            self.name_edit.setText(self.project_data.get('name', ''))
            self.location_edit.setText(self.project_data.get('location', ''))
            self.type_combo.setCurrentText(self.project_data.get('project_type', ''))
            self.cost_spinbox.setValue(self.project_data.get('total_cost', 0))
            self.completion_spinbox.setValue(self.project_data.get('completion_percentage', 0))
            
            if self.project_data.get('start_date'):
                self.start_date.setDate(QDate.fromString(str(self.project_data['start_date']), Qt.ISODate))
            
            if self.project_data.get('expected_end_date'):
                self.end_date.setDate(QDate.fromString(str(self.project_data['expected_end_date']), Qt.ISODate))
            
            # تعيين الحالة
            status = self.project_data.get('status', 'planning')
            index = self.status_combo.findData(status)
            if index >= 0:
                self.status_combo.setCurrentIndex(index)
            
            self.description_edit.setPlainText(self.project_data.get('description', ''))
    
    def get_project_data(self):
        """الحصول على بيانات المشروع"""
        return {
            'name': self.name_edit.text().strip(),
            'location': self.location_edit.text().strip(),
            'project_type': self.type_combo.currentText(),
            'total_cost': self.cost_spinbox.value(),
            'completion_percentage': self.completion_spinbox.value(),
            'start_date': self.start_date.date().toString(Qt.ISODate),
            'expected_end_date': self.end_date.date().toString(Qt.ISODate),
            'status': self.status_combo.currentData(),
            'description': self.description_edit.toPlainText().strip()
        }

class ProjectsWidget(QWidget):
    """ويدجت إدارة المشاريع"""
    
    def __init__(self, user_data):
        super().__init__()
        self.user_data = user_data
        self.projects_data = []
        self.setup_ui()
        self.load_projects()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # عنوان الصفحة
        title_label = QLabel("إدارة المشاريع العقارية")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # شريط الأدوات
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # زر إضافة مشروع جديد
        self.add_button = QPushButton("إضافة مشروع جديد")
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.add_button.clicked.connect(self.add_project)
        toolbar_layout.addWidget(self.add_button)
        
        # زر تعديل
        self.edit_button = QPushButton("تعديل")
        self.edit_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.edit_button.clicked.connect(self.edit_project)
        self.edit_button.setEnabled(False)
        toolbar_layout.addWidget(self.edit_button)
        
        # زر حذف
        self.delete_button = QPushButton("حذف")
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.delete_button.clicked.connect(self.delete_project)
        self.delete_button.setEnabled(False)
        toolbar_layout.addWidget(self.delete_button)
        
        toolbar_layout.addStretch()
        
        # حقل البحث
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في المشاريع...")
        self.search_edit.textChanged.connect(self.filter_projects)
        toolbar_layout.addWidget(self.search_edit)
        
        # زر تحديث
        self.refresh_button = QPushButton("تحديث")
        self.refresh_button.clicked.connect(self.load_projects)
        toolbar_layout.addWidget(self.refresh_button)
        
        layout.addWidget(toolbar_frame)
        
        # جدول المشاريع
        self.projects_table = QTableWidget()
        self.projects_table.setColumnCount(8)
        self.projects_table.setHorizontalHeaderLabels([
            "الرقم", "اسم المشروع", "الموقع", "النوع", "التكلفة", 
            "نسبة الإنجاز", "الحالة", "تاريخ البداية"
        ])
        
        # تنسيق الجدول
        header = self.projects_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.projects_table.setAlternatingRowColors(True)
        self.projects_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.projects_table.setSelectionMode(QTableWidget.SingleSelection)
        
        # ربط أحداث الجدول
        self.projects_table.selectionModel().selectionChanged.connect(self.on_selection_changed)
        self.projects_table.doubleClicked.connect(self.edit_project)
        
        layout.addWidget(self.projects_table)
    
    def load_projects(self):
        """تحميل المشاريع من قاعدة البيانات"""
        try:
            with get_db_session() as session:
                result = session.execute("""
                    SELECT id, name, location, project_type, total_cost, 
                           completion_percentage, status, start_date, 
                           expected_end_date, description
                    FROM projects 
                    ORDER BY created_at DESC
                """)
                
                self.projects_data = []
                for row in result.fetchall():
                    self.projects_data.append({
                        'id': row[0],
                        'name': row[1],
                        'location': row[2],
                        'project_type': row[3],
                        'total_cost': row[4],
                        'completion_percentage': row[5],
                        'status': row[6],
                        'start_date': row[7],
                        'expected_end_date': row[8],
                        'description': row[9]
                    })
                
                self.populate_table()
                logger.info(f"تم تحميل {len(self.projects_data)} مشروع")
                
        except Exception as e:
            logger.error(f"خطأ في تحميل المشاريع: {e}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المشاريع:\n{str(e)}")
    
    def populate_table(self):
        """ملء الجدول بالبيانات"""
        self.projects_table.setRowCount(len(self.projects_data))
        
        for row, project in enumerate(self.projects_data):
            self.projects_table.setItem(row, 0, QTableWidgetItem(str(project['id'])))
            self.projects_table.setItem(row, 1, QTableWidgetItem(project['name'] or ''))
            self.projects_table.setItem(row, 2, QTableWidgetItem(project['location'] or ''))
            self.projects_table.setItem(row, 3, QTableWidgetItem(project['project_type'] or ''))
            self.projects_table.setItem(row, 4, QTableWidgetItem(f"{project['total_cost']:,.0f} ريال" if project['total_cost'] else '0 ريال'))
            self.projects_table.setItem(row, 5, QTableWidgetItem(f"{project['completion_percentage']:.1f}%" if project['completion_percentage'] else '0%'))
            self.projects_table.setItem(row, 6, QTableWidgetItem(PROJECT_STATUSES.get(project['status'], project['status'])))
            self.projects_table.setItem(row, 7, QTableWidgetItem(str(project['start_date']) if project['start_date'] else ''))
    
    def filter_projects(self):
        """تصفية المشاريع حسب النص المدخل"""
        search_text = self.search_edit.text().lower()
        
        for row in range(self.projects_table.rowCount()):
            show_row = False
            for col in range(self.projects_table.columnCount()):
                item = self.projects_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break
            
            self.projects_table.setRowHidden(row, not show_row)
    
    def on_selection_changed(self):
        """معالج تغيير التحديد"""
        has_selection = bool(self.projects_table.selectedItems())
        self.edit_button.setEnabled(has_selection)
        self.delete_button.setEnabled(has_selection)
    
    def add_project(self):
        """إضافة مشروع جديد"""
        dialog = ProjectDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            project_data = dialog.get_project_data()
            
            # التحقق من صحة البيانات
            if not project_data['name']:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المشروع")
                return
            
            try:
                with get_db_session() as session:
                    session.execute("""
                        INSERT INTO projects (name, location, project_type, total_cost, 
                                            completion_percentage, start_date, expected_end_date, 
                                            status, description, created_by)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        project_data['name'],
                        project_data['location'],
                        project_data['project_type'],
                        project_data['total_cost'],
                        project_data['completion_percentage'],
                        project_data['start_date'],
                        project_data['expected_end_date'],
                        project_data['status'],
                        project_data['description'],
                        self.user_data['id']
                    ))
                    session.commit()
                
                QMessageBox.information(self, "نجح", "تم إضافة المشروع بنجاح")
                self.load_projects()
                logger.info(f"تم إضافة مشروع جديد: {project_data['name']}")
                
            except Exception as e:
                logger.error(f"خطأ في إضافة المشروع: {e}")
                QMessageBox.critical(self, "خطأ", f"فشل في إضافة المشروع:\n{str(e)}")
    
    def edit_project(self):
        """تعديل المشروع المحدد"""
        current_row = self.projects_table.currentRow()
        if current_row < 0:
            return
        
        project_id = int(self.projects_table.item(current_row, 0).text())
        project_data = next((p for p in self.projects_data if p['id'] == project_id), None)
        
        if not project_data:
            return
        
        dialog = ProjectDialog(project_data, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            updated_data = dialog.get_project_data()
            
            try:
                with get_db_session() as session:
                    session.execute("""
                        UPDATE projects 
                        SET name=%s, location=%s, project_type=%s, total_cost=%s,
                            completion_percentage=%s, start_date=%s, expected_end_date=%s,
                            status=%s, description=%s, updated_at=CURRENT_TIMESTAMP
                        WHERE id=%s
                    """, (
                        updated_data['name'],
                        updated_data['location'],
                        updated_data['project_type'],
                        updated_data['total_cost'],
                        updated_data['completion_percentage'],
                        updated_data['start_date'],
                        updated_data['expected_end_date'],
                        updated_data['status'],
                        updated_data['description'],
                        project_id
                    ))
                    session.commit()
                
                QMessageBox.information(self, "نجح", "تم تحديث المشروع بنجاح")
                self.load_projects()
                logger.info(f"تم تحديث المشروع: {updated_data['name']}")
                
            except Exception as e:
                logger.error(f"خطأ في تحديث المشروع: {e}")
                QMessageBox.critical(self, "خطأ", f"فشل في تحديث المشروع:\n{str(e)}")
    
    def delete_project(self):
        """حذف المشروع المحدد"""
        current_row = self.projects_table.currentRow()
        if current_row < 0:
            return
        
        project_name = self.projects_table.item(current_row, 1).text()
        project_id = int(self.projects_table.item(current_row, 0).text())
        
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المشروع '{project_name}'؟\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                with get_db_session() as session:
                    session.execute("DELETE FROM projects WHERE id = %s", (project_id,))
                    session.commit()
                
                QMessageBox.information(self, "نجح", "تم حذف المشروع بنجاح")
                self.load_projects()
                logger.info(f"تم حذف المشروع: {project_name}")
                
            except Exception as e:
                logger.error(f"خطأ في حذف المشروع: {e}")
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المشروع:\n{str(e)}")
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.load_projects()
