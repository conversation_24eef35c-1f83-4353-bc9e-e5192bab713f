#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشكلة عدم ظهور البيانات في الواجهة
Fix Data Display Issue in Interface
"""

import sqlite3
import os
from datetime import datetime

def check_database_data():
    """فحص البيانات في قاعدة البيانات"""
    print("🔍 فحص البيانات في قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('data/rafea_system.db')
        cursor = conn.cursor()
        
        # فحص الجداول والبيانات
        tables_to_check = [
            'users', 'projects', 'customers', 'units', 'contractors',
            'suppliers', 'extracts', 'invoices', 'purchase_requests',
            'maintenance_tasks', 'daily_tasks', 'contracts', 'payments'
        ]
        
        data_counts = {}
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                data_counts[table] = count
                print(f"   📊 {table}: {count} سجل")
            except Exception as e:
                print(f"   ❌ خطأ في جدول {table}: {e}")
                data_counts[table] = 0
        
        conn.close()
        return data_counts
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return {}

def add_sample_data():
    """إضافة بيانات تجريبية للاختبار"""
    print("🔧 إضافة بيانات تجريبية...")
    
    try:
        conn = sqlite3.connect('data/rafea_system.db')
        cursor = conn.cursor()
        
        # إضافة مشاريع تجريبية
        print("   📋 إضافة مشاريع تجريبية...")
        projects_data = [
            ('مشروع الواحة السكني', 'الرياض', 'residential', 5000000, 25, '2024-01-01', '2025-12-31', 'in_progress', 'مشروع سكني متكامل'),
            ('مشروع النخيل التجاري', 'جدة', 'commercial', 8000000, 40, '2024-02-01', '2026-01-31', 'in_progress', 'مشروع تجاري حديث'),
            ('مشروع الياسمين', 'الدمام', 'residential', 3000000, 60, '2023-06-01', '2024-12-31', 'in_progress', 'مشروع سكني اقتصادي')
        ]
        
        for project in projects_data:
            cursor.execute("""
                INSERT OR IGNORE INTO projects (name, location, project_type, total_cost, 
                                              completion_percentage, start_date, expected_end_date, 
                                              status, description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, project)
        
        # إضافة عملاء تجريبيين
        print("   👥 إضافة عملاء تجريبيين...")
        customers_data = [
            ('أحمد محمد السعيد', '1234567890', '0501234567', '<EMAIL>', 'individual'),
            ('شركة البناء المتطور', '9876543210', '0507654321', '<EMAIL>', 'company'),
            ('فاطمة علي الأحمد', '5555555555', '0509876543', '<EMAIL>', 'individual')
        ]
        
        for customer in customers_data:
            cursor.execute("""
                INSERT OR IGNORE INTO customers (full_name, national_id, phone, email, customer_type)
                VALUES (?, ?, ?, ?, ?)
            """, customer)
        
        # إضافة وحدات تجريبية
        print("   🏠 إضافة وحدات تجريبية...")
        units_data = [
            ('A101', 1, 1, 'apartment', 120.5, 450000, 'available'),
            ('A102', 1, 1, 'apartment', 135.0, 520000, 'available'),
            ('B201', 1, 2, 'apartment', 150.0, 600000, 'reserved'),
            ('C301', 2, 3, 'office', 80.0, 300000, 'available'),
            ('D401', 3, 4, 'apartment', 100.0, 380000, 'sold')
        ]
        
        for unit in units_data:
            cursor.execute("""
                INSERT OR IGNORE INTO units (unit_number, project_id, floor_number, 
                                           unit_type, area, price, status)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, unit)
        
        # إضافة مقاولين تجريبيين
        print("   👷 إضافة مقاولين تجريبيين...")
        contractors_data = [
            ('شركة البناء الحديث', 'أعمال البناء', '0501111111', '<EMAIL>', 'active'),
            ('مؤسسة التشييد المتقدم', 'أعمال الكهرباء', '0502222222', '<EMAIL>', 'active'),
            ('شركة التطوير العقاري', 'أعمال السباكة', '0503333333', '<EMAIL>', 'active')
        ]
        
        for contractor in contractors_data:
            cursor.execute("""
                INSERT OR IGNORE INTO contractors (name, specialty, phone, email, status)
                VALUES (?, ?, ?, ?, ?)
            """, contractor)
        
        # إضافة موردين تجريبيين
        print("   📦 إضافة موردين تجريبيين...")
        suppliers_data = [
            ('شركة مواد البناء', 'مواد البناء', '0504444444', '<EMAIL>', 'active'),
            ('مؤسسة الحديد والصلب', 'حديد وصلب', '0505555555', '<EMAIL>', 'active'),
            ('شركة الأدوات الصحية', 'أدوات صحية', '0506666666', '<EMAIL>', 'active')
        ]
        
        for supplier in suppliers_data:
            cursor.execute("""
                INSERT OR IGNORE INTO suppliers (name, material_type, phone, email, status)
                VALUES (?, ?, ?, ?, ?)
            """, supplier)
        
        # إضافة مستخلصات تجريبية
        print("   📄 إضافة مستخلصات تجريبية...")
        extracts_data = [
            ('EXT-001', 'شركة البناء الحديث', 1, 150000, '2024-01-15', 'pending'),
            ('EXT-002', 'مؤسسة التشييد المتقدم', 2, 200000, '2024-02-01', 'approved'),
            ('EXT-003', 'شركة التطوير العقاري', 1, 180000, '2024-02-15', 'paid')
        ]
        
        for extract in extracts_data:
            cursor.execute("""
                INSERT OR IGNORE INTO extracts (extract_number, contractor, project, 
                                              total_amount, extract_date, status)
                VALUES (?, ?, ?, ?, ?, ?)
            """, extract)
        
        # إضافة فواتير تجريبية
        print("   🧾 إضافة فواتير تجريبية...")
        invoices_data = [
            ('INV-001', 'شركة مواد البناء', 75000, '2024-01-10', '2024-02-10', 'pending'),
            ('INV-002', 'مؤسسة الحديد والصلب', 120000, '2024-01-20', '2024-02-20', 'paid'),
            ('INV-003', 'شركة الأدوات الصحية', 45000, '2024-02-01', '2024-03-01', 'approved')
        ]
        
        for invoice in invoices_data:
            cursor.execute("""
                INSERT OR IGNORE INTO invoices (invoice_number, supplier, total_amount, 
                                              invoice_date, due_date, status)
                VALUES (?, ?, ?, ?, ?, ?)
            """, invoice)
        
        # إضافة طلبات شراء تجريبية
        print("   🛒 إضافة طلبات شراء تجريبية...")
        purchases_data = [
            ('PUR-001', 1, 'أسمنت وحديد', 100, 50000, '2024-01-05', 'approved'),
            ('PUR-002', 2, 'أدوات كهربائية', 50, 25000, '2024-01-15', 'pending'),
            ('PUR-003', 1, 'أدوات صحية', 30, 15000, '2024-02-01', 'completed')
        ]
        
        for purchase in purchases_data:
            cursor.execute("""
                INSERT OR IGNORE INTO purchase_requests (request_number, project, materials, 
                                                       quantity, estimated_cost, request_date, status)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, purchase)
        
        # إضافة مهام صيانة تجريبية
        print("   🔧 إضافة مهام صيانة تجريبية...")
        maintenance_data = [
            ('MAIN-001', 'إصلاح المصعد', 'صيانة', 'المبنى الأول', 'فريق الصيانة', '2024-01-10', 0, 'pending'),
            ('MAIN-002', 'فحص التكييف', 'فحص', 'المبنى الثاني', 'فني التكييف', '2024-01-15', 0, 'in_progress'),
            ('MAIN-003', 'تنظيف الخزانات', 'تنظيف', 'جميع المباني', 'شركة التنظيف', '2024-02-01', 0, 'completed')
        ]
        
        for maintenance in maintenance_data:
            cursor.execute("""
                INSERT OR IGNORE INTO maintenance_tasks (task_number, task_title, maintenance_type, 
                                                       location, technician, date, cost, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, maintenance)
        
        # إضافة مهام يومية تجريبية
        print("   📋 إضافة مهام يومية تجريبية...")
        daily_tasks_data = [
            ('مراجعة التقارير اليومية', 'مراجعة جميع التقارير', 'المدير', 'high', '2024-01-20', 'pending'),
            ('متابعة المشاريع', 'متابعة تقدم المشاريع', 'مدير المشاريع', 'medium', '2024-01-21', 'in_progress'),
            ('اجتماع الفريق', 'اجتماع أسبوعي مع الفريق', 'جميع الموظفين', 'medium', '2024-01-22', 'completed')
        ]
        
        for task in daily_tasks_data:
            cursor.execute("""
                INSERT OR IGNORE INTO daily_tasks (task_title, description, assigned_to, 
                                                 priority, due_date, status)
                VALUES (?, ?, ?, ?, ?, ?)
            """, task)
        
        conn.commit()
        conn.close()
        
        print("✅ تم إضافة البيانات التجريبية بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات التجريبية: {e}")
        return False

def fix_data_loading_functions():
    """إصلاح دوال تحميل البيانات"""
    print("🔧 إصلاح دوال تحميل البيانات...")
    
    try:
        # قراءة الملف الرئيسي
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة استدعاء تحديث البيانات بعد كل عملية إضافة/تعديل/حذف
        fixes = [
            # إصلاح دالة تحميل البيانات الأولية
            ('self.load_users_data()', '''self.load_users_data()
            
            # تحديث البيانات في جميع التبويبات
            self.refresh_all_data()'''),
            
            # إضافة دالة تحديث شاملة
            ('def logout(self):', '''def refresh_all_data(self):
        """تحديث جميع البيانات في النظام"""
        try:
            # تحديث الإحصائيات
            self.update_dashboard_stats()
            
            # تحديث جميع الجداول
            if hasattr(self, 'projects_tree'):
                self.load_projects_data()
            if hasattr(self, 'customers_tree'):
                self.load_customers_data()
            if hasattr(self, 'units_tree'):
                self.load_units_data()
            if hasattr(self, 'contractors_tree'):
                self.load_contractors_data()
            if hasattr(self, 'suppliers_tree'):
                self.load_suppliers_data()
            if hasattr(self, 'extracts_tree'):
                self.load_extracts_data()
            if hasattr(self, 'invoices_tree'):
                self.load_invoices_data()
            if hasattr(self, 'purchase_requests_tree'):
                self.load_purchase_requests_data()
            if hasattr(self, 'maintenance_tasks_tree'):
                self.load_maintenance_tasks_data()
            if hasattr(self, 'daily_tasks_tree'):
                self.load_daily_tasks_data()
            if hasattr(self, 'users_tree'):
                self.load_users_data()
            if hasattr(self, 'contracts_tree'):
                self.load_contracts_data()
            if hasattr(self, 'payments_tree'):
                self.load_payments_data()
                
            self.update_status("تم تحديث جميع البيانات")
            
        except Exception as e:
            logger.error(f"خطأ في تحديث البيانات: {e}")
            self.update_status(f"خطأ في تحديث البيانات: {str(e)}")
    
    def logout(self):''')
        ]
        
        for old_text, new_text in fixes:
            if old_text in content and new_text not in content:
                content = content.replace(old_text, new_text)
        
        # كتابة الملف المحدث
        with open('rafea_complete_system.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح دوال تحميل البيانات!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح دوال تحميل البيانات: {e}")
        return False

def test_data_display():
    """اختبار عرض البيانات"""
    print("🧪 اختبار عرض البيانات...")
    
    try:
        # اختبار الاتصال بقاعدة البيانات
        from database_sqlite import SQLiteManager
        sqlite_manager = SQLiteManager()
        
        # اختبار استعلامات البيانات
        test_queries = [
            ("المشاريع", "SELECT COUNT(*) FROM projects"),
            ("العملاء", "SELECT COUNT(*) FROM customers"),
            ("الوحدات", "SELECT COUNT(*) FROM units"),
            ("المقاولين", "SELECT COUNT(*) FROM contractors"),
            ("الموردين", "SELECT COUNT(*) FROM suppliers")
        ]
        
        for name, query in test_queries:
            try:
                result = sqlite_manager.execute_query(query)
                count = result[0][0] if result else 0
                print(f"   ✅ {name}: {count} سجل")
            except Exception as e:
                print(f"   ❌ خطأ في {name}: {e}")
        
        print("🎉 اختبار عرض البيانات مكتمل!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عرض البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🔧 إصلاح مشكلة عدم ظهور البيانات في الواجهة")
    print("=" * 70)
    
    # فحص البيانات الحالية
    data_counts = check_database_data()
    
    # إضافة بيانات تجريبية إذا كانت قاعدة البيانات فارغة
    total_records = sum(data_counts.values())
    if total_records < 10:
        print(f"\n⚠️ قاعدة البيانات تحتوي على {total_records} سجل فقط")
        print("🔧 سيتم إضافة بيانات تجريبية...")
        add_sample_data()
    else:
        print(f"\n✅ قاعدة البيانات تحتوي على {total_records} سجل")
    
    # إصلاح دوال تحميل البيانات
    print("\n🔧 إصلاح دوال تحميل البيانات...")
    fix_data_loading_functions()
    
    # اختبار عرض البيانات
    print("\n🧪 اختبار عرض البيانات...")
    test_data_display()
    
    print("\n" + "=" * 70)
    print("🎉 تم إصلاح مشكلة عدم ظهور البيانات!")
    print("=" * 70)
    print("📋 الآن يمكنك:")
    print("1. تشغيل النظام: python rafea_complete_system.py")
    print("2. تسجيل الدخول: admin / admin123")
    print("3. ستجد البيانات التجريبية في جميع الصفحات")
    print("4. يمكنك إضافة بيانات جديدة وستظهر فوراً")

if __name__ == "__main__":
    main()
