#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة شركة رافع للتطوير العقاري - النسخة النهائية الكاملة
Rafea Real Estate Development Management System - Final Complete Version
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import sqlite3
import bcrypt
import logging
from datetime import datetime
import os

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SQLiteManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self, db_path='data/rafea_system.db'):
        self.db_path = db_path
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            conn.execute("PRAGMA foreign_keys = OFF")
            cursor = conn.execute(query, params or ())
            if query.strip().upper().startswith('SELECT'):
                result = cursor.fetchall()
                conn.close()
                return result
            else:
                conn.commit()
                rowcount = cursor.rowcount
                conn.close()
                return rowcount
        except Exception as e:
            if conn:
                conn.close()
            logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            raise

# إنشاء مثيل مدير قاعدة البيانات
sqlite_manager = SQLiteManager()

class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.user_data = None
        self.setup_window()
        self.create_widgets()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.root.title("تسجيل الدخول - نظام إدارة شركة رافع")
        self.root.geometry("450x350")
        self.root.configure(bg='#2c3e50')
        self.root.resizable(False, False)
        
        # توسيط النافذة
        self.root.eval('tk::PlaceWindow . center')
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg='#2c3e50')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = tk.Label(main_frame, text="نظام إدارة شركة رافع للتطوير العقاري", 
                              font=('Arial', 16, 'bold'), bg='#2c3e50', fg='white')
        title_label.pack(pady=(0, 30))
        
        # إطار تسجيل الدخول
        login_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=2)
        login_frame.pack(pady=20, fill='both', expand=True)
        
        # عنوان تسجيل الدخول
        login_title = tk.Label(login_frame, text="تسجيل الدخول", 
                              font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        login_title.pack(pady=20)
        
        # اسم المستخدم
        tk.Label(login_frame, text="اسم المستخدم:", bg='white', 
                font=('Arial', 12)).pack(pady=(10, 5))
        self.username_entry = tk.Entry(login_frame, font=('Arial', 12), width=25)
        self.username_entry.pack(pady=5)
        
        # كلمة المرور
        tk.Label(login_frame, text="كلمة المرور:", bg='white', 
                font=('Arial', 12)).pack(pady=(15, 5))
        self.password_entry = tk.Entry(login_frame, font=('Arial', 12), width=25, show='*')
        self.password_entry.pack(pady=5)
        
        # زر تسجيل الدخول
        login_btn = tk.Button(login_frame, text="تسجيل الدخول", bg='#3498db', fg='white',
                             font=('Arial', 12, 'bold'), command=self.login, width=20)
        login_btn.pack(pady=30)
        
        # معلومات تسجيل الدخول
        info_label = tk.Label(login_frame, text="المستخدم الافتراضي: admin | كلمة المرور: admin123", 
                             bg='white', fg='#7f8c8d', font=('Arial', 9))
        info_label.pack(pady=(0, 20))
        
        # ربط Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda e: self.login())
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        try:
            query = "SELECT id, username, password_hash, full_name, user_type, is_active FROM users WHERE username = ?"
            result = sqlite_manager.execute_query(query, (username,))
            
            if not result:
                messagebox.showerror("خطأ", "اسم المستخدم غير صحيح")
                return
            
            user = result[0]
            
            if not user['is_active']:
                messagebox.showerror("خطأ", "الحساب غير مفعل")
                return
            
            if bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
                self.user_data = {
                    'id': user['id'],
                    'username': user['username'],
                    'full_name': user['full_name'],
                    'user_type': user['user_type']
                }
                
                logger.info(f"تم تسجيل دخول المستخدم: {username}")
                self.root.destroy()
            else:
                messagebox.showerror("خطأ", "كلمة المرور غير صحيحة")
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل الدخول: {e}")
            messagebox.showerror("خطأ", f"فشل في تسجيل الدخول: {str(e)}")
    
    def run(self):
        """تشغيل نافذة تسجيل الدخول"""
        self.root.mainloop()
        return self.user_data

class RafeaCompleteSystem:
    """النظام الكامل لإدارة شركة رافع"""
    
    def __init__(self, user_data):
        self.user_data = user_data
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.load_data()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title(f"نظام إدارة شركة رافع - {self.user_data['full_name']}")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f5f5f5')
        self.root.state('zoomed')  # ملء الشاشة
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # شريط العنوان
        self.create_header()
        
        # التبويبات الرئيسية
        self.create_main_tabs()
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_header(self):
        """إنشاء شريط العنوان"""
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=70)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # العنوان الرئيسي
        title_label = tk.Label(header_frame, text="نظام إدارة شركة رافع للتطوير العقاري",
                              font=('Arial', 20, 'bold'), bg='#2c3e50', fg='white')
        title_label.pack(side='left', padx=20, pady=20)
        
        # معلومات المستخدم
        user_frame = tk.Frame(header_frame, bg='#2c3e50')
        user_frame.pack(side='right', padx=20, pady=20)
        
        user_label = tk.Label(user_frame, text=f"مرحباً، {self.user_data['full_name']}",
                             font=('Arial', 12), bg='#2c3e50', fg='white')
        user_label.pack()
        
        logout_btn = tk.Button(user_frame, text="تسجيل الخروج", bg='#e74c3c', fg='white',
                              font=('Arial', 10), command=self.logout)
        logout_btn.pack(pady=(5, 0))
    
    def create_main_tabs(self):
        """إنشاء التبويبات الرئيسية"""
        # إنشاء التبويبات
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # التبويبات
        self.create_dashboard_tab()
        self.create_projects_tab()
        self.create_customers_tab()
        self.create_units_tab()
        self.create_sales_tab()
        self.create_contractors_tab()
        self.create_suppliers_tab()
        self.create_maintenance_tab()
        self.create_tasks_tab()
        self.create_users_tab()
        self.create_reports_tab()
    
    def create_dashboard_tab(self):
        """إنشاء تبويب لوحة التحكم"""
        dashboard_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(dashboard_frame, text='📊 لوحة التحكم')
        
        # عنوان لوحة التحكم
        title_label = tk.Label(dashboard_frame, text="لوحة التحكم الرئيسية", 
                              font=('Arial', 24, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=20)
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(dashboard_frame, bg='white')
        stats_frame.pack(fill='x', padx=20, pady=20)
        
        # بطاقات الإحصائيات
        self.create_stat_card(stats_frame, "المشاريع", "0", "#3498db", 0, 0)
        self.create_stat_card(stats_frame, "العملاء", "0", "#e74c3c", 0, 1)
        self.create_stat_card(stats_frame, "الوحدات", "0", "#f39c12", 0, 2)
        self.create_stat_card(stats_frame, "العقود", "0", "#27ae60", 0, 3)
        self.create_stat_card(stats_frame, "المقاولين", "0", "#9b59b6", 1, 0)
        self.create_stat_card(stats_frame, "الموردين", "0", "#1abc9c", 1, 1)
        self.create_stat_card(stats_frame, "المهام", "0", "#34495e", 1, 2)
        self.create_stat_card(stats_frame, "المستخدمين", "0", "#e67e22", 1, 3)
        
        # منطقة الأنشطة الحديثة
        activities_label = tk.Label(dashboard_frame, text="الأنشطة الحديثة والإحصائيات", 
                                   font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        activities_label.pack(pady=(30, 10))
        
        # إطار النص مع شريط التمرير
        text_frame = tk.Frame(dashboard_frame, bg='white')
        text_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        self.activities_text = tk.Text(text_frame, height=15, font=('Arial', 11), 
                                      bg='#f8f9fa', relief='sunken', bd=2)
        scrollbar = tk.Scrollbar(text_frame, orient='vertical', command=self.activities_text.yview)
        self.activities_text.configure(yscrollcommand=scrollbar.set)
        
        self.activities_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
    
    def create_stat_card(self, parent, title, value, color, row, col):
        """إنشاء بطاقة إحصائية"""
        card_frame = tk.Frame(parent, bg=color, relief='raised', bd=3)
        card_frame.grid(row=row, column=col, padx=15, pady=15, sticky='ew', ipadx=20, ipady=15)
        parent.grid_columnconfigure(col, weight=1)
        
        title_label = tk.Label(card_frame, text=title, font=('Arial', 14, 'bold'), 
                              bg=color, fg='white')
        title_label.pack(pady=(10, 5))
        
        value_label = tk.Label(card_frame, text=value, font=('Arial', 24, 'bold'), 
                              bg=color, fg='white')
        value_label.pack(pady=(0, 10))
        
        # حفظ مرجع للتحديث لاحقاً
        setattr(self, f"{title}_value_label", value_label)
    
    def create_projects_tab(self):
        """إنشاء تبويب المشاريع"""
        projects_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(projects_frame, text='🏗️ المشاريع')
        
        # شريط الأدوات
        toolbar = tk.Frame(projects_frame, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)
        
        tk.Button(toolbar, text="إضافة مشروع", bg='#27ae60', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.add_project).pack(side='left', padx=5)
        tk.Button(toolbar, text="تعديل مشروع", bg='#f39c12', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.edit_project).pack(side='left', padx=5)
        tk.Button(toolbar, text="حذف مشروع", bg='#e74c3c', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.delete_project).pack(side='left', padx=5)
        tk.Button(toolbar, text="تحديث", bg='#3498db', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.load_projects).pack(side='left', padx=5)
        
        # جدول المشاريع
        columns = ('الرقم', 'اسم المشروع', 'الموقع', 'الميزانية', 'الحالة', 'تاريخ البداية', 'الوصف')
        self.projects_tree = ttk.Treeview(projects_frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            self.projects_tree.heading(col, text=col)
            self.projects_tree.column(col, width=150)
        
        # شريط التمرير
        projects_scrollbar = ttk.Scrollbar(projects_frame, orient='vertical', command=self.projects_tree.yview)
        self.projects_tree.configure(yscrollcommand=projects_scrollbar.set)
        
        self.projects_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        projects_scrollbar.pack(side='right', fill='y', pady=10)
    
    def create_customers_tab(self):
        """إنشاء تبويب العملاء"""
        customers_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(customers_frame, text='👥 العملاء')
        
        # شريط الأدوات
        toolbar = tk.Frame(customers_frame, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)
        
        tk.Button(toolbar, text="إضافة عميل", bg='#27ae60', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.add_customer).pack(side='left', padx=5)
        tk.Button(toolbar, text="تعديل عميل", bg='#f39c12', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.edit_customer).pack(side='left', padx=5)
        tk.Button(toolbar, text="حذف عميل", bg='#e74c3c', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.delete_customer).pack(side='left', padx=5)
        tk.Button(toolbar, text="تحديث", bg='#3498db', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.load_customers).pack(side='left', padx=5)
        
        # جدول العملاء
        columns = ('الرقم', 'الاسم', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'الهوية الوطنية')
        self.customers_tree = ttk.Treeview(customers_frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            self.customers_tree.heading(col, text=col)
            self.customers_tree.column(col, width=150)
        
        # شريط التمرير
        customers_scrollbar = ttk.Scrollbar(customers_frame, orient='vertical', command=self.customers_tree.yview)
        self.customers_tree.configure(yscrollcommand=customers_scrollbar.set)
        
        self.customers_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        customers_scrollbar.pack(side='right', fill='y', pady=10)
    
    def create_units_tab(self):
        """إنشاء تبويب الوحدات"""
        units_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(units_frame, text='🏠 الوحدات')
        
        # شريط الأدوات
        toolbar = tk.Frame(units_frame, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)
        
        tk.Button(toolbar, text="إضافة وحدة", bg='#27ae60', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.add_unit).pack(side='left', padx=5)
        tk.Button(toolbar, text="تعديل وحدة", bg='#f39c12', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.edit_unit).pack(side='left', padx=5)
        tk.Button(toolbar, text="حذف وحدة", bg='#e74c3c', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.delete_unit).pack(side='left', padx=5)
        tk.Button(toolbar, text="حجز وحدة", bg='#f39c12', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.reserve_unit).pack(side='left', padx=5)
        tk.Button(toolbar, text="بيع وحدة", bg='#e74c3c', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.sell_unit).pack(side='left', padx=5)
        tk.Button(toolbar, text="تحديث", bg='#3498db', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.load_units).pack(side='left', padx=5)
        
        # جدول الوحدات
        columns = ('الرقم', 'رقم الوحدة', 'النوع', 'المساحة', 'السعر', 'الحالة', 'المشروع')
        self.units_tree = ttk.Treeview(units_frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            self.units_tree.heading(col, text=col)
            self.units_tree.column(col, width=120)
        
        # شريط التمرير
        units_scrollbar = ttk.Scrollbar(units_frame, orient='vertical', command=self.units_tree.yview)
        self.units_tree.configure(yscrollcommand=units_scrollbar.set)
        
        self.units_tree.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        units_scrollbar.pack(side='right', fill='y', pady=10)
    
    def create_sales_tab(self):
        """إنشاء تبويب المبيعات والعقود"""
        sales_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(sales_frame, text='💰 المبيعات والعقود')
        
        # تبويبات فرعية
        sales_notebook = ttk.Notebook(sales_frame)
        sales_notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # تبويب العقود
        contracts_frame = tk.Frame(sales_notebook, bg='white')
        sales_notebook.add(contracts_frame, text='📄 العقود')
        
        # شريط أدوات العقود
        contracts_toolbar = tk.Frame(contracts_frame, bg='white')
        contracts_toolbar.pack(fill='x', padx=10, pady=10)
        
        tk.Button(contracts_toolbar, text="إضافة عقد", bg='#27ae60', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.add_contract).pack(side='left', padx=5)
        tk.Button(contracts_toolbar, text="تحديث", bg='#3498db', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.load_contracts).pack(side='left', padx=5)
        
        # جدول العقود
        contracts_columns = ('الرقم', 'رقم العقد', 'العميل', 'الوحدة', 'المبلغ الإجمالي', 'المبلغ المدفوع', 'المتبقي', 'النوع', 'الحالة')
        self.contracts_tree = ttk.Treeview(contracts_frame, columns=contracts_columns, show='headings', height=15)
        
        for col in contracts_columns:
            self.contracts_tree.heading(col, text=col)
            self.contracts_tree.column(col, width=120)
        
        self.contracts_tree.pack(fill='both', expand=True, padx=10, pady=10)
        
        # تبويب المدفوعات
        payments_frame = tk.Frame(sales_notebook, bg='white')
        sales_notebook.add(payments_frame, text='💳 المدفوعات')
        
        # شريط أدوات المدفوعات
        payments_toolbar = tk.Frame(payments_frame, bg='white')
        payments_toolbar.pack(fill='x', padx=10, pady=10)
        
        tk.Button(payments_toolbar, text="إضافة دفعة", bg='#27ae60', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.add_payment).pack(side='left', padx=5)
        tk.Button(payments_toolbar, text="تحديث", bg='#3498db', fg='white', 
                 font=('Arial', 10, 'bold'), command=self.load_payments).pack(side='left', padx=5)
        
        # جدول المدفوعات
        payments_columns = ('الرقم', 'العقد', 'المبلغ', 'تاريخ الدفع', 'طريقة الدفع', 'رقم المرجع', 'ملاحظات')
        self.payments_tree = ttk.Treeview(payments_frame, columns=payments_columns, show='headings', height=15)
        
        for col in payments_columns:
            self.payments_tree.heading(col, text=col)
            self.payments_tree.column(col, width=120)
        
        self.payments_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_contractors_tab(self):
        """إنشاء تبويب المقاولين"""
        contractors_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(contractors_frame, text='👷 المقاولين')

        # شريط الأدوات
        toolbar = tk.Frame(contractors_frame, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(toolbar, text="إضافة مقاول", bg='#27ae60', fg='white',
                 font=('Arial', 10, 'bold'), command=self.add_contractor).pack(side='left', padx=5)
        tk.Button(toolbar, text="تحديث", bg='#3498db', fg='white',
                 font=('Arial', 10, 'bold'), command=self.load_contractors).pack(side='left', padx=5)

        # جدول المقاولين
        columns = ('الرقم', 'الاسم', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'التخصص')
        self.contractors_tree = ttk.Treeview(contractors_frame, columns=columns, show='headings', height=20)

        for col in columns:
            self.contractors_tree.heading(col, text=col)
            self.contractors_tree.column(col, width=150)

        self.contractors_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_suppliers_tab(self):
        """إنشاء تبويب الموردين"""
        suppliers_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(suppliers_frame, text='📦 الموردين')

        # شريط الأدوات
        toolbar = tk.Frame(suppliers_frame, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(toolbar, text="إضافة مورد", bg='#27ae60', fg='white',
                 font=('Arial', 10, 'bold'), command=self.add_supplier).pack(side='left', padx=5)
        tk.Button(toolbar, text="تحديث", bg='#3498db', fg='white',
                 font=('Arial', 10, 'bold'), command=self.load_suppliers).pack(side='left', padx=5)

        # جدول الموردين
        columns = ('الرقم', 'الاسم', 'الهاتف', 'البريد الإلكتروني', 'العنوان', 'نوع المواد')
        self.suppliers_tree = ttk.Treeview(suppliers_frame, columns=columns, show='headings', height=20)

        for col in columns:
            self.suppliers_tree.heading(col, text=col)
            self.suppliers_tree.column(col, width=150)

        self.suppliers_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_maintenance_tab(self):
        """إنشاء تبويب الصيانة"""
        maintenance_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(maintenance_frame, text='🔧 الصيانة')

        # شريط الأدوات
        toolbar = tk.Frame(maintenance_frame, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(toolbar, text="إضافة مهمة صيانة", bg='#27ae60', fg='white',
                 font=('Arial', 10, 'bold'), command=self.add_maintenance_task).pack(side='left', padx=5)
        tk.Button(toolbar, text="تحديث", bg='#3498db', fg='white',
                 font=('Arial', 10, 'bold'), command=self.load_maintenance_tasks).pack(side='left', padx=5)

        # جدول مهام الصيانة
        columns = ('الرقم', 'رقم المهمة', 'العنوان', 'النوع', 'الموقع', 'الفني', 'التاريخ', 'التكلفة', 'الحالة')
        self.maintenance_tree = ttk.Treeview(maintenance_frame, columns=columns, show='headings', height=20)

        for col in columns:
            self.maintenance_tree.heading(col, text=col)
            self.maintenance_tree.column(col, width=120)

        self.maintenance_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_tasks_tab(self):
        """إنشاء تبويب المهام اليومية"""
        tasks_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(tasks_frame, text='📋 المهام اليومية')

        # شريط الأدوات
        toolbar = tk.Frame(tasks_frame, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(toolbar, text="إضافة مهمة", bg='#27ae60', fg='white',
                 font=('Arial', 10, 'bold'), command=self.add_daily_task).pack(side='left', padx=5)
        tk.Button(toolbar, text="تحديث", bg='#3498db', fg='white',
                 font=('Arial', 10, 'bold'), command=self.load_daily_tasks).pack(side='left', padx=5)

        # جدول المهام اليومية
        columns = ('الرقم', 'عنوان المهمة', 'الوصف', 'المسؤول', 'الأولوية', 'تاريخ الاستحقاق', 'الحالة')
        self.daily_tasks_tree = ttk.Treeview(tasks_frame, columns=columns, show='headings', height=20)

        for col in columns:
            self.daily_tasks_tree.heading(col, text=col)
            self.daily_tasks_tree.column(col, width=150)

        self.daily_tasks_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_users_tab(self):
        """إنشاء تبويب المستخدمين"""
        users_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(users_frame, text='👤 المستخدمين')

        # شريط الأدوات
        toolbar = tk.Frame(users_frame, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(toolbar, text="إضافة مستخدم", bg='#27ae60', fg='white',
                 font=('Arial', 10, 'bold'), command=self.add_user).pack(side='left', padx=5)
        tk.Button(toolbar, text="تحديث", bg='#3498db', fg='white',
                 font=('Arial', 10, 'bold'), command=self.load_users).pack(side='left', padx=5)

        # جدول المستخدمين
        columns = ('الرقم', 'اسم المستخدم', 'الاسم الكامل', 'البريد الإلكتروني', 'نوع المستخدم', 'الحالة')
        self.users_tree = ttk.Treeview(users_frame, columns=columns, show='headings', height=20)

        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=150)

        self.users_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        reports_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(reports_frame, text='📊 التقارير')

        # عنوان التقارير
        title_label = tk.Label(reports_frame, text="التقارير والإحصائيات التفصيلية",
                              font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        title_label.pack(pady=20)

        # أزرار التقارير
        reports_toolbar = tk.Frame(reports_frame, bg='white')
        reports_toolbar.pack(fill='x', padx=20, pady=10)

        tk.Button(reports_toolbar, text="تقرير المشاريع", bg='#3498db', fg='white',
                 font=('Arial', 10, 'bold'), command=self.generate_projects_report).pack(side='left', padx=5)
        tk.Button(reports_toolbar, text="تقرير المبيعات", bg='#27ae60', fg='white',
                 font=('Arial', 10, 'bold'), command=self.generate_sales_report).pack(side='left', padx=5)
        tk.Button(reports_toolbar, text="تقرير المالي", bg='#f39c12', fg='white',
                 font=('Arial', 10, 'bold'), command=self.generate_financial_report).pack(side='left', padx=5)

        # منطقة عرض التقارير
        self.reports_text = tk.Text(reports_frame, height=25, font=('Arial', 11),
                                   bg='#f8f9fa', relief='sunken', bd=2)
        reports_scrollbar = tk.Scrollbar(reports_frame, orient='vertical', command=self.reports_text.yview)
        self.reports_text.configure(yscrollcommand=reports_scrollbar.set)

        self.reports_text.pack(side='left', fill='both', expand=True, padx=20, pady=10)
        reports_scrollbar.pack(side='right', fill='y', pady=10)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = tk.Frame(self.root, bg='#34495e', height=30)
        self.status_frame.pack(fill='x', side='bottom')
        self.status_frame.pack_propagate(False)

        self.status_label = tk.Label(self.status_frame, text="جاهز",
                                    bg='#34495e', fg='white', font=('Arial', 10))
        self.status_label.pack(side='left', padx=10, pady=5)

        # معلومات إضافية
        time_label = tk.Label(self.status_frame, text=f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                             bg='#34495e', fg='white', font=('Arial', 10))
        time_label.pack(side='right', padx=10, pady=5)

    # دوال تحميل البيانات
    def load_data(self):
        """تحميل جميع البيانات"""
        try:
            self.load_projects()
            self.load_customers()
            self.load_units()
            self.load_contracts()
            self.load_payments()
            self.load_contractors()
            self.load_suppliers()
            self.load_maintenance_tasks()
            self.load_daily_tasks()
            self.load_users()
            self.update_dashboard()
            self.update_status("تم تحميل جميع البيانات بنجاح")
        except Exception as e:
            logger.error(f"خطأ في تحميل البيانات: {e}")
            self.update_status(f"خطأ في تحميل البيانات: {str(e)}")

    def load_projects(self):
        """تحميل المشاريع"""
        try:
            for item in self.projects_tree.get_children():
                self.projects_tree.delete(item)

            query = "SELECT id, name, location, budget, status, start_date, description FROM projects ORDER BY id DESC"
            projects = sqlite_manager.execute_query(query)

            for project in projects:
                budget_text = f"{project['budget']:,.0f} ريال" if project['budget'] else "غير محدد"
                self.projects_tree.insert('', 'end', values=(
                    project['id'],
                    project['name'] or '',
                    project['location'] or '',
                    budget_text,
                    project['status'] or '',
                    project['start_date'] or '',
                    project['description'] or ''
                ))

            self.update_status(f"تم تحميل {len(projects)} مشروع")

        except Exception as e:
            logger.error(f"خطأ في تحميل المشاريع: {e}")
            self.update_status(f"خطأ في تحميل المشاريع: {str(e)}")

    def load_customers(self):
        """تحميل العملاء"""
        try:
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            query = "SELECT id, name, phone, email, address, national_id FROM customers ORDER BY id DESC"
            customers = sqlite_manager.execute_query(query)

            for customer in customers:
                self.customers_tree.insert('', 'end', values=(
                    customer['id'],
                    customer['name'] or '',
                    customer['phone'] or '',
                    customer['email'] or '',
                    customer['address'] or '',
                    customer['national_id'] or ''
                ))

            self.update_status(f"تم تحميل {len(customers)} عميل")

        except Exception as e:
            logger.error(f"خطأ في تحميل العملاء: {e}")
            self.update_status(f"خطأ في تحميل العملاء: {str(e)}")

    def load_units(self):
        """تحميل الوحدات"""
        try:
            for item in self.units_tree.get_children():
                self.units_tree.delete(item)

            query = """
                SELECT u.id, u.unit_number, u.unit_type, u.area, u.price, u.status,
                       COALESCE(p.name, 'غير محدد') as project_name
                FROM units u
                LEFT JOIN projects p ON u.project_id = p.id
                ORDER BY u.id DESC
            """
            units = sqlite_manager.execute_query(query)

            for unit in units:
                area_text = f"{unit['area']:.1f} م²" if unit['area'] else "غير محدد"
                price_text = f"{unit['price']:,.0f} ريال" if unit['price'] else "غير محدد"
                status_text = {
                    'available': 'متاح',
                    'reserved': 'محجوز',
                    'sold': 'مباع'
                }.get(unit['status'], unit['status'])

                self.units_tree.insert('', 'end', values=(
                    unit['id'],
                    unit['unit_number'] or '',
                    unit['unit_type'] or '',
                    area_text,
                    price_text,
                    status_text,
                    unit['project_name']
                ))

            self.update_status(f"تم تحميل {len(units)} وحدة")

        except Exception as e:
            logger.error(f"خطأ في تحميل الوحدات: {e}")
            self.update_status(f"خطأ في تحميل الوحدات: {str(e)}")

    def load_contracts(self):
        """تحميل العقود"""
        try:
            for item in self.contracts_tree.get_children():
                self.contracts_tree.delete(item)

            query = """
                SELECT c.id, c.contract_number,
                       COALESCE(cu.name, 'غير محدد') as customer_name,
                       COALESCE(u.unit_number, 'غير محدد') as unit_number,
                       c.total_amount, c.paid_amount, c.remaining_amount,
                       c.contract_type, c.status
                FROM contracts c
                LEFT JOIN customers cu ON c.customer_id = cu.id
                LEFT JOIN units u ON c.unit_id = u.id
                ORDER BY c.id DESC
            """
            contracts = sqlite_manager.execute_query(query)

            for contract in contracts:
                total_text = f"{contract['total_amount']:,.0f} ريال" if contract['total_amount'] else "0"
                paid_text = f"{contract['paid_amount']:,.0f} ريال" if contract['paid_amount'] else "0"
                remaining_text = f"{contract['remaining_amount']:,.0f} ريال" if contract['remaining_amount'] else "0"

                type_text = 'بيع' if contract['contract_type'] == 'sale' else 'حجز'

                self.contracts_tree.insert('', 'end', values=(
                    contract['id'],
                    contract['contract_number'] or '',
                    contract['customer_name'],
                    contract['unit_number'],
                    total_text,
                    paid_text,
                    remaining_text,
                    type_text,
                    contract['status'] or ''
                ))

            self.update_status(f"تم تحميل {len(contracts)} عقد")

        except Exception as e:
            logger.error(f"خطأ في تحميل العقود: {e}")
            self.update_status(f"خطأ في تحميل العقود: {str(e)}")

    def load_payments(self):
        """تحميل المدفوعات"""
        try:
            for item in self.payments_tree.get_children():
                self.payments_tree.delete(item)

            query = """
                SELECT p.id, c.contract_number, p.amount, p.payment_date,
                       p.payment_method, p.reference_number, p.notes
                FROM payments p
                LEFT JOIN contracts c ON p.contract_id = c.id
                ORDER BY p.id DESC
            """
            payments = sqlite_manager.execute_query(query)

            for payment in payments:
                amount_text = f"{payment['amount']:,.0f} ريال" if payment['amount'] else "0"
                method_text = {
                    'cash': 'نقد',
                    'bank_transfer': 'تحويل بنكي',
                    'check': 'شيك',
                    'card': 'بطاقة'
                }.get(payment['payment_method'], payment['payment_method'])

                self.payments_tree.insert('', 'end', values=(
                    payment['id'],
                    payment['contract_number'] or '',
                    amount_text,
                    payment['payment_date'] or '',
                    method_text,
                    payment['reference_number'] or '',
                    payment['notes'] or ''
                ))

            self.update_status(f"تم تحميل {len(payments)} دفعة")

        except Exception as e:
            logger.error(f"خطأ في تحميل المدفوعات: {e}")
            self.update_status(f"خطأ في تحميل المدفوعات: {str(e)}")

    def load_contractors(self):
        """تحميل المقاولين"""
        try:
            for item in self.contractors_tree.get_children():
                self.contractors_tree.delete(item)

            query = "SELECT id, name, phone, email, address FROM contractors ORDER BY id DESC"
            contractors = sqlite_manager.execute_query(query)

            for contractor in contractors:
                self.contractors_tree.insert('', 'end', values=(
                    contractor['id'],
                    contractor['name'] or '',
                    contractor['phone'] or '',
                    contractor['email'] or '',
                    contractor['address'] or '',
                    'عام'  # التخصص
                ))

            self.update_status(f"تم تحميل {len(contractors)} مقاول")

        except Exception as e:
            logger.error(f"خطأ في تحميل المقاولين: {e}")
            self.update_status(f"خطأ في تحميل المقاولين: {str(e)}")

    def load_suppliers(self):
        """تحميل الموردين"""
        try:
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)

            query = "SELECT id, name, phone, email, address FROM suppliers ORDER BY id DESC"
            suppliers = sqlite_manager.execute_query(query)

            for supplier in suppliers:
                self.suppliers_tree.insert('', 'end', values=(
                    supplier['id'],
                    supplier['name'] or '',
                    supplier['phone'] or '',
                    supplier['email'] or '',
                    supplier['address'] or '',
                    'مواد عامة'  # نوع المواد
                ))

            self.update_status(f"تم تحميل {len(suppliers)} مورد")

        except Exception as e:
            logger.error(f"خطأ في تحميل الموردين: {e}")
            self.update_status(f"خطأ في تحميل الموردين: {str(e)}")

    def load_maintenance_tasks(self):
        """تحميل مهام الصيانة"""
        try:
            for item in self.maintenance_tree.get_children():
                self.maintenance_tree.delete(item)

            query = """
                SELECT id, task_number, task_title, maintenance_type, location,
                       technician, date, cost, status
                FROM maintenance_tasks ORDER BY id DESC
            """
            tasks = sqlite_manager.execute_query(query)

            for task in tasks:
                cost_text = f"{task['cost']:,.0f} ريال" if task['cost'] else "0"
                status_text = {
                    'pending': 'معلق',
                    'in_progress': 'قيد التنفيذ',
                    'completed': 'مكتمل'
                }.get(task['status'], task['status'])

                self.maintenance_tree.insert('', 'end', values=(
                    task['id'],
                    task['task_number'] or '',
                    task['task_title'] or '',
                    task['maintenance_type'] or '',
                    task['location'] or '',
                    task['technician'] or '',
                    task['date'] or '',
                    cost_text,
                    status_text
                ))

            self.update_status(f"تم تحميل {len(tasks)} مهمة صيانة")

        except Exception as e:
            logger.error(f"خطأ في تحميل مهام الصيانة: {e}")
            self.update_status(f"خطأ في تحميل مهام الصيانة: {str(e)}")

    def load_daily_tasks(self):
        """تحميل المهام اليومية"""
        try:
            for item in self.daily_tasks_tree.get_children():
                self.daily_tasks_tree.delete(item)

            query = """
                SELECT id, task_title, description, assigned_to, priority, due_date, status
                FROM daily_tasks ORDER BY id DESC
            """
            tasks = sqlite_manager.execute_query(query)

            for task in tasks:
                priority_text = {
                    'high': 'عالية',
                    'medium': 'متوسطة',
                    'low': 'منخفضة'
                }.get(task['priority'], task['priority'])

                status_text = {
                    'pending': 'معلق',
                    'in_progress': 'قيد التنفيذ',
                    'completed': 'مكتمل'
                }.get(task['status'], task['status'])

                self.daily_tasks_tree.insert('', 'end', values=(
                    task['id'],
                    task['task_title'] or '',
                    task['description'] or '',
                    task['assigned_to'] or '',
                    priority_text,
                    task['due_date'] or '',
                    status_text
                ))

            self.update_status(f"تم تحميل {len(tasks)} مهمة يومية")

        except Exception as e:
            logger.error(f"خطأ في تحميل المهام اليومية: {e}")
            self.update_status(f"خطأ في تحميل المهام اليومية: {str(e)}")

    def load_users(self):
        """تحميل المستخدمين"""
        try:
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            query = "SELECT id, username, full_name, email, user_type, is_active FROM users ORDER BY id DESC"
            users = sqlite_manager.execute_query(query)

            for user in users:
                user_type_text = {
                    'admin': 'مدير',
                    'manager': 'مدير قسم',
                    'employee': 'موظف',
                    'viewer': 'مشاهد'
                }.get(user['user_type'], user['user_type'])

                status_text = 'مفعل' if user['is_active'] else 'غير مفعل'

                self.users_tree.insert('', 'end', values=(
                    user['id'],
                    user['username'] or '',
                    user['full_name'] or '',
                    user['email'] or '',
                    user_type_text,
                    status_text
                ))

            self.update_status(f"تم تحميل {len(users)} مستخدم")

        except Exception as e:
            logger.error(f"خطأ في تحميل المستخدمين: {e}")
            self.update_status(f"خطأ في تحميل المستخدمين: {str(e)}")

    def update_status(self, message):
        """تحديث شريط الحالة"""
        if hasattr(self, 'status_label'):
            self.status_label.config(text=message)

    def update_dashboard(self):
        """تحديث لوحة التحكم"""
        try:
            # إحصائيات المشاريع
            projects_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM projects")[0][0]
            if hasattr(self, 'المشاريع_value_label'):
                self.المشاريع_value_label.config(text=str(projects_count))

            # إحصائيات العملاء
            customers_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM customers")[0][0]
            if hasattr(self, 'العملاء_value_label'):
                self.العملاء_value_label.config(text=str(customers_count))

            # إحصائيات الوحدات
            units_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM units")[0][0]
            if hasattr(self, 'الوحدات_value_label'):
                self.الوحدات_value_label.config(text=str(units_count))

            # إحصائيات العقود
            contracts_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM contracts")[0][0]
            if hasattr(self, 'العقود_value_label'):
                self.العقود_value_label.config(text=str(contracts_count))

            # إحصائيات المقاولين
            contractors_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM contractors")[0][0]
            if hasattr(self, 'المقاولين_value_label'):
                self.المقاولين_value_label.config(text=str(contractors_count))

            # إحصائيات الموردين
            suppliers_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM suppliers")[0][0]
            if hasattr(self, 'الموردين_value_label'):
                self.الموردين_value_label.config(text=str(suppliers_count))

            # إحصائيات المهام
            tasks_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM daily_tasks")[0][0]
            if hasattr(self, 'المهام_value_label'):
                self.المهام_value_label.config(text=str(tasks_count))

            # إحصائيات المستخدمين
            users_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM users")[0][0]
            if hasattr(self, 'المستخدمين_value_label'):
                self.المستخدمين_value_label.config(text=str(users_count))

            # تحديث الأنشطة
            self.update_activities_display()

        except Exception as e:
            logger.error(f"خطأ في تحديث لوحة التحكم: {e}")

    def update_activities_display(self):
        """تحديث عرض الأنشطة"""
        try:
            # جمع الإحصائيات
            projects_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM projects")[0][0]
            customers_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM customers")[0][0]
            units_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM units")[0][0]
            contracts_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM contracts")[0][0]

            # إحصائيات الوحدات حسب الحالة
            available_units = sqlite_manager.execute_query("SELECT COUNT(*) FROM units WHERE status='available'")[0][0]
            reserved_units = sqlite_manager.execute_query("SELECT COUNT(*) FROM units WHERE status='reserved'")[0][0]
            sold_units = sqlite_manager.execute_query("SELECT COUNT(*) FROM units WHERE status='sold'")[0][0]

            # إحصائيات مالية
            total_sales = sqlite_manager.execute_query("SELECT COALESCE(SUM(total_amount), 0) FROM contracts WHERE contract_type='sale'")[0][0]
            total_paid = sqlite_manager.execute_query("SELECT COALESCE(SUM(paid_amount), 0) FROM contracts")[0][0]

            activities = f"""
🎉 مرحباً بك في نظام إدارة شركة رافع للتطوير العقاري!

📊 إحصائيات النظام الحالية:
{'='*50}
🏗️ المشاريع: {projects_count} مشروع
👥 العملاء: {customers_count} عميل
🏠 الوحدات: {units_count} وحدة
📄 العقود: {contracts_count} عقد

🏠 تفاصيل الوحدات:
{'='*30}
✅ متاحة: {available_units} وحدة
🔶 محجوزة: {reserved_units} وحدة
✅ مباعة: {sold_units} وحدة

💰 الإحصائيات المالية:
{'='*30}
💵 إجمالي المبيعات: {total_sales:,.0f} ريال
💳 إجمالي المدفوعات: {total_paid:,.0f} ريال
💰 المتبقي: {total_sales - total_paid:,.0f} ريال

📅 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

✨ الوظائف المتاحة:
{'='*30}
🏗️ إدارة المشاريع والتخطيط
👥 إدارة العملاء والعلاقات
🏠 إدارة الوحدات السكنية
💰 إدارة العقود والمبيعات
💳 تتبع المدفوعات والمالية
👷 إدارة المقاولين والموردين
🔧 إدارة الصيانة والعمليات
📋 إدارة المهام اليومية
👤 إدارة المستخدمين والصلاحيات
📊 التقارير والإحصائيات التفصيلية

🎯 النظام يعمل بكفاءة عالية ويوفر جميع الأدوات اللازمة لإدارة شركة التطوير العقاري بشكل احترافي.
"""

            self.activities_text.delete('1.0', tk.END)
            self.activities_text.insert('1.0', activities)

        except Exception as e:
            logger.error(f"خطأ في تحديث الأنشطة: {e}")

    # دوال الإضافة والتعديل والحذف (مبسطة)
    def add_project(self):
        """إضافة مشروع جديد"""
        name = simpledialog.askstring("إضافة مشروع", "اسم المشروع:")
        if name:
            location = simpledialog.askstring("إضافة مشروع", "موقع المشروع:")
            budget = simpledialog.askfloat("إضافة مشروع", "ميزانية المشروع:")

            try:
                query = "INSERT INTO projects (name, location, budget, status) VALUES (?, ?, ?, 'active')"
                sqlite_manager.execute_query(query, (name, location or '', budget or 0))
                messagebox.showinfo("نجح", "تم إضافة المشروع بنجاح")
                self.load_projects()
                self.update_dashboard()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة المشروع: {str(e)}")

    def edit_project(self):
        """تعديل مشروع"""
        messagebox.showinfo("قريباً", "سيتم تطوير تعديل المشاريع قريباً")

    def delete_project(self):
        """حذف مشروع"""
        selection = self.projects_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للحذف")
            return

        item = self.projects_tree.item(selection[0])
        project_id = item['values'][0]
        project_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف المشروع '{project_name}'؟"):
            try:
                sqlite_manager.execute_query("DELETE FROM projects WHERE id = ?", (project_id,))
                messagebox.showinfo("نجح", "تم حذف المشروع بنجاح")
                self.load_projects()
                self.update_dashboard()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المشروع: {str(e)}")

    def add_customer(self):
        """إضافة عميل جديد"""
        name = simpledialog.askstring("إضافة عميل", "اسم العميل:")
        if name:
            phone = simpledialog.askstring("إضافة عميل", "رقم الهاتف:")
            email = simpledialog.askstring("إضافة عميل", "البريد الإلكتروني:")
            address = simpledialog.askstring("إضافة عميل", "العنوان:")

            try:
                query = "INSERT INTO customers (name, phone, email, address) VALUES (?, ?, ?, ?)"
                sqlite_manager.execute_query(query, (name, phone or '', email or '', address or ''))
                messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
                self.load_customers()
                self.update_dashboard()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة العميل: {str(e)}")

    def edit_customer(self):
        """تعديل عميل"""
        messagebox.showinfo("قريباً", "سيتم تطوير تعديل العملاء قريباً")

    def delete_customer(self):
        """حذف عميل"""
        selection = self.customers_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للحذف")
            return

        item = self.customers_tree.item(selection[0])
        customer_id = item['values'][0]
        customer_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف العميل '{customer_name}'؟"):
            try:
                sqlite_manager.execute_query("DELETE FROM customers WHERE id = ?", (customer_id,))
                messagebox.showinfo("نجح", "تم حذف العميل بنجاح")
                self.load_customers()
                self.update_dashboard()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف العميل: {str(e)}")

    def add_unit(self):
        """إضافة وحدة جديدة"""
        unit_number = simpledialog.askstring("إضافة وحدة", "رقم الوحدة:")
        if unit_number:
            unit_type = simpledialog.askstring("إضافة وحدة", "نوع الوحدة (apartment/villa/office):")
            area = simpledialog.askfloat("إضافة وحدة", "المساحة (م²):")
            price = simpledialog.askfloat("إضافة وحدة", "السعر (ريال):")

            try:
                query = "INSERT INTO units (unit_number, unit_type, area, price, status) VALUES (?, ?, ?, ?, 'available')"
                sqlite_manager.execute_query(query, (unit_number, unit_type or 'apartment', area or 0, price or 0))
                messagebox.showinfo("نجح", "تم إضافة الوحدة بنجاح")
                self.load_units()
                self.update_dashboard()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إضافة الوحدة: {str(e)}")

    def edit_unit(self):
        """تعديل وحدة"""
        messagebox.showinfo("قريباً", "سيتم تطوير تعديل الوحدات قريباً")

    def delete_unit(self):
        """حذف وحدة"""
        selection = self.units_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار وحدة للحذف")
            return

        item = self.units_tree.item(selection[0])
        unit_id = item['values'][0]
        unit_number = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل تريد حذف الوحدة '{unit_number}'؟"):
            try:
                sqlite_manager.execute_query("DELETE FROM units WHERE id = ?", (unit_id,))
                messagebox.showinfo("نجح", "تم حذف الوحدة بنجاح")
                self.load_units()
                self.update_dashboard()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف الوحدة: {str(e)}")

    def reserve_unit(self):
        """حجز وحدة"""
        selection = self.units_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار وحدة للحجز")
            return

        item = self.units_tree.item(selection[0])
        unit_id = item['values'][0]
        unit_number = item['values'][1]

        if messagebox.askyesno("تأكيد الحجز", f"هل تريد حجز الوحدة '{unit_number}'؟"):
            try:
                sqlite_manager.execute_query("UPDATE units SET status='reserved' WHERE id=?", (unit_id,))
                messagebox.showinfo("نجح", f"تم حجز الوحدة {unit_number} بنجاح")
                self.load_units()
                self.update_dashboard()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حجز الوحدة: {str(e)}")

    def sell_unit(self):
        """بيع وحدة"""
        selection = self.units_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار وحدة للبيع")
            return

        item = self.units_tree.item(selection[0])
        unit_id = item['values'][0]
        unit_number = item['values'][1]

        if messagebox.askyesno("تأكيد البيع", f"هل تريد بيع الوحدة '{unit_number}'؟"):
            try:
                sqlite_manager.execute_query("UPDATE units SET status='sold' WHERE id=?", (unit_id,))
                messagebox.showinfo("نجح", f"تم بيع الوحدة {unit_number} بنجاح")
                self.load_units()
                self.update_dashboard()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في بيع الوحدة: {str(e)}")

    # دوال مبسطة للوظائف الأخرى
    def add_contract(self):
        """إضافة عقد جديد"""
        messagebox.showinfo("قريباً", "سيتم تطوير إضافة العقود قريباً")

    def add_payment(self):
        """إضافة دفعة جديدة"""
        messagebox.showinfo("قريباً", "سيتم تطوير إضافة المدفوعات قريباً")

    def add_contractor(self):
        """إضافة مقاول جديد"""
        messagebox.showinfo("قريباً", "سيتم تطوير إضافة المقاولين قريباً")

    def add_supplier(self):
        """إضافة مورد جديد"""
        messagebox.showinfo("قريباً", "سيتم تطوير إضافة الموردين قريباً")

    def add_maintenance_task(self):
        """إضافة مهمة صيانة جديدة"""
        messagebox.showinfo("قريباً", "سيتم تطوير إضافة مهام الصيانة قريباً")

    def add_daily_task(self):
        """إضافة مهمة يومية جديدة"""
        messagebox.showinfo("قريباً", "سيتم تطوير إضافة المهام اليومية قريباً")

    def add_user(self):
        """إضافة مستخدم جديد"""
        messagebox.showinfo("قريباً", "سيتم تطوير إضافة المستخدمين قريباً")

    # دوال التقارير
    def generate_projects_report(self):
        """إنشاء تقرير المشاريع"""
        try:
            projects = sqlite_manager.execute_query("SELECT * FROM projects ORDER BY id DESC")

            report = f"""
📊 تقرير المشاريع
{'='*50}
📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📋 عدد المشاريع: {len(projects)}

تفاصيل المشاريع:
{'='*30}
"""

            for i, project in enumerate(projects, 1):
                budget_text = f"{project['budget']:,.0f} ريال" if project['budget'] else "غير محدد"
                report += f"""
{i}. {project['name']}
   📍 الموقع: {project['location'] or 'غير محدد'}
   💰 الميزانية: {budget_text}
   📊 الحالة: {project['status'] or 'غير محدد'}
   📅 تاريخ البداية: {project['start_date'] or 'غير محدد'}
   📝 الوصف: {project['description'] or 'لا يوجد'}
"""

            self.reports_text.delete('1.0', tk.END)
            self.reports_text.insert('1.0', report)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير المشاريع: {str(e)}")

    def generate_sales_report(self):
        """إنشاء تقرير المبيعات"""
        try:
            contracts = sqlite_manager.execute_query("SELECT * FROM contracts ORDER BY id DESC")
            total_sales = sum(contract['total_amount'] or 0 for contract in contracts)
            total_paid = sum(contract['paid_amount'] or 0 for contract in contracts)

            report = f"""
💰 تقرير المبيعات
{'='*50}
📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
📋 عدد العقود: {len(contracts)}
💵 إجمالي المبيعات: {total_sales:,.0f} ريال
💳 إجمالي المدفوعات: {total_paid:,.0f} ريال
💰 المتبقي: {total_sales - total_paid:,.0f} ريال

تفاصيل العقود:
{'='*30}
"""

            for i, contract in enumerate(contracts, 1):
                total_text = f"{contract['total_amount']:,.0f} ريال" if contract['total_amount'] else "0"
                paid_text = f"{contract['paid_amount']:,.0f} ريال" if contract['paid_amount'] else "0"
                remaining_text = f"{contract['remaining_amount']:,.0f} ريال" if contract['remaining_amount'] else "0"

                report += f"""
{i}. عقد رقم: {contract['contract_number']}
   💰 المبلغ الإجمالي: {total_text}
   💳 المبلغ المدفوع: {paid_text}
   💰 المتبقي: {remaining_text}
   📊 النوع: {'بيع' if contract['contract_type'] == 'sale' else 'حجز'}
   📅 تاريخ العقد: {contract['contract_date'] or 'غير محدد'}
"""

            self.reports_text.delete('1.0', tk.END)
            self.reports_text.insert('1.0', report)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير المبيعات: {str(e)}")

    def generate_financial_report(self):
        """إنشاء التقرير المالي"""
        try:
            # إحصائيات العقود
            contracts_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM contracts")[0][0]
            total_sales = sqlite_manager.execute_query("SELECT COALESCE(SUM(total_amount), 0) FROM contracts")[0][0]
            total_paid = sqlite_manager.execute_query("SELECT COALESCE(SUM(paid_amount), 0) FROM contracts")[0][0]

            # إحصائيات المدفوعات
            payments_count = sqlite_manager.execute_query("SELECT COUNT(*) FROM payments")[0][0]
            cash_payments = sqlite_manager.execute_query("SELECT COALESCE(SUM(amount), 0) FROM payments WHERE payment_method='cash'")[0][0]
            transfer_payments = sqlite_manager.execute_query("SELECT COALESCE(SUM(amount), 0) FROM payments WHERE payment_method='bank_transfer'")[0][0]

            report = f"""
📊 التقرير المالي الشامل
{'='*50}
📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

💰 ملخص العقود:
{'='*30}
📋 عدد العقود: {contracts_count}
💵 إجمالي قيمة العقود: {total_sales:,.0f} ريال
💳 إجمالي المدفوعات: {total_paid:,.0f} ريال
💰 المبالغ المتبقية: {total_sales - total_paid:,.0f} ريال
📊 نسبة التحصيل: {(total_paid/total_sales*100) if total_sales > 0 else 0:.1f}%

💳 تفاصيل المدفوعات:
{'='*30}
📋 عدد المدفوعات: {payments_count}
💵 المدفوعات النقدية: {cash_payments:,.0f} ريال
🏦 التحويلات البنكية: {transfer_payments:,.0f} ريال

📈 مؤشرات الأداء:
{'='*30}
💰 متوسط قيمة العقد: {total_sales/contracts_count if contracts_count > 0 else 0:,.0f} ريال
💳 متوسط الدفعة: {total_paid/payments_count if payments_count > 0 else 0:,.0f} ريال

🎯 التوصيات:
{'='*30}
• متابعة تحصيل المبالغ المتبقية
• تحسين طرق الدفع المتاحة
• زيادة عدد العقود الجديدة
"""

            self.reports_text.delete('1.0', tk.END)
            self.reports_text.insert('1.0', report)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير المالي: {str(e)}")

    def logout(self):
        """تسجيل الخروج"""
        if messagebox.askyesno("تأكيد الخروج", "هل تريد تسجيل الخروج؟"):
            self.root.destroy()

    def run(self):
        """تشغيل النظام"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        # نافذة تسجيل الدخول
        login_window = LoginWindow()
        user_data = login_window.run()

        if user_data:
            # النظام الرئيسي
            main_system = RafeaCompleteSystem(user_data)
            main_system.run()
        else:
            print("تم إلغاء تسجيل الدخول")

    except Exception as e:
        logger.error(f"خطأ في تشغيل النظام: {e}")
        messagebox.showerror("خطأ", f"فشل في تشغيل النظام: {str(e)}")

if __name__ == "__main__":
    main()
