#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل تجريبي للتطبيق باستخدام tkinter
Demo Run Script using tkinter
"""

import sys
import tkinter as tk
from tkinter import ttk, messagebox
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class RafeaMainWindow:
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("نظام إدارة شركة رافع للتطوير العقاري")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f5f5f5')
        
        # تعيين الأيقونة (إذا كانت متوفرة)
        try:
            self.root.iconbitmap('assets/icon.ico')
        except:
            pass
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # شريط العنوان
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x', padx=5, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="نظام إدارة شركة رافع للتطوير العقاري",
            font=('Arial', 18, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(expand=True)
        
        # شريط المعلومات
        info_frame = tk.Frame(self.root, bg='#34495e', height=50)
        info_frame.pack(fill='x', padx=5, pady=(0, 5))
        info_frame.pack_propagate(False)
        
        user_label = tk.Label(
            info_frame,
            text="مرحباً، مدير النظام (admin)",
            font=('Arial', 12),
            fg='white',
            bg='#34495e'
        )
        user_label.pack(side='left', padx=10, pady=10)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#ecf0f1')
        main_frame.pack(fill='both', expand=True, padx=5, pady=(0, 5))
        
        # إنشاء التبويبات
        self.create_tabs(main_frame)
        
        # شريط الحالة
        status_frame = tk.Frame(self.root, bg='#95a5a6', height=30)
        status_frame.pack(fill='x', padx=5, pady=(0, 5))
        status_frame.pack_propagate(False)
        
        status_label = tk.Label(
            status_frame,
            text="جاهز - تشغيل تجريبي",
            font=('Arial', 10),
            fg='white',
            bg='#95a5a6'
        )
        status_label.pack(side='left', padx=10, pady=5)
    
    def create_tabs(self, parent):
        """إنشاء التبويبات"""
        # إنشاء التبويبات
        notebook = ttk.Notebook(parent)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # تبويب لوحة التحكم
        dashboard_frame = tk.Frame(notebook, bg='white')
        notebook.add(dashboard_frame, text='لوحة التحكم')
        self.create_dashboard(dashboard_frame)
        
        # تبويب المشاريع
        projects_frame = tk.Frame(notebook, bg='white')
        notebook.add(projects_frame, text='إدارة المشاريع')
        self.create_projects_tab(projects_frame)
        
        # تبويب المبيعات
        sales_frame = tk.Frame(notebook, bg='white')
        notebook.add(sales_frame, text='مبيعات الشقق')
        self.create_placeholder_tab(sales_frame, "مبيعات الشقق")
        
        # تبويب المقاولين
        contractors_frame = tk.Frame(notebook, bg='white')
        notebook.add(contractors_frame, text='المقاولون')
        self.create_placeholder_tab(contractors_frame, "المقاولون والمستخلصات")
        
        # تبويب التقارير
        reports_frame = tk.Frame(notebook, bg='white')
        notebook.add(reports_frame, text='التقارير')
        self.create_placeholder_tab(reports_frame, "التقارير")
    
    def create_dashboard(self, parent):
        """إنشاء لوحة التحكم"""
        # عنوان لوحة التحكم
        title_label = tk.Label(
            parent,
            text="لوحة التحكم الرئيسية",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        title_label.pack(pady=20)
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(parent, bg='white')
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        # بطاقات الإحصائيات
        stats = [
            ("إجمالي المشاريع", "5", "#e74c3c"),
            ("المشاريع النشطة", "3", "#27ae60"),
            ("إجمالي الوحدات", "150", "#3498db"),
            ("الوحدات المباعة", "85", "#f39c12")
        ]
        
        for i, (title, value, color) in enumerate(stats):
            self.create_stat_card(stats_frame, title, value, color, i)
        
        # معلومات إضافية
        info_text = """
        🎉 مرحباً بك في النسخة التجريبية من نظام إدارة شركة رافع للتطوير العقاري
        
        ✅ تم تطوير النظام باستخدام Python
        ✅ واجهة عربية كاملة مع دعم RTL
        ✅ نظام صلاحيات متقدم
        ✅ إدارة شاملة للمشاريع العقارية
        ✅ تقارير مفصلة وقابلة للتصدير
        
        📋 الوحدات المتاحة:
        • إدارة المشاريع العقارية
        • مبيعات الشقق والوحدات
        • إدارة المقاولين والمستخلصات
        • إدارة الموردين والفواتير
        • نظام المشتريات
        • إدارة الصيانة والتشغيل
        • المهام اليومية
        • التقارير المتقدمة
        """
        
        info_label = tk.Label(
            parent,
            text=info_text,
            font=('Arial', 11),
            fg='#34495e',
            bg='white',
            justify='right'
        )
        info_label.pack(pady=20, padx=20)
    
    def create_stat_card(self, parent, title, value, color, index):
        """إنشاء بطاقة إحصائية"""
        card_frame = tk.Frame(parent, bg=color, relief='raised', bd=2)
        card_frame.grid(row=0, column=index, padx=10, pady=10, sticky='ew')
        parent.grid_columnconfigure(index, weight=1)
        
        value_label = tk.Label(
            card_frame,
            text=value,
            font=('Arial', 20, 'bold'),
            fg='white',
            bg=color
        )
        value_label.pack(pady=(10, 5))
        
        title_label = tk.Label(
            card_frame,
            text=title,
            font=('Arial', 12),
            fg='white',
            bg=color
        )
        title_label.pack(pady=(0, 10))
    
    def create_projects_tab(self, parent):
        """إنشاء تبويب المشاريع"""
        # عنوان
        title_label = tk.Label(
            parent,
            text="إدارة المشاريع العقارية",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        title_label.pack(pady=20)
        
        # شريط الأدوات
        toolbar_frame = tk.Frame(parent, bg='white')
        toolbar_frame.pack(fill='x', padx=20, pady=10)
        
        add_btn = tk.Button(
            toolbar_frame,
            text="إضافة مشروع جديد",
            font=('Arial', 10, 'bold'),
            bg='#27ae60',
            fg='white',
            relief='flat',
            padx=20,
            pady=8,
            command=self.add_project
        )
        add_btn.pack(side='left', padx=(0, 10))
        
        edit_btn = tk.Button(
            toolbar_frame,
            text="تعديل",
            font=('Arial', 10, 'bold'),
            bg='#3498db',
            fg='white',
            relief='flat',
            padx=20,
            pady=8
        )
        edit_btn.pack(side='left', padx=(0, 10))
        
        # جدول المشاريع
        columns = ('الرقم', 'اسم المشروع', 'الموقع', 'النوع', 'الحالة', 'نسبة الإنجاز')
        tree = ttk.Treeview(parent, columns=columns, show='headings', height=10)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)
        
        # بيانات تجريبية
        sample_projects = [
            (1, 'مشروع الواحة السكني', 'الرياض - النرجس', 'سكني', 'قيد التنفيذ', '75%'),
            (2, 'برج الأعمال التجاري', 'جدة - الكورنيش', 'تجاري', 'مكتمل', '100%'),
            (3, 'مجمع الفلل الراقية', 'الدمام - الشاطئ', 'سكني', 'تخطيط', '25%'),
        ]
        
        for project in sample_projects:
            tree.insert('', 'end', values=project)
        
        tree.pack(fill='both', expand=True, padx=20, pady=10)
    
    def create_placeholder_tab(self, parent, tab_name):
        """إنشاء تبويب مؤقت"""
        label = tk.Label(
            parent,
            text=f"تبويب {tab_name}\n\nهذا التبويب قيد التطوير...\n\nسيتم إضافة المزيد من الميزات قريباً",
            font=('Arial', 14),
            fg='#7f8c8d',
            bg='white'
        )
        label.pack(expand=True)
    
    def add_project(self):
        """إضافة مشروع جديد"""
        messagebox.showinfo(
            "إضافة مشروع",
            "سيتم فتح نافذة إضافة مشروع جديد\n(قيد التطوير)"
        )
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("تشغيل تجريبي لنظام شركة رافع للتطوير العقاري")
    print("Demo Run - Rafea Real Estate Management System")
    print("=" * 60)
    
    try:
        # إنشاء وتشغيل التطبيق
        app = RafeaMainWindow()
        print("✅ تم تحميل التطبيق بنجاح")
        print("🚀 يمكنك الآن استخدام التطبيق")
        
        app.run()
        
        print("👋 تم إغلاق التطبيق")
        return 0
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
