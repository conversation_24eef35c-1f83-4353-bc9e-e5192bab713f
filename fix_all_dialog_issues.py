#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح جميع مشاكل النوافذ
Fix All Dialog Issues
"""

import re

def fix_status_values():
    """إصلاح قيم الحالة في جميع النوافذ"""
    
    print("🔧 إصلاح قيم الحالة في النوافذ...")
    
    try:
        # قراءة ملف النوافذ
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح حالات المستخلصات (تم بالفعل)
        # إصلاح حالات الفواتير
        content = content.replace(
            "self.status_combo['values'] = ('غير مدفوعة', 'مدفوعة جزئياً', 'مدفوعة بالكامل')",
            "self.status_combo['values'] = ('unpaid', 'partially_paid', 'paid')"
        )
        content = content.replace(
            "self.status_combo.set('غير مدفوعة')",
            "self.status_combo.set('unpaid')"
        )
        
        # إصلاح حالات المشتريات
        content = content.replace(
            "self.status_combo['values'] = ('pending', 'approved', 'ordered', 'received', 'cancelled')",
            "self.status_combo['values'] = ('pending', 'approved', 'ordered', 'received', 'cancelled')"
        )
        
        # إصلاح حالات الصيانة
        content = content.replace(
            "self.status_combo['values'] = ('جديد', 'قيد التنفيذ', 'مكتمل', 'مؤجل', 'ملغي')",
            "self.status_combo['values'] = ('new', 'in_progress', 'completed', 'postponed', 'cancelled')"
        )
        content = content.replace(
            "self.status_combo.set('جديد')",
            "self.status_combo.set('new')"
        )
        
        # إصلاح حالات المهام اليومية
        content = content.replace(
            "self.status_combo['values'] = ('جديد', 'قيد التنفيذ', 'مكتمل', 'مؤجل')",
            "self.status_combo['values'] = ('new', 'in_progress', 'completed', 'postponed')"
        )
        
        # إصلاح الأولوية
        content = content.replace(
            "self.priority_combo['values'] = ('منخفض', 'متوسط', 'عالي', 'عاجل')",
            "self.priority_combo['values'] = ('low', 'medium', 'high', 'urgent')"
        )
        content = content.replace(
            "self.priority_combo.set('متوسط')",
            "self.priority_combo.set('medium')"
        )
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح قيم الحالة في جميع النوافذ!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قيم الحالة: {e}")
        return False

def fix_field_names():
    """إصلاح أسماء الحقول لتطابق قاعدة البيانات"""
    
    print("🔧 إصلاح أسماء الحقول...")
    
    try:
        # قراءة ملف النوافذ
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح نافذة الفواتير
        invoice_fixes = [
            ("'supplier_name'", "'supplier'"),
            ("'invoice_date'", "'date'"),
        ]
        
        for old_field, new_field in invoice_fixes:
            content = content.replace(old_field, new_field)
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح أسماء الحقول!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح أسماء الحقول: {e}")
        return False

def fix_database_queries():
    """إصلاح استعلامات قاعدة البيانات"""
    
    print("🔧 إصلاح استعلامات قاعدة البيانات...")
    
    try:
        # قراءة الملف الرئيسي
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح استعلام المستخلصات
        content = content.replace(
            "INSERT INTO extracts (extract_number, contractor, project, total_amount, extract_date, status, description)",
            "INSERT INTO extracts (extract_number, contractor_name, project_id, amount, extract_date, status, description)"
        )
        
        # إصلاح استعلام الفواتير
        content = content.replace(
            "INSERT INTO invoices (invoice_number, supplier, amount, date, due_date, status, description)",
            "INSERT INTO invoices (invoice_number, supplier_name, amount, invoice_date, due_date, status, description)"
        )
        
        # إصلاح استعلام المشتريات
        content = content.replace(
            "INSERT INTO purchase_requests (request_number, project, materials, quantity, estimated_cost, request_date, status, notes, created_by)",
            "INSERT INTO purchase_requests (request_number, item_name, quantity, unit_price, total_amount, request_date, status, notes, created_by)"
        )
        
        # إصلاح استعلام الصيانة
        content = content.replace(
            "INSERT INTO maintenance_tasks (task_number, maintenance_type, location, technician, date, cost, status, description)",
            "INSERT INTO maintenance_tasks (task_number, task_description, assigned_to, priority, created_date, status, notes)"
        )
        
        # إصلاح استعلام المهام اليومية
        content = content.replace(
            "INSERT INTO daily_tasks (title, description, assigned_to, priority, due_date, status, created_by)",
            "INSERT INTO daily_tasks (title, description, priority, due_date, status, created_by)"
        )
        
        # كتابة الملف المحدث
        with open('rafea_complete_system.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح استعلامات قاعدة البيانات!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الاستعلامات: {e}")
        return False

def fix_parameter_mapping():
    """إصلاح ربط المعاملات"""
    
    print("🔧 إصلاح ربط المعاملات...")
    
    try:
        # قراءة الملف الرئيسي
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح معاملات المستخلصات
        old_extract_params = """sqlite_manager.execute_query(query, (
                    result.get('extract_number', ''),
                    result.get('contractor_name', ''),  # إصلاح اسم الحقل
                    result.get('project_id', 1),  # إصلاح اسم الحقل
                    result.get('amount', 0.0),
                    result.get('extract_date', ''),  # تطابق قاعدة البيانات
                    result.get('status', ''),
                    result.get('description', '')
                ))"""
        
        new_extract_params = """sqlite_manager.execute_query(query, (
                    result.get('extract_number', ''),
                    result.get('contractor_name', ''),
                    result.get('project_id', 1),
                    result.get('amount', 0.0),
                    result.get('extract_date', ''),
                    result.get('status', ''),
                    result.get('description', '')
                ))"""
        
        content = content.replace(old_extract_params, new_extract_params)
        
        # إصلاح معاملات الفواتير
        old_invoice_params = """sqlite_manager.execute_query(query, (
                    result['invoice_number'],
                    result.get('supplier_name', result.get('supplier', '')),  # إصلاح اسم الحقل
                    result['amount'],
                    result.get('invoice_date', result.get('date', '')),  # إصلاح اسم الحقل
                    result['due_date'],
                    result['status'],
                    result.get('description', '')
                ))"""
        
        new_invoice_params = """sqlite_manager.execute_query(query, (
                    result.get('invoice_number', ''),
                    result.get('supplier_name', ''),
                    result.get('amount', 0.0),
                    result.get('invoice_date', ''),
                    result.get('due_date', ''),
                    result.get('status', ''),
                    result.get('description', '')
                ))"""
        
        content = content.replace(old_invoice_params, new_invoice_params)
        
        # كتابة الملف المحدث
        with open('rafea_complete_system.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح ربط المعاملات!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح ربط المعاملات: {e}")
        return False

def create_test_script():
    """إنشاء سكريبت اختبار شامل"""
    
    test_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع النوافذ المُصلحة
"""

import tkinter as tk
from dialogs import (
    ExtractDialog, InvoiceDialog, PurchaseRequestDialog,
    MaintenanceTaskDialog, DailyTaskDialog
)

def test_all_dialogs():
    """اختبار جميع النوافذ"""
    root = tk.Tk()
    root.withdraw()
    
    dialogs = [
        ("المستخلصات", ExtractDialog),
        ("الفواتير", InvoiceDialog),
        ("المشتريات", PurchaseRequestDialog),
        ("الصيانة", MaintenanceTaskDialog),
        ("المهام اليومية", DailyTaskDialog),
    ]
    
    for name, dialog_class in dialogs:
        try:
            dialog = dialog_class(root, f"اختبار {name}")
            print(f"✅ نافذة {name} تعمل بشكل صحيح")
        except Exception as e:
            print(f"❌ خطأ في نافذة {name}: {e}")
    
    root.destroy()

if __name__ == "__main__":
    test_all_dialogs()
'''
    
    try:
        with open('test_all_dialogs.py', 'w', encoding='utf-8') as f:
            f.write(test_content)
        print("✅ تم إنشاء سكريبت الاختبار: test_all_dialogs.py")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء سكريبت الاختبار: {e}")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🔧 إصلاح جميع مشاكل النوافذ")
    print("=" * 70)
    
    # تشغيل جميع الإصلاحات
    fixes = [
        ("إصلاح قيم الحالة", fix_status_values),
        ("إصلاح أسماء الحقول", fix_field_names),
        ("إصلاح استعلامات قاعدة البيانات", fix_database_queries),
        ("إصلاح ربط المعاملات", fix_parameter_mapping),
        ("إنشاء سكريبت الاختبار", create_test_script),
    ]
    
    success_count = 0
    for name, fix_func in fixes:
        print(f"\\n🔧 {name}...")
        if fix_func():
            success_count += 1
    
    print(f"\\n📊 النتيجة النهائية: {success_count}/{len(fixes)} إصلاحات نجحت")
    
    if success_count == len(fixes):
        print("\\n🎉 تم إصلاح جميع المشاكل بنجاح!")
        print("يمكنك الآن تشغيل النظام واختبار جميع النوافذ.")
    else:
        print("\\n⚠️ بعض الإصلاحات فشلت. راجع الأخطاء أعلاه.")
    
    input("\\nاضغط Enter للخروج...")
