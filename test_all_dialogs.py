#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع النوافذ المُصلحة
"""

import tkinter as tk
from dialogs import (
    ExtractDialog, InvoiceDialog, PurchaseRequestDialog,
    MaintenanceTaskDialog, DailyTaskDialog
)

def test_all_dialogs():
    """اختبار جميع النوافذ"""
    root = tk.Tk()
    root.withdraw()
    
    dialogs = [
        ("المستخلصات", ExtractDialog),
        ("الفواتير", InvoiceDialog),
        ("المشتريات", PurchaseRequestDialog),
        ("الصيانة", MaintenanceTaskDialog),
        ("المهام اليومية", DailyTaskDialog),
    ]
    
    for name, dialog_class in dialogs:
        try:
            dialog = dialog_class(root, f"اختبار {name}")
            print(f"✅ نافذة {name} تعمل بشكل صحيح")
        except Exception as e:
            print(f"❌ خطأ في نافذة {name}: {e}")
    
    root.destroy()

if __name__ == "__main__":
    test_all_dialogs()
