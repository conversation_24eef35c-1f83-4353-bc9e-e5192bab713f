#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار جميع نوافذ الحوار
Test All Dialog Windows
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from dialogs import (
    ProjectDialog, CustomerDialog, UnitDialog, ContractorDialog,
    SupplierDialog, ExtractDialog, InvoiceDialog, PurchaseRequestDialog,
    MaintenanceTaskDialog, DailyTaskDialog, UserDialog
)

class DialogTester:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("اختبار جميع نوافذ الحوار")
        self.root.geometry("600x500")
        self.root.configure(bg='#ecf0f1')
        
        self.create_widgets()
        
    def create_widgets(self):
        """إنشاء واجهة الاختبار"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="اختبار جميع نوافذ الحوار",
            font=('Arial', 18, 'bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.root, bg='#ecf0f1')
        buttons_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # قائمة النوافذ للاختبار
        dialogs = [
            ("إدارة المشاريع", self.test_project_dialog),
            ("إدارة العملاء", self.test_customer_dialog),
            ("إدارة الوحدات", self.test_unit_dialog),
            ("إدارة المقاولين", self.test_contractor_dialog),
            ("إدارة الموردين", self.test_supplier_dialog),
            ("إدارة المستخلصات", self.test_extract_dialog),
            ("إدارة الفواتير", self.test_invoice_dialog),
            ("إدارة المشتريات", self.test_purchase_dialog),
            ("إدارة الصيانة", self.test_maintenance_dialog),
            ("إدارة المهام اليومية", self.test_daily_task_dialog),
            ("إدارة المستخدمين", self.test_user_dialog),
        ]
        
        # إنشاء أزرار الاختبار
        row = 0
        col = 0
        for name, command in dialogs:
            btn = tk.Button(
                buttons_frame,
                text=f"اختبار {name}",
                font=('Arial', 12, 'bold'),
                bg='#3498db',
                fg='white',
                width=25,
                height=2,
                command=command
            )
            btn.grid(row=row, column=col, padx=10, pady=10)
            
            col += 1
            if col > 1:  # عمودين
                col = 0
                row += 1
        
        # زر اختبار جميع النوافذ
        test_all_btn = tk.Button(
            self.root,
            text="اختبار جميع النوافذ",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=30,
            height=2,
            command=self.test_all_dialogs
        )
        test_all_btn.pack(pady=20)
        
    def test_project_dialog(self):
        """اختبار نافذة المشاريع"""
        print("🧪 اختبار نافذة المشاريع...")
        dialog = ProjectDialog(self.root, "إضافة مشروع جديد")
        self.root.wait_window(dialog.dialog)
        
    def test_customer_dialog(self):
        """اختبار نافذة العملاء"""
        print("🧪 اختبار نافذة العملاء...")
        dialog = CustomerDialog(self.root, "إضافة عميل جديد")
        self.root.wait_window(dialog.dialog)
        
    def test_unit_dialog(self):
        """اختبار نافذة الوحدات"""
        print("🧪 اختبار نافذة الوحدات...")
        dialog = UnitDialog(self.root, "إضافة وحدة جديدة")
        self.root.wait_window(dialog.dialog)
        
    def test_contractor_dialog(self):
        """اختبار نافذة المقاولين"""
        print("🧪 اختبار نافذة المقاولين...")
        dialog = ContractorDialog(self.root, "إضافة مقاول جديد")
        self.root.wait_window(dialog.dialog)
        
    def test_supplier_dialog(self):
        """اختبار نافذة الموردين"""
        print("🧪 اختبار نافذة الموردين...")
        dialog = SupplierDialog(self.root, "إضافة مورد جديد")
        self.root.wait_window(dialog.dialog)
        
    def test_extract_dialog(self):
        """اختبار نافذة المستخلصات"""
        print("🧪 اختبار نافذة المستخلصات...")
        dialog = ExtractDialog(self.root, "إضافة مستخلص جديد")
        self.root.wait_window(dialog.dialog)
        
    def test_invoice_dialog(self):
        """اختبار نافذة الفواتير"""
        print("🧪 اختبار نافذة الفواتير...")
        dialog = InvoiceDialog(self.root, "إضافة فاتورة جديدة")
        self.root.wait_window(dialog.dialog)
        
    def test_purchase_dialog(self):
        """اختبار نافذة المشتريات"""
        print("🧪 اختبار نافذة المشتريات...")
        dialog = PurchaseRequestDialog(self.root, "إضافة طلب شراء جديد")
        self.root.wait_window(dialog.dialog)
        
    def test_maintenance_dialog(self):
        """اختبار نافذة الصيانة"""
        print("🧪 اختبار نافذة الصيانة...")
        dialog = MaintenanceTaskDialog(self.root, "إضافة مهمة صيانة جديدة")
        self.root.wait_window(dialog.dialog)
        
    def test_daily_task_dialog(self):
        """اختبار نافذة المهام اليومية"""
        print("🧪 اختبار نافذة المهام اليومية...")
        dialog = DailyTaskDialog(self.root, "إضافة مهمة يومية جديدة")
        self.root.wait_window(dialog.dialog)
        
    def test_user_dialog(self):
        """اختبار نافذة المستخدمين"""
        print("🧪 اختبار نافذة المستخدمين...")
        dialog = UserDialog(self.root, "إضافة مستخدم جديد")
        self.root.wait_window(dialog.dialog)
        
    def test_all_dialogs(self):
        """اختبار جميع النوافذ تلقائياً"""
        print("🧪 اختبار جميع النوافذ...")
        dialogs = [
            ("المشاريع", lambda: ProjectDialog(self.root, "اختبار المشاريع")),
            ("العملاء", lambda: CustomerDialog(self.root, "اختبار العملاء")),
            ("الوحدات", lambda: UnitDialog(self.root, "اختبار الوحدات")),
            ("المقاولين", lambda: ContractorDialog(self.root, "اختبار المقاولين")),
            ("الموردين", lambda: SupplierDialog(self.root, "اختبار الموردين")),
            ("المستخلصات", lambda: ExtractDialog(self.root, "اختبار المستخلصات")),
            ("الفواتير", lambda: InvoiceDialog(self.root, "اختبار الفواتير")),
            ("المشتريات", lambda: PurchaseRequestDialog(self.root, "اختبار المشتريات")),
            ("الصيانة", lambda: MaintenanceTaskDialog(self.root, "اختبار الصيانة")),
            ("المهام اليومية", lambda: DailyTaskDialog(self.root, "اختبار المهام اليومية")),
            ("المستخدمين", lambda: UserDialog(self.root, "اختبار المستخدمين")),
        ]
        
        for name, dialog_func in dialogs:
            try:
                print(f"🔍 اختبار نافذة {name}...")
                dialog = dialog_func()
                # إغلاق النافذة تلقائياً بعد ثانية واحدة
                self.root.after(1000, dialog.dialog.destroy)
                print(f"✅ نافذة {name} تعمل بشكل صحيح")
            except Exception as e:
                print(f"❌ خطأ في نافذة {name}: {e}")
        
        print("🎉 انتهى اختبار جميع النوافذ!")
        
    def run(self):
        """تشغيل الاختبار"""
        print("🧪 بدء اختبار نوافذ الحوار...")
        print("اضغط على أي زر لاختبار النافذة المقابلة")
        self.root.mainloop()

if __name__ == "__main__":
    tester = DialogTester()
    tester.run()
