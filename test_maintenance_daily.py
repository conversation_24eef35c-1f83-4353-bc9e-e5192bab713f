#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مخصص للصيانة والمهام اليومية
"""

import tkinter as tk
from dialogs import MaintenanceTaskDialog, DailyTaskDialog

def test_maintenance_dialog():
    """اختبار نافذة الصيانة"""
    print("🔧 اختبار نافذة الصيانة...")
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        dialog = MaintenanceTaskDialog(root, "اختبار الصيانة")
        
        # ملء البيانات تلقائياً
        dialog.task_number_entry.insert(0, "MAIN-TEST-001")
        dialog.description_text.insert('1.0', "اختبار مهمة صيانة")
        dialog.assigned_to_entry.insert(0, "فريق الاختبار")
        dialog.priority_combo.set('medium')
        dialog.status_combo.set('pending')
        
        # محاكاة الحفظ
        dialog.save_task()
        
        result = dialog.result
        if result:
            print("✅ نافذة الصيانة تعمل وتحفظ البيانات بنجاح!")
            print(f"   البيانات المحفوظة: {len(result)} حقل")
        else:
            print("❌ فشل في حفظ بيانات الصيانة")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة الصيانة: {e}")
    finally:
        root.destroy()

def test_daily_tasks_dialog():
    """اختبار نافذة المهام اليومية"""
    print("📅 اختبار نافذة المهام اليومية...")
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        dialog = DailyTaskDialog(root, "اختبار المهام اليومية")
        
        # ملء البيانات تلقائياً
        dialog.title_entry.insert(0, "مهمة اختبار يومية")
        dialog.description_text.insert('1.0', "وصف مهمة الاختبار")
        dialog.priority_combo.set('medium')
        dialog.status_combo.set('pending')
        
        # محاكاة الحفظ
        dialog.save_task()
        
        result = dialog.result
        if result:
            print("✅ نافذة المهام اليومية تعمل وتحفظ البيانات بنجاح!")
            print(f"   البيانات المحفوظة: {len(result)} حقل")
        else:
            print("❌ فشل في حفظ بيانات المهمة اليومية")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة المهام اليومية: {e}")
    finally:
        root.destroy()

def main():
    """الاختبار الرئيسي"""
    print("=" * 60)
    print("🧪 اختبار مخصص للصيانة والمهام اليومية")
    print("=" * 60)
    
    test_maintenance_dialog()
    print()
    test_daily_tasks_dialog()
    
    print("\n🎉 انتهى الاختبار!")

if __name__ == "__main__":
    main()
