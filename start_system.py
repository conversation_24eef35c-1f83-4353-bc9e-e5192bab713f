#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت بدء تشغيل النظام
System Startup Script
"""

import os
import sys

def main():
    """تشغيل النظام"""
    print("=" * 70)
    print("🏢 النظام الشامل لإدارة شركة رافع للتطوير العقاري")
    print("=" * 70)
    
    try:
        # التحقق من وجود قاعدة البيانات
        if not os.path.exists('data/rafea_system.db'):
            print("⚠️ قاعدة البيانات غير موجودة!")
            print("يرجى تشغيل: python create_database_from_scratch.py")
            return
        
        # تشغيل النظام
        import rafea_complete_system
        print("✅ تم تشغيل النظام بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("\nللمساعدة:")
        print("1. تشغيل: python create_database_from_scratch.py")
        print("2. تشغيل: python ultimate_system_fixer.py")
        print("3. تشغيل: python start_system.py")

if __name__ == "__main__":
    main()
