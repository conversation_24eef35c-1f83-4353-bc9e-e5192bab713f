#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح أحجام نوافذ الحوار
Fix Dialog Window Sizes
"""

import re

def fix_window_sizes():
    """إصلاح أحجام جميع نوافذ الحوار"""
    
    print("🔧 إصلاح أحجام نوافذ الحوار...")
    
    try:
        # قراءة الملف
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # قائمة الإصلاحات المطلوبة
        fixes = [
            # إصلاح الأحجام الصغيرة
            (r'geometry\("450x500"\)', 'geometry("450x580")'),
            (r'geometry\("500x500"\)', 'geometry("500x580")'),
            (r'geometry\("500x550"\)', 'geometry("500x600")'),
            (r'geometry\("500x600"\)', 'geometry("500x650")'),
            
            # إصلاح التوسيط المقابل
            (r'geometry\(f"450x500\+\{x\}\+\{y\}"\)', 'geometry(f"450x580+{x}+{y}")'),
            (r'geometry\(f"500x500\+\{x\}\+\{y\}"\)', 'geometry(f"500x580+{x}+{y}")'),
            (r'geometry\(f"500x550\+\{x\}\+\{y\}"\)', 'geometry(f"500x600+{x}+{y}")'),
            (r'geometry\(f"500x600\+\{x\}\+\{y\}"\)', 'geometry(f"500x650+{x}+{y}")'),
            
            # إصلاح حسابات التوسيط
            (r'\(500 // 2\) - \(500 // 2\)', '(500 // 2) - (580 // 2)'),
            (r'\(450 // 2\) - \(500 // 2\)', '(450 // 2) - (580 // 2)'),
            (r'\(500 // 2\) - \(550 // 2\)', '(500 // 2) - (600 // 2)'),
            (r'\(500 // 2\) - \(600 // 2\)', '(500 // 2) - (650 // 2)'),
        ]
        
        # تطبيق الإصلاحات
        changes_made = 0
        for pattern, replacement in fixes:
            matches = re.findall(pattern, content)
            if matches:
                content = re.sub(pattern, replacement, content)
                changes_made += len(matches)
                print(f"✅ تم إصلاح {len(matches)} حالة من النمط: {pattern}")
        
        # إصلاحات إضافية للتأكد من ظهور الأزرار
        additional_fixes = [
            # التأكد من وجود مسافة كافية للأزرار
            (r'buttons_frame\.pack\(fill=\'x\', pady=\(10, 0\)\)', 
             'buttons_frame.pack(fill=\'x\', pady=(15, 10))'),
            
            # زيادة حجم الأزرار قليلاً
            (r'width=12,\s+height=2,', 'width=15,\n            height=2,'),
        ]
        
        for pattern, replacement in additional_fixes:
            matches = re.findall(pattern, content)
            if matches:
                content = re.sub(pattern, replacement, content)
                changes_made += len(matches)
                print(f"✅ تم تطبيق إصلاح إضافي: {len(matches)} حالة")
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ تم إصلاح {changes_made} تغيير في نوافذ الحوار!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الأحجام: {e}")
        return False

def add_debug_info():
    """إضافة معلومات تشخيصية للنوافذ"""
    
    print("🔍 إضافة معلومات تشخيصية...")
    
    try:
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة طباعة معلومات تشخيصية
        debug_code = '''        
        # معلومات تشخيصية
        print(f"🔍 نافذة الحوار: {self.dialog.winfo_geometry()}")
        print(f"🔍 الإطار الرئيسي: {main_frame.winfo_geometry()}")
        print(f"🔍 إطار الأزرار: {buttons_frame.winfo_geometry()}")
        print("✅ تم إنشاء الأزرار بنجاح!")'''
        
        # البحث عن مكان إضافة الكود التشخيصي
        pattern = r'(cancel_btn\.pack\(side=\'right\', padx=\(10, 10\)\))'
        replacement = r'\1' + debug_code
        
        content = re.sub(pattern, replacement, content)
        
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة معلومات تشخيصية!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المعلومات التشخيصية: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🔧 إصلاح أحجام نوافذ الحوار")
    print("=" * 60)
    
    success1 = fix_window_sizes()
    success2 = add_debug_info()
    
    if success1 and success2:
        print("\n🎉 تم إصلاح جميع النوافذ بنجاح!")
        print("الآن ستكون النوافذ أطول وستظهر الأزرار بوضوح.")
        print("كما ستظهر معلومات تشخيصية في وحدة التحكم.")
    else:
        print("\n❌ فشل في بعض الإصلاحات!")
    
    input("\nاضغط Enter للخروج...")
