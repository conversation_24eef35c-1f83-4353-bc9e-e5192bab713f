#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح أعمدة قاعدة البيانات
Fix Database Columns
"""

def fix_all_database_issues():
    """إصلاح جميع مشاكل قاعدة البيانات"""
    
    print("🔧 إصلاح جميع مشاكل قاعدة البيانات...")
    
    try:
        # قراءة الملف الرئيسي
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح المستخلصات - استخدام الأعمدة الموجودة فعلاً
        content = content.replace(
            "INSERT INTO extracts (extract_number, contractor, project, amount, extract_date, status, description)",
            "INSERT INTO extracts (extract_number, contractor, project, amount, extract_date, status, description)"
        )
        
        # إصلاح معاملات المستخلصات
        old_extract = """sqlite_manager.execute_query(query, (
                    result.get('extract_number', ''),
                    result.get('contractor_name', ''),  # النافذة ترسل contractor_name
                    result.get('project_id', 1),
                    result.get('amount', 0.0),
                    result.get('extract_date', ''),
                    result.get('status', ''),
                    result.get('description', '')
                ))"""
        
        new_extract = """sqlite_manager.execute_query(query, (
                    result.get('extract_number', ''),
                    result.get('contractor_name', ''),  # النافذة ترسل contractor_name
                    1,  # project ID ثابت
                    result.get('amount', 0.0),
                    result.get('extract_date', ''),
                    result.get('status', ''),
                    result.get('description', '')
                ))"""
        
        content = content.replace(old_extract, new_extract)
        
        # إصلاح الفواتير
        content = content.replace(
            "INSERT INTO invoices (invoice_number, supplier_name, amount, invoice_date, due_date, status, description)",
            "INSERT INTO invoices (invoice_number, supplier, amount, date, due_date, status, description)"
        )
        
        # إصلاح معاملات الفواتير
        old_invoice = """sqlite_manager.execute_query(query, (
                    result.get('invoice_number', ''),
                    result.get('supplier_name', ''),
                    result.get('amount', 0.0),
                    result.get('invoice_date', ''),
                    result.get('due_date', ''),
                    result.get('status', ''),
                    result.get('description', '')
                ))"""
        
        new_invoice = """sqlite_manager.execute_query(query, (
                    result.get('invoice_number', ''),
                    result.get('supplier_name', ''),  # النافذة ترسل supplier_name
                    result.get('amount', 0.0),
                    result.get('invoice_date', ''),  # النافذة ترسل invoice_date
                    result.get('due_date', ''),
                    result.get('status', ''),
                    result.get('description', '')
                ))"""
        
        content = content.replace(old_invoice, new_invoice)
        
        # إصلاح المشتريات
        content = content.replace(
            "INSERT INTO purchase_requests (request_number, item_name, quantity, unit_price, total_amount, request_date, status, notes, created_by)",
            "INSERT INTO purchase_requests (request_number, project, materials, quantity, estimated_cost, request_date, status, notes, created_by)"
        )
        
        # إصلاح معاملات المشتريات
        old_purchase = """sqlite_manager.execute_query(query, (
                    result.get('request_number', ''),
                    result.get('project_id', 1),  # إصلاح اسم الحقل
                    result.get('item_name', ''),  # إصلاح اسم الحقل
                    result.get('quantity', 0),
                    result.get('total_amount', 0),  # إصلاح اسم الحقل
                    result.get('request_date', ''),
                    result.get('status', ''),
                    result.get('notes', ''),
                    result.get('created_by', 1)
                ))"""
        
        new_purchase = """sqlite_manager.execute_query(query, (
                    result.get('request_number', ''),
                    1,  # project ID ثابت
                    result.get('item_name', ''),  # النافذة ترسل item_name
                    result.get('quantity', 0),
                    result.get('total_amount', 0),  # النافذة ترسل total_amount
                    result.get('request_date', ''),
                    result.get('status', ''),
                    result.get('notes', ''),
                    1  # created_by ثابت
                ))"""
        
        content = content.replace(old_purchase, new_purchase)
        
        # إصلاح الصيانة
        content = content.replace(
            "INSERT INTO maintenance_tasks (task_number, task_description, assigned_to, priority, created_date, status, notes)",
            "INSERT INTO maintenance_tasks (task_number, maintenance_type, location, technician, date, cost, status, description)"
        )
        
        # إصلاح معاملات الصيانة
        old_maintenance = """sqlite_manager.execute_query(query, (
                    result.get('task_number', ''),
                    result.get('task_description', ''),  # إصلاح اسم الحقل
                    result.get('location', ''),
                    result.get('assigned_to', ''),  # إصلاح اسم الحقل
                    result.get('created_date', ''),  # إصلاح اسم الحقل
                    result.get('cost', 0),
                    result.get('status', ''),
                    result.get('task_description', '')  # إصلاح اسم الحقل
                ))"""
        
        new_maintenance = """sqlite_manager.execute_query(query, (
                    result.get('task_number', ''),
                    result.get('task_description', ''),  # النافذة ترسل task_description
                    'مكان العمل',  # location ثابت
                    result.get('assigned_to', ''),  # النافذة ترسل assigned_to
                    result.get('created_date', ''),  # النافذة ترسل created_date
                    0,  # cost ثابت
                    result.get('status', ''),
                    result.get('task_description', '')  # النافذة ترسل task_description
                ))"""
        
        content = content.replace(old_maintenance, new_maintenance)
        
        # إصلاح المهام اليومية
        content = content.replace(
            "INSERT INTO daily_tasks (title, description, priority, due_date, status, created_by)",
            "INSERT INTO daily_tasks (title, description, assigned_to, priority, due_date, status, created_by)"
        )
        
        # إصلاح معاملات المهام اليومية
        old_daily = """sqlite_manager.execute_query(query, (
                    result.get('title', ''),
                    result.get('description', ''),
                    result.get('assigned_to', ''),
                    result.get('priority', ''),
                    result.get('due_date', ''),
                    result.get('status', ''),
                    self.user_data['id']  # المستخدم الحالي
                ))"""
        
        new_daily = """sqlite_manager.execute_query(query, (
                    result.get('title', ''),
                    result.get('description', ''),
                    'المستخدم الحالي',  # assigned_to ثابت
                    result.get('priority', ''),
                    result.get('due_date', ''),
                    result.get('status', ''),
                    self.user_data['id']  # المستخدم الحالي
                ))"""
        
        content = content.replace(old_daily, new_daily)
        
        # كتابة الملف المحدث
        with open('rafea_complete_system.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح جميع مشاكل قاعدة البيانات!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
        return False

def fix_dialog_data():
    """إصلاح بيانات النوافذ"""
    
    print("🔧 إصلاح بيانات النوافذ...")
    
    try:
        # قراءة ملف النوافذ
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح نافذة الفواتير لترسل البيانات الصحيحة
        old_invoice_data = """invoice_data = {
                'invoice_number': self.invoice_number_entry.get().strip(),
                'supplier_id': 1,  # إضافة الحقل المفقود
                'supplier': self.supplier_name_entry.get().strip(),  # تطابق قاعدة البيانات
                'amount': float(self.amount_entry.get()) if self.amount_entry.get().strip() else 0.0,
                'date': self.date_entry.get().strip(),  # تطابق قاعدة البيانات
                'due_date': self.date_entry.get().strip(),  # إضافة الحقل المفقود
                'status': self.status_combo.get(),
                'description': '',  # إضافة الحقل المفقود
                'created_by': 1,  # إضافة الحقل المفقود
                'created_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }"""
        
        new_invoice_data = """invoice_data = {
                'invoice_number': self.invoice_number_entry.get().strip(),
                'supplier_name': self.supplier_name_entry.get().strip(),
                'amount': float(self.amount_entry.get()) if self.amount_entry.get().strip() else 0.0,
                'invoice_date': self.date_entry.get().strip(),
                'due_date': self.date_entry.get().strip(),
                'status': self.status_combo.get(),
                'description': '',
                'created_by': 1,
                'created_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }"""
        
        content = content.replace(old_invoice_data, new_invoice_data)
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح بيانات النوافذ!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح بيانات النوافذ: {e}")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🔧 إصلاح أعمدة قاعدة البيانات")
    print("=" * 70)
    
    # تشغيل جميع الإصلاحات
    fixes = [
        ("إصلاح قاعدة البيانات", fix_all_database_issues),
        ("إصلاح بيانات النوافذ", fix_dialog_data),
    ]
    
    success_count = 0
    for name, fix_func in fixes:
        print(f"\\n🔧 {name}...")
        if fix_func():
            success_count += 1
    
    print(f"\\n📊 النتيجة النهائية: {success_count}/{len(fixes)} إصلاحات نجحت")
    
    if success_count == len(fixes):
        print("\\n🎉 تم إصلاح جميع مشاكل قاعدة البيانات بنجاح!")
        print("يمكنك الآن تشغيل النظام وإضافة البيانات بدون أخطاء.")
    else:
        print("\\n⚠️ بعض الإصلاحات فشلت. راجع الأخطاء أعلاه.")
    
    input("\\nاضغط Enter للخروج...")
