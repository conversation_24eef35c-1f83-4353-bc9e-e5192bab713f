# -*- coding: utf-8 -*-
"""
إعداد نظام السجلات
Logger Setup Module
"""

import sys
from pathlib import Path
from loguru import logger
from config import LOGGING_CONFIG, LOGS_DIR

def setup_logger():
    """إعداد نظام السجلات"""
    try:
        # إزالة المعالج الافتراضي
        logger.remove()
        
        # إضافة معالج وحدة التحكم
        logger.add(
            sys.stderr,
            format=LOGGING_CONFIG['format'],
            level=LOGGING_CONFIG['level'],
            colorize=True
        )
        
        # إضافة معالج الملف العام
        logger.add(
            LOGS_DIR / "app.log",
            format=LOGGING_CONFIG['format'],
            level=LOGGING_CONFIG['level'],
            rotation=LOGGING_CONFIG['rotation'],
            retention=LOGGING_CONFIG['retention'],
            compression=LOGGING_CONFIG['compression'],
            encoding='utf-8'
        )
        
        # إضافة معالج ملف الأخطاء
        logger.add(
            LOGS_DIR / "errors.log",
            format=LOGGING_CONFIG['format'],
            level="ERROR",
            rotation=LOGGING_CONFIG['rotation'],
            retention=LOGGING_CONFIG['retention'],
            compression=LOGGING_CONFIG['compression'],
            encoding='utf-8'
        )
        
        # إضافة معالج ملف قاعدة البيانات
        logger.add(
            LOGS_DIR / "database.log",
            format=LOGGING_CONFIG['format'],
            level="INFO",
            rotation=LOGGING_CONFIG['rotation'],
            retention=LOGGING_CONFIG['retention'],
            compression=LOGGING_CONFIG['compression'],
            encoding='utf-8',
            filter=lambda record: "database" in record["name"].lower()
        )
        
        logger.info("تم إعداد نظام السجلات بنجاح")
        
    except Exception as e:
        print(f"خطأ في إعداد نظام السجلات: {e}")
        raise
