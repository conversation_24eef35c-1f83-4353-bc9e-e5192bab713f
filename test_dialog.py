#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نوافذ الحوار
Test Dialog Windows
"""

import tkinter as tk
from tkinter import ttk, messagebox

class TestDialog:
    def __init__(self, parent):
        self.parent = parent
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("اختبار نافذة الحوار")
        self.dialog.geometry("500x400")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"500x400+{x}+{y}")
        
        self.create_widgets()
        
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = tk.Label(
            main_frame,
            text="اختبار نافذة الحوار",
            font=('Arial', 16, 'bold'),
            bg='#f5f5f5',
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))
        
        # إطار المحتوى
        content_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        content_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        # حقل الاسم
        tk.Label(content_frame, text="الاسم:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(20, 5), padx=20)
        self.name_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.name_entry.pack(pady=(0, 15), padx=20)
        
        # حقل الوصف
        tk.Label(content_frame, text="الوصف:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.desc_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.desc_entry.pack(pady=(0, 20), padx=20)
        
        # أزرار التحكم - هذا هو الجزء المهم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x', pady=(10, 0))
        
        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=15,
            height=2,
            command=self.save_data
        )
        save_btn.pack(side='right', padx=(10, 0))
        
        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=15,
            height=2,
            command=self.cancel
        )
        cancel_btn.pack(side='right', padx=(10, 10))
        
        print("✅ تم إنشاء الأزرار بنجاح!")
        print(f"📍 موقع الأزرار: {buttons_frame.winfo_geometry()}")
        
    def save_data(self):
        """حفظ البيانات"""
        name = self.name_entry.get().strip()
        desc = self.desc_entry.get().strip()
        
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال الاسم")
            return
            
        messagebox.showinfo("نجح", f"تم حفظ البيانات:\nالاسم: {name}\nالوصف: {desc}")
        self.result = {'name': name, 'desc': desc}
        self.dialog.destroy()
        
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

def test_dialog():
    """اختبار نافذة الحوار"""
    root = tk.Tk()
    root.title("اختبار النظام")
    root.geometry("300x200")
    root.configure(bg='#ecf0f1')
    
    # زر لفتح نافذة الحوار
    def open_dialog():
        dialog = TestDialog(root)
        root.wait_window(dialog.dialog)
        if dialog.result:
            print(f"النتيجة: {dialog.result}")
        else:
            print("تم الإلغاء")
    
    test_btn = tk.Button(
        root,
        text="اختبار نافذة الحوار",
        font=('Arial', 14, 'bold'),
        bg='#3498db',
        fg='white',
        width=20,
        height=3,
        command=open_dialog
    )
    test_btn.pack(expand=True)
    
    print("🔧 اختبار نوافذ الحوار")
    print("اضغط على الزر لفتح نافذة الحوار")
    
    root.mainloop()

if __name__ == "__main__":
    test_dialog()
