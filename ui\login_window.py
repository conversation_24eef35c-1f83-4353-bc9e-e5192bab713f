# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول
Login Window
"""

import bcrypt
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QMessageBox, QFrame,
                            QGridLayout, QCheckBox, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon
from loguru import logger

from database import get_db_session
from config import APP_CONFIG, SECURITY_CONFIG, SYSTEM_MESSAGES

class LoginWindow(QDialog):
    """نافذة تسجيل الدخول"""
    
    login_successful = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.failed_attempts = 0
        self.is_locked = False
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - " + APP_CONFIG['name'])
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # شعار الشركة
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: white;
                font-size: 24px;
                font-weight: bold;
                padding: 20px;
                border-radius: 10px;
            }
        """)
        logo_label.setText("شركة رافع للتطوير العقاري")
        main_layout.addWidget(logo_label)
        
        # عنوان تسجيل الدخول
        title_label = QLabel("تسجيل الدخول")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin: 10px 0;")
        main_layout.addWidget(title_label)
        
        # إطار النموذج
        form_frame = QFrame()
        form_frame.setFrameStyle(QFrame.StyledPanel)
        form_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #ecf0f1;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        form_layout = QGridLayout(form_frame)
        form_layout.setSpacing(15)
        
        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم")
        self.username_edit.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                font-size: 14px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        
        form_layout.addWidget(username_label, 0, 0)
        form_layout.addWidget(self.username_edit, 0, 1)
        
        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setFont(QFont("Arial", 12, QFont.Bold))
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("أدخل كلمة المرور")
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                font-size: 14px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        
        form_layout.addWidget(password_label, 1, 0)
        form_layout.addWidget(self.password_edit, 1, 1)
        
        # خانة تذكر كلمة المرور
        self.remember_checkbox = QCheckBox("تذكر كلمة المرور")
        self.remember_checkbox.setStyleSheet("font-size: 12px; color: #7f8c8d;")
        form_layout.addWidget(self.remember_checkbox, 2, 1)
        
        main_layout.addWidget(form_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 12px 30px;
                border: none;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFont(QFont("Arial", 12, QFont.Bold))
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 12px 30px;
                border: none;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        
        buttons_layout.addWidget(self.login_button)
        buttons_layout.addWidget(self.cancel_button)
        main_layout.addLayout(buttons_layout)
        
        # رسالة الحالة
        self.status_label = QLabel()
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                font-size: 12px;
                padding: 5px;
            }
        """)
        main_layout.addWidget(self.status_label)
        
        # مساحة فارغة
        spacer = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)
        main_layout.addItem(spacer)
        
        # معلومات الإصدار
        version_label = QLabel(f"الإصدار {APP_CONFIG['version']}")
        version_label.setAlignment(Qt.AlignCenter)
        version_label.setStyleSheet("color: #95a5a6; font-size: 10px;")
        main_layout.addWidget(version_label)
        
        self.setLayout(main_layout)
        
        # تركيز على حقل اسم المستخدم
        self.username_edit.setFocus()
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.reject)
        self.username_edit.returnPressed.connect(self.password_edit.setFocus)
        self.password_edit.returnPressed.connect(self.handle_login)
    
    def handle_login(self):
        """معالج تسجيل الدخول"""
        if self.is_locked:
            self.show_status_message("الحساب مقفل مؤقتاً. يرجى المحاولة لاحقاً.", "error")
            return
        
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        
        # التحقق من صحة البيانات
        if not username or not password:
            self.show_status_message("يرجى إدخال اسم المستخدم وكلمة المرور", "error")
            return
        
        # تعطيل الأزرار أثناء المعالجة
        self.login_button.setEnabled(False)
        self.login_button.setText("جاري التحقق...")
        
        try:
            # التحقق من بيانات المستخدم
            user_data = self.authenticate_user(username, password)
            
            if user_data:
                self.show_status_message(SYSTEM_MESSAGES['login_success'], "success")
                logger.info(f"تم تسجيل دخول المستخدم: {username}")
                
                # إرسال إشارة نجاح تسجيل الدخول
                self.login_successful.emit(user_data)
            else:
                self.failed_attempts += 1
                remaining_attempts = SECURITY_CONFIG['max_login_attempts'] - self.failed_attempts
                
                if remaining_attempts > 0:
                    self.show_status_message(
                        f"بيانات خاطئة. المحاولات المتبقية: {remaining_attempts}",
                        "error"
                    )
                else:
                    self.lock_account()
                
                logger.warning(f"محاولة دخول فاشلة للمستخدم: {username}")
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل الدخول: {e}")
            self.show_status_message("خطأ في الاتصال بقاعدة البيانات", "error")
        
        finally:
            # إعادة تفعيل الأزرار
            self.login_button.setEnabled(True)
            self.login_button.setText("تسجيل الدخول")
    
    def authenticate_user(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        try:
            with get_db_session() as session:
                # البحث عن المستخدم
                result = session.execute(
                    "SELECT id, username, password_hash, full_name, user_type, is_active FROM users WHERE username = %s",
                    (username,)
                )
                user = result.fetchone()
                
                if not user:
                    return None
                
                # التحقق من حالة المستخدم
                if not user[5]:  # is_active
                    self.show_status_message("الحساب غير مفعل", "error")
                    return None
                
                # التحقق من كلمة المرور
                if bcrypt.checkpw(password.encode('utf-8'), user[2].encode('utf-8')):
                    return {
                        'id': user[0],
                        'username': user[1],
                        'full_name': user[3],
                        'user_type': user[4]
                    }
                
                return None
                
        except Exception as e:
            logger.error(f"خطأ في التحقق من المستخدم: {e}")
            raise
    
    def lock_account(self):
        """قفل الحساب مؤقتاً"""
        self.is_locked = True
        self.login_button.setEnabled(False)
        self.show_status_message("تم قفل الحساب مؤقتاً لمدة 15 دقيقة", "error")
        
        # إعادة تفعيل الحساب بعد فترة
        QTimer.singleShot(SECURITY_CONFIG['lockout_duration'] * 1000, self.unlock_account)
    
    def unlock_account(self):
        """إلغاء قفل الحساب"""
        self.is_locked = False
        self.failed_attempts = 0
        self.login_button.setEnabled(True)
        self.status_label.clear()
    
    def show_status_message(self, message, message_type="info"):
        """عرض رسالة الحالة"""
        colors = {
            "success": "#27ae60",
            "error": "#e74c3c",
            "info": "#3498db",
            "warning": "#f39c12"
        }
        
        color = colors.get(message_type, colors["info"])
        self.status_label.setStyleSheet(f"color: {color}; font-size: 12px; padding: 5px;")
        self.status_label.setText(message)
        
        # إخفاء الرسالة بعد 5 ثوان
        if message_type != "error":
            QTimer.singleShot(5000, self.status_label.clear)
