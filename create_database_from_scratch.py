#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء قاعدة البيانات من الصفر
Create Database From Scratch
"""

import sqlite3
import os
import bcrypt
from datetime import datetime

def create_database_structure():
    """إنشاء بنية قاعدة البيانات الكاملة"""
    print("🔧 إنشاء قاعدة البيانات من الصفر...")
    
    # إنشاء مجلد البيانات إذا لم يكن موجود
    if not os.path.exists('data'):
        os.makedirs('data')
        print("✅ تم إنشاء مجلد data")
    
    # حذف قاعدة البيانات القديمة إذا كانت موجودة
    db_path = 'data/rafea_system.db'
    if os.path.exists(db_path):
        os.remove(db_path)
        print("✅ تم حذف قاعدة البيانات القديمة")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # تعطيل المفاتيح الخارجية مؤقتاً
        cursor.execute("PRAGMA foreign_keys = OFF")
        
        # إنشاء جدول المستخدمين
        cursor.execute('''
            CREATE TABLE users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                email TEXT,
                user_type TEXT NOT NULL CHECK (user_type IN ('admin', 'accountant', 'engineer', 'sales', 'maintenance')),
                is_active INTEGER DEFAULT 1,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✅ تم إنشاء جدول المستخدمين")
        
        # إنشاء جدول المشاريع
        cursor.execute('''
            CREATE TABLE projects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                location TEXT,
                start_date DATE,
                end_date DATE,
                budget REAL,
                status TEXT DEFAULT 'active',
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✅ تم إنشاء جدول المشاريع")
        
        # إنشاء جدول العملاء
        cursor.execute('''
            CREATE TABLE customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                national_id TEXT,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✅ تم إنشاء جدول العملاء")
        
        # إنشاء جدول الوحدات
        cursor.execute('''
            CREATE TABLE units (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER,
                unit_number TEXT NOT NULL,
                unit_type TEXT,
                area REAL,
                price REAL,
                status TEXT DEFAULT 'available',
                description TEXT,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✅ تم إنشاء جدول الوحدات")
        
        # إنشاء جدول المقاولين
        cursor.execute('''
            CREATE TABLE contractors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                license_number TEXT,
                specialization TEXT,
                rating REAL DEFAULT 0.0,
                notes TEXT,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✅ تم إنشاء جدول المقاولين")
        
        # إنشاء جدول الموردين
        cursor.execute('''
            CREATE TABLE suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                contact_person TEXT,
                tax_number TEXT,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✅ تم إنشاء جدول الموردين")
        
        # إنشاء جدول المستخلصات
        cursor.execute('''
            CREATE TABLE extracts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                extract_number TEXT NOT NULL,
                contractor TEXT NOT NULL,
                project INTEGER DEFAULT 1,
                total_amount REAL NOT NULL,
                extract_date DATE,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'paid', 'rejected')),
                description TEXT,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✅ تم إنشاء جدول المستخلصات")
        
        # إنشاء جدول الفواتير
        cursor.execute('''
            CREATE TABLE invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT NOT NULL,
                supplier TEXT NOT NULL,
                total_amount REAL NOT NULL,
                invoice_date DATE,
                due_date DATE,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'paid', 'rejected')),
                description TEXT,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✅ تم إنشاء جدول الفواتير")
        
        # إنشاء جدول طلبات الشراء
        cursor.execute('''
            CREATE TABLE purchase_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                request_number TEXT NOT NULL,
                project INTEGER DEFAULT 1,
                materials TEXT NOT NULL,
                quantity REAL NOT NULL,
                estimated_cost REAL,
                request_date DATE,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'completed')),
                notes TEXT,
                created_by INTEGER DEFAULT 1
            )
        ''')
        print("✅ تم إنشاء جدول طلبات الشراء")
        
        # إنشاء جدول مهام الصيانة
        cursor.execute('''
            CREATE TABLE maintenance_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_number TEXT NOT NULL,
                task_title TEXT,
                maintenance_type TEXT,
                location TEXT,
                technician TEXT,
                date DATE,
                cost REAL DEFAULT 0,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')),
                description TEXT,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✅ تم إنشاء جدول مهام الصيانة")
        
        # إنشاء جدول المهام اليومية
        cursor.execute('''
            CREATE TABLE daily_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_title TEXT NOT NULL,
                description TEXT,
                assigned_to TEXT,
                priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
                due_date DATE,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'postponed')),
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✅ تم إنشاء جدول المهام اليومية")
        
        # إنشاء مستخدم افتراضي (admin)
        password = "admin123"
        password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        cursor.execute('''
            INSERT INTO users (username, password_hash, full_name, user_type, is_active)
            VALUES (?, ?, ?, ?, ?)
        ''', ('admin', password_hash, 'المدير العام', 'admin', 1))
        print("✅ تم إنشاء المستخدم الافتراضي (admin/admin123)")
        
        # إنشاء مشروع افتراضي
        cursor.execute('''
            INSERT INTO projects (name, description, location, status)
            VALUES (?, ?, ?, ?)
        ''', ('المشروع الافتراضي', 'مشروع تجريبي للاختبار', 'الرياض', 'active'))
        print("✅ تم إنشاء مشروع افتراضي")
        
        # إنشاء بيانات تجريبية
        cursor.execute('''
            INSERT INTO customers (name, phone, email, address)
            VALUES (?, ?, ?, ?)
        ''', ('عميل تجريبي', '0501234567', '<EMAIL>', 'الرياض'))
        
        cursor.execute('''
            INSERT INTO contractors (name, phone, specialization)
            VALUES (?, ?, ?)
        ''', ('مقاول تجريبي', '0507654321', 'أعمال البناء'))
        
        cursor.execute('''
            INSERT INTO suppliers (name, phone, contact_person)
            VALUES (?, ?, ?)
        ''', ('مورد تجريبي', '0509876543', 'مدير المبيعات'))
        
        print("✅ تم إنشاء بيانات تجريبية")
        
        # تفعيل المفاتيح الخارجية
        cursor.execute("PRAGMA foreign_keys = ON")
        
        conn.commit()
        conn.close()
        
        print("🎉 تم إنشاء قاعدة البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
        return False

def verify_database():
    """التحقق من قاعدة البيانات"""
    print("🔍 التحقق من قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('data/rafea_system.db')
        cursor = conn.cursor()
        
        # عرض الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"✅ تم إنشاء {len(tables)} جدول:")
        for table in tables:
            print(f"   - {table[0]}")
        
        # عرض عدد المستخدمين
        cursor.execute("SELECT COUNT(*) FROM users")
        users_count = cursor.fetchone()[0]
        print(f"✅ عدد المستخدمين: {users_count}")
        
        # عرض المستخدم الافتراضي
        cursor.execute("SELECT username, full_name, user_type FROM users WHERE id = 1")
        admin_user = cursor.fetchone()
        if admin_user:
            print(f"✅ المستخدم الافتراضي: {admin_user[0]} ({admin_user[1]}) - {admin_user[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق من قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🔧 إنشاء قاعدة البيانات من الصفر")
    print("=" * 70)
    
    # إنشاء قاعدة البيانات
    if create_database_structure():
        # التحقق من قاعدة البيانات
        if verify_database():
            print("\n🎉 تم إنشاء قاعدة البيانات بنجاح!")
            print("يمكنك الآن تشغيل النظام بدون أخطاء.")
            print("\nبيانات تسجيل الدخول:")
            print("اسم المستخدم: admin")
            print("كلمة المرور: admin123")
        else:
            print("\n❌ فشل في التحقق من قاعدة البيانات")
    else:
        print("\n❌ فشل في إنشاء قاعدة البيانات")

if __name__ == "__main__":
    main()
