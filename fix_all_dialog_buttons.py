#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لجميع أزرار النوافذ
Complete Fix for All Dialog Buttons
"""

import re

def fix_all_dialog_sizes():
    """إصلاح أحجام جميع النوافذ لضمان ظهور الأزرار"""
    
    print("🔧 إصلاح أحجام جميع النوافذ...")
    
    try:
        # قراءة الملف
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح أحجام النوافذ
        size_fixes = [
            # نافذة المستخدمين
            ('self.dialog.geometry("500x500")', 'self.dialog.geometry("500x650")'),
            
            # نافذة المشاريع
            ('self.dialog.geometry("500x600")', 'self.dialog.geometry("500x700")'),
            
            # نافذة العملاء - تم إصلاحها بالفعل
            
            # نافذة الوحدات
            ('self.dialog.geometry("500x600")', 'self.dialog.geometry("500x700")'),
            
            # نافذة المقاولين
            ('self.dialog.geometry("500x600")', 'self.dialog.geometry("500x700")'),
            
            # نافذة الموردين
            ('self.dialog.geometry("500x600")', 'self.dialog.geometry("500x700")'),
            
            # نافذة المستخلصات
            ('self.dialog.geometry("500x650")', 'self.dialog.geometry("500x750")'),
            
            # نافذة الفواتير
            ('self.dialog.geometry("500x600")', 'self.dialog.geometry("500x700")'),
        ]
        
        for old_size, new_size in size_fixes:
            content = content.replace(old_size, new_size)
        
        # إصلاح إطارات المحتوى لترك مساحة للأزرار
        content_frame_fixes = [
            ('pady=(0, 100)', 'pady=(0, 120)'),
            ('pady=(0, 120)', 'pady=(0, 130)'),  # للنوافذ الأطول
        ]
        
        for old_padding, new_padding in content_frame_fixes:
            content = content.replace(old_padding, new_padding)
        
        # إصلاح ارتفاع إطار الأزرار
        content = content.replace('height=80', 'height=100')
        
        # إصلاح توسيط النوافذ
        geometry_updates = [
            ('y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)', 
             'y = (self.dialog.winfo_screenheight() // 2) - (650 // 2)'),
            ('y = (self.dialog.winfo_screenheight() // 2) - (550 // 2)', 
             'y = (self.dialog.winfo_screenheight() // 2) - (700 // 2)'),
            ('y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)', 
             'y = (self.dialog.winfo_screenheight() // 2) - (700 // 2)'),
            ('y = (self.dialog.winfo_screenheight() // 2) - (650 // 2)', 
             'y = (self.dialog.winfo_screenheight() // 2) - (750 // 2)'),
            ('y = (self.dialog.winfo_screenheight() // 2) - (700 // 2)', 
             'y = (self.dialog.winfo_screenheight() // 2) - (700 // 2)'),  # لا تغيير
        ]
        
        for old_center, new_center in geometry_updates:
            content = content.replace(old_center, new_center)
        
        # إصلاح geometry calls النهائية
        final_geometry_fixes = [
            ('self.dialog.geometry(f"500x500+{x}+{y}")', 'self.dialog.geometry(f"500x650+{x}+{y}")'),
            ('self.dialog.geometry(f"500x550+{x}+{y}")', 'self.dialog.geometry(f"500x700+{x}+{y}")'),
            ('self.dialog.geometry(f"500x600+{x}+{y}")', 'self.dialog.geometry(f"500x700+{x}+{y}")'),
            ('self.dialog.geometry(f"500x650+{x}+{y}")', 'self.dialog.geometry(f"500x750+{x}+{y}")'),
        ]
        
        for old_geo, new_geo in final_geometry_fixes:
            content = content.replace(old_geo, new_geo)
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح أحجام جميع النوافذ!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {e}")
        return False

def add_button_visibility_fix():
    """إضافة إصلاح لضمان ظهور الأزرار"""
    
    print("🔍 إضافة إصلاح ظهور الأزرار...")
    
    try:
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة كود إضافي لضمان ظهور الأزرار
        button_visibility_code = '''
        # إجبار ظهور الأزرار
        self.dialog.update_idletasks()
        buttons_frame.update_idletasks()
        save_btn.update_idletasks()
        cancel_btn.update_idletasks()
        
        # التأكد من أن الأزرار في المقدمة
        buttons_frame.lift()
        save_btn.lift()
        cancel_btn.lift()'''
        
        # البحث عن مكان إدراج الكود
        pattern = r'(print\("✅ تم إنشاء أزرار [^"]+!"\))'
        replacement = r'\1' + button_visibility_code
        
        content = re.sub(pattern, replacement, content)
        
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة إصلاح ظهور الأزرار!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الإصلاح: {e}")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🔧 إصلاح شامل لجميع أزرار النوافذ")
    print("=" * 70)
    
    success1 = fix_all_dialog_sizes()
    success2 = add_button_visibility_fix()
    
    if success1 and success2:
        print("\n🎉 تم الإصلاح الشامل بنجاح!")
        print("الآن ستظهر الأزرار بوضوح في جميع النوافذ.")
        print("جميع النوافذ أصبحت أطول مع مساحة كافية للأزرار.")
    else:
        print("\n❌ فشل في الإصلاح!")
    
    input("\nاضغط Enter للخروج...")
