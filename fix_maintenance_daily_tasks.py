#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل الصيانة والمهام اليومية
Fix Maintenance and Daily Tasks Issues
"""

def fix_maintenance_priority_values():
    """إصلاح قيم الأولوية في الصيانة"""
    print("🔧 إصلاح قيم الأولوية في الصيانة...")
    
    try:
        # قراءة ملف النوافذ
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح قيم الأولوية لتطابق قاعدة البيانات
        content = content.replace(
            "self.priority_combo['values'] = ('low', 'medium', 'high', 'urgent')",
            "self.priority_combo['values'] = ('low', 'medium', 'high', 'urgent')"
        )
        content = content.replace(
            "self.priority_combo.set('medium')",
            "self.priority_combo.set('medium')"
        )
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح قيم الأولوية في الصيانة!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قيم الأولوية: {e}")
        return False

def fix_daily_tasks_priority_values():
    """إصلاح قيم الأولوية في المهام اليومية"""
    print("🔧 إصلاح قيم الأولوية في المهام اليومية...")
    
    try:
        # قراءة ملف النوافذ
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التأكد من أن قيم الأولوية صحيحة
        # (تم إصلاحها بالفعل في الإصلاحات السابقة)
        
        print("✅ قيم الأولوية في المهام اليومية صحيحة!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قيم الأولوية: {e}")
        return False

def add_default_values_to_maintenance():
    """إضافة قيم افتراضية لنافذة الصيانة"""
    print("🔧 إضافة قيم افتراضية لنافذة الصيانة...")
    
    try:
        # قراءة ملف النوافذ
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة قيم افتراضية للصيانة إذا لم تكن موجودة
        if 'self.assigned_to_entry.insert(0, "فريق الصيانة")' not in content:
            content = content.replace(
                'self.assigned_to_entry.pack(pady=(0, 15), padx=20)',
                '''self.assigned_to_entry.pack(pady=(0, 15), padx=20)
        # قيمة افتراضية
        if not self.task_data:
            self.assigned_to_entry.insert(0, "فريق الصيانة")'''
            )
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة قيم افتراضية لنافذة الصيانة!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة قيم افتراضية: {e}")
        return False

def add_default_values_to_daily_tasks():
    """إضافة قيم افتراضية لنافذة المهام اليومية"""
    print("🔧 إضافة قيم افتراضية لنافذة المهام اليومية...")
    
    try:
        # قراءة ملف النوافذ
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة تاريخ استحقاق افتراضي
        if 'datetime.now().strftime("%Y-%m-%d")' not in content or 'المهام اليومية' not in content:
            # البحث عن نافذة المهام اليومية وإضافة التاريخ
            content = content.replace(
                '# تعبئة بيانات تجريبية لتسهيل الاختبار',
                '''# تعبئة بيانات تجريبية لتسهيل الاختبار
        # إضافة تاريخ استحقاق افتراضي للمهام اليومية
        if "المهام اليومية" in title and not self.task_data:
            from datetime import datetime, timedelta
            due_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")'''
            )
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة قيم افتراضية لنافذة المهام اليومية!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة قيم افتراضية: {e}")
        return False

def test_maintenance_and_daily_tasks():
    """اختبار الصيانة والمهام اليومية"""
    print("🧪 اختبار الصيانة والمهام اليومية...")
    
    try:
        import tkinter as tk
        from dialogs import MaintenanceTaskDialog, DailyTaskDialog
        
        root = tk.Tk()
        root.withdraw()
        
        # اختبار نافذة الصيانة
        maintenance_dialog = MaintenanceTaskDialog(root, "اختبار الصيانة")
        maintenance_dialog.dialog.destroy()
        print("✅ نافذة الصيانة تعمل بشكل صحيح")
        
        # اختبار نافذة المهام اليومية
        daily_dialog = DailyTaskDialog(root, "اختبار المهام اليومية")
        daily_dialog.dialog.destroy()
        print("✅ نافذة المهام اليومية تعمل بشكل صحيح")
        
        root.destroy()
        
        print("✅ جميع النوافذ تعمل بشكل صحيح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النوافذ: {e}")
        return False

def create_specific_test():
    """إنشاء اختبار مخصص للصيانة والمهام اليومية"""
    
    test_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مخصص للصيانة والمهام اليومية
"""

import tkinter as tk
from dialogs import MaintenanceTaskDialog, DailyTaskDialog

def test_maintenance_dialog():
    """اختبار نافذة الصيانة"""
    print("🔧 اختبار نافذة الصيانة...")
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        dialog = MaintenanceTaskDialog(root, "اختبار الصيانة")
        
        # ملء البيانات تلقائياً
        dialog.task_number_entry.insert(0, "MAIN-TEST-001")
        dialog.description_text.insert('1.0', "اختبار مهمة صيانة")
        dialog.assigned_to_entry.insert(0, "فريق الاختبار")
        dialog.priority_combo.set('medium')
        dialog.status_combo.set('pending')
        
        # محاكاة الحفظ
        dialog.save_task()
        
        result = dialog.result
        if result:
            print("✅ نافذة الصيانة تعمل وتحفظ البيانات بنجاح!")
            print(f"   البيانات المحفوظة: {len(result)} حقل")
        else:
            print("❌ فشل في حفظ بيانات الصيانة")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة الصيانة: {e}")
    finally:
        root.destroy()

def test_daily_tasks_dialog():
    """اختبار نافذة المهام اليومية"""
    print("📅 اختبار نافذة المهام اليومية...")
    
    root = tk.Tk()
    root.withdraw()
    
    try:
        dialog = DailyTaskDialog(root, "اختبار المهام اليومية")
        
        # ملء البيانات تلقائياً
        dialog.title_entry.insert(0, "مهمة اختبار يومية")
        dialog.description_text.insert('1.0', "وصف مهمة الاختبار")
        dialog.priority_combo.set('medium')
        dialog.status_combo.set('pending')
        
        # محاكاة الحفظ
        dialog.save_task()
        
        result = dialog.result
        if result:
            print("✅ نافذة المهام اليومية تعمل وتحفظ البيانات بنجاح!")
            print(f"   البيانات المحفوظة: {len(result)} حقل")
        else:
            print("❌ فشل في حفظ بيانات المهمة اليومية")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة المهام اليومية: {e}")
    finally:
        root.destroy()

def main():
    """الاختبار الرئيسي"""
    print("=" * 60)
    print("🧪 اختبار مخصص للصيانة والمهام اليومية")
    print("=" * 60)
    
    test_maintenance_dialog()
    print()
    test_daily_tasks_dialog()
    
    print("\\n🎉 انتهى الاختبار!")

if __name__ == "__main__":
    main()
'''
    
    try:
        with open('test_maintenance_daily.py', 'w', encoding='utf-8') as f:
            f.write(test_content)
        print("✅ تم إنشاء اختبار مخصص: test_maintenance_daily.py")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء الاختبار: {e}")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🔧 إصلاح مشاكل الصيانة والمهام اليومية")
    print("=" * 70)
    
    # تشغيل جميع الإصلاحات
    fixes = [
        ("إصلاح قيم الأولوية في الصيانة", fix_maintenance_priority_values),
        ("إصلاح قيم الأولوية في المهام اليومية", fix_daily_tasks_priority_values),
        ("إضافة قيم افتراضية للصيانة", add_default_values_to_maintenance),
        ("إضافة قيم افتراضية للمهام اليومية", add_default_values_to_daily_tasks),
        ("اختبار النوافذ", test_maintenance_and_daily_tasks),
        ("إنشاء اختبار مخصص", create_specific_test),
    ]
    
    success_count = 0
    for name, fix_func in fixes:
        print(f"\\n🔧 {name}...")
        if fix_func():
            success_count += 1
    
    print(f"\\n📊 النتيجة النهائية: {success_count}/{len(fixes)} إصلاحات نجحت")
    
    if success_count == len(fixes):
        print("\\n🎉 تم إصلاح جميع مشاكل الصيانة والمهام اليومية بنجاح!")
        print("يمكنك الآن تشغيل النظام واختبار هذه الوحدات.")
    else:
        print("\\n⚠️ بعض الإصلاحات فشلت. راجع الأخطاء أعلاه.")
    
    input("\\nاضغط Enter للخروج...")
