#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نوافذ الحوار المصلحة مع أزرار مضمونة
Fixed Dialog Windows with Guaranteed Buttons
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import bcrypt

class UserDialog:
    """نافذة حوار إضافة/تعديل مستخدم - مصلحة"""
    
    def __init__(self, parent, title, user_data=None):
        self.parent = parent
        self.user_data = user_data
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x750")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (750 // 2)
        self.dialog.geometry(f"500x750+{x}+{y}")
        
        self.create_widgets()
        
        if user_data:
            self.load_user_data()
    
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        
        # العنوان
        title_label = tk.Label(
            self.dialog,
            text="إدارة المستخدمين",
            font=('Arial', 18, 'bold'),
            bg='#f5f5f5',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # إطار المحتوى
        content_frame = tk.Frame(self.dialog, bg='white', relief='raised', bd=2)
        content_frame.pack(fill='both', expand=True, padx=20, pady=(0, 130))
        
        # اسم المستخدم
        tk.Label(content_frame, text="اسم المستخدم:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(20, 5), padx=20)
        self.username_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.username_entry.pack(pady=(0, 15), padx=20)
        
        # كلمة المرور
        tk.Label(content_frame, text="كلمة المرور:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.password_entry = tk.Entry(content_frame, font=('Arial', 12), width=40, show='*')
        self.password_entry.pack(pady=(0, 15), padx=20)
        
        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.email_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.email_entry.pack(pady=(0, 15), padx=20)
        
        # نوع المستخدم
        tk.Label(content_frame, text="نوع المستخدم:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.user_type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.user_type_combo['values'] = ('admin', 'manager', 'employee', 'viewer')
        self.user_type_combo.set('employee')
        self.user_type_combo.pack(pady=(0, 15), padx=20)
        
        # الحالة
        self.is_active_var = tk.BooleanVar(value=True)
        self.is_active_check = tk.Checkbutton(
            content_frame,
            text="مستخدم مفعل",
            variable=self.is_active_var,
            font=('Arial', 12),
            bg='white'
        )
        self.is_active_check.pack(anchor='e', pady=(0, 20), padx=20)
        
        # إطار الأزرار - الجزء المهم
        buttons_frame = tk.Frame(self.dialog, bg='#f5f5f5', height=100)
        buttons_frame.pack(fill='x', side='bottom', padx=20, pady=20)
        buttons_frame.pack_propagate(False)
        
        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ المستخدم",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.save_user
        )
        save_btn.pack(side='right', padx=(10, 0), pady=10)
        
        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.cancel
        )
        cancel_btn.pack(side='right', padx=(10, 10), pady=10)
        
        # التأكد من ظهور الأزرار
        self.dialog.update()
        buttons_frame.update()
        save_btn.update()
        cancel_btn.update()
        
        print("✅ تم إنشاء أزرار المستخدمين بنجاح!")
        # إجبار ظهور الأزرار
        self.dialog.update_idletasks()
        buttons_frame.update_idletasks()
        save_btn.update_idletasks()
        cancel_btn.update_idletasks()
        
        # التأكد من أن الأزرار في المقدمة
        buttons_frame.lift()
        save_btn.lift()
        cancel_btn.lift()
    
    def load_user_data(self):
        """تحميل بيانات المستخدم للتعديل"""
        if self.user_data:
            self.username_entry.insert(0, self.user_data.get('username', ''))
            self.email_entry.insert(0, self.user_data.get('email', ''))
            self.user_type_combo.set(self.user_data.get('user_type', 'employee'))
            self.is_active_var.set(self.user_data.get('is_active', True))
    
    def save_user(self):
        """حفظ بيانات المستخدم"""
        # التحقق من صحة البيانات
        if not self.username_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
            return
        
        if not self.password_entry.get().strip() and not self.user_data:
            messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
            return
        
        try:
            # تحضير البيانات
            user_data = {
                'username': self.username_entry.get().strip(),
                'email': self.email_entry.get().strip(),
                'user_type': self.user_type_combo.get(),
                'is_active': self.is_active_var.get()
            }
            
            # تشفير كلمة المرور إذا تم إدخالها
            password = self.password_entry.get().strip()
            if password:
                user_data['password_hash'] = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            self.result = user_data
            messagebox.showinfo("نجح", "تم حفظ بيانات المستخدم بنجاح!")
            self.dialog.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

class ProjectDialog:
    """نافذة حوار إضافة/تعديل مشروع - مصلحة"""
    
    def __init__(self, parent, title, project_data=None):
        self.parent = parent
        self.project_data = project_data
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x700")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (700 // 2)
        self.dialog.geometry(f"500x700+{x}+{y}")
        
        self.create_widgets()
        
        if project_data:
            self.load_project_data()
    
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        
        # العنوان
        title_label = tk.Label(
            self.dialog,
            text="إدارة المشاريع",
            font=('Arial', 18, 'bold'),
            bg='#f5f5f5',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # إطار المحتوى
        content_frame = tk.Frame(self.dialog, bg='white', relief='raised', bd=2)
        content_frame.pack(fill='both', expand=True, padx=20, pady=(0, 130))
        
        # اسم المشروع
        tk.Label(content_frame, text="اسم المشروع:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(20, 5), padx=20)
        self.name_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.name_entry.pack(pady=(0, 15), padx=20)
        
        # الموقع
        tk.Label(content_frame, text="الموقع:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.location_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.location_entry.pack(pady=(0, 15), padx=20)
        
        # نوع المشروع
        tk.Label(content_frame, text="نوع المشروع:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.type_combo['values'] = ('سكني', 'تجاري', 'إداري', 'مختلط')
        self.type_combo.set('سكني')
        self.type_combo.pack(pady=(0, 15), padx=20)
        
        # التكلفة الإجمالية
        tk.Label(content_frame, text="التكلفة الإجمالية:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.cost_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.cost_entry.pack(pady=(0, 20), padx=20)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.dialog, bg='#f5f5f5', height=100)
        buttons_frame.pack(fill='x', side='bottom', padx=20, pady=20)
        buttons_frame.pack_propagate(False)
        
        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ المشروع",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.save_project
        )
        save_btn.pack(side='right', padx=(10, 0), pady=10)
        
        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.cancel
        )
        cancel_btn.pack(side='right', padx=(10, 10), pady=10)
        
        # التأكد من ظهور الأزرار
        self.dialog.update()
        buttons_frame.update()
        save_btn.update()
        cancel_btn.update()
        
        print("✅ تم إنشاء أزرار المشاريع بنجاح!")
        # إجبار ظهور الأزرار
        self.dialog.update_idletasks()
        buttons_frame.update_idletasks()
        save_btn.update_idletasks()
        cancel_btn.update_idletasks()
        
        # التأكد من أن الأزرار في المقدمة
        buttons_frame.lift()
        save_btn.lift()
        cancel_btn.lift()
    
    def load_project_data(self):
        """تحميل بيانات المشروع للتعديل"""
        if self.project_data:
            self.name_entry.insert(0, self.project_data.get('name', ''))
            self.location_entry.insert(0, self.project_data.get('location', ''))
            self.type_combo.set(self.project_data.get('project_type', 'سكني'))
            self.cost_entry.insert(0, str(self.project_data.get('total_cost', '')))
    
    def save_project(self):
        """حفظ بيانات المشروع"""
        # التحقق من صحة البيانات
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المشروع")
            return
        
        try:
            # تحضير البيانات
            project_data = {
                'name': self.name_entry.get().strip(),
                'location': self.location_entry.get().strip(),
                'project_type': self.type_combo.get(),
                'total_cost': float(self.cost_entry.get()) if self.cost_entry.get().strip() else 0.0,
                'start_date': datetime.now().strftime("%Y-%m-%d"),
                'status': 'تخطيط'
            }
            
            self.result = project_data
            messagebox.showinfo("نجح", "تم حفظ بيانات المشروع بنجاح!")
            self.dialog.destroy()
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيمة صحيحة للتكلفة")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

class CustomerDialog:
    """نافذة حوار إضافة/تعديل عميل - مصلحة"""

    def __init__(self, parent, title, customer_data=None):
        self.parent = parent
        self.customer_data = customer_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x700")  # زيادة الارتفاع
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (700 // 2)
        self.dialog.geometry(f"500x700+{x}+{y}")

        self.create_widgets()

        if customer_data:
            self.load_customer_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""

        # العنوان
        title_label = tk.Label(
            self.dialog,
            text="إدارة العملاء",
            font=('Arial', 18, 'bold'),
            bg='#f5f5f5',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # إطار المحتوى
        content_frame = tk.Frame(self.dialog, bg='white', relief='raised', bd=2)
        content_frame.pack(fill='both', expand=True, padx=20, pady=(0, 130))

        # الاسم الكامل
        tk.Label(content_frame, text="الاسم الكامل:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(20, 5), padx=20)
        self.name_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.name_entry.pack(pady=(0, 15), padx=20)

        # رقم الهوية
        tk.Label(content_frame, text="رقم الهوية:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.national_id_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.national_id_entry.pack(pady=(0, 15), padx=20)

        # رقم الهاتف
        tk.Label(content_frame, text="رقم الهاتف:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.phone_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.phone_entry.pack(pady=(0, 15), padx=20)

        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.email_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.email_entry.pack(pady=(0, 15), padx=20)

        # نوع العميل
        tk.Label(content_frame, text="نوع العميل:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.customer_type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.customer_type_combo['values'] = ('فرد', 'شركة')
        self.customer_type_combo.set('فرد')
        self.customer_type_combo.pack(pady=(0, 20), padx=20)

        # إطار الأزرار
        buttons_frame = tk.Frame(self.dialog, bg='#f5f5f5', height=100)
        buttons_frame.pack(fill='x', side='bottom', padx=20, pady=20)
        buttons_frame.pack_propagate(False)

        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ العميل",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.save_customer
        )
        save_btn.pack(side='right', padx=(10, 0), pady=10)

        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.cancel
        )
        cancel_btn.pack(side='right', padx=(10, 10), pady=10)

        # التأكد من ظهور الأزرار
        self.dialog.update()
        buttons_frame.update()
        save_btn.update()
        cancel_btn.update()

        print("✅ تم إنشاء أزرار العملاء بنجاح!")
        # إجبار ظهور الأزرار
        self.dialog.update_idletasks()
        buttons_frame.update_idletasks()
        save_btn.update_idletasks()
        cancel_btn.update_idletasks()
        
        # التأكد من أن الأزرار في المقدمة
        buttons_frame.lift()
        save_btn.lift()
        cancel_btn.lift()

    def load_customer_data(self):
        """تحميل بيانات العميل للتعديل"""
        if self.customer_data:
            self.name_entry.insert(0, self.customer_data.get('full_name', ''))
            self.national_id_entry.insert(0, self.customer_data.get('national_id', ''))
            self.phone_entry.insert(0, self.customer_data.get('phone', ''))
            self.email_entry.insert(0, self.customer_data.get('email', ''))
            customer_type = 'شركة' if self.customer_data.get('customer_type') == 'company' else 'فرد'
            self.customer_type_combo.set(customer_type)

    def save_customer(self):
        """حفظ بيانات العميل"""
        # التحقق من صحة البيانات
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
            return

        try:
            # تحويل نوع العميل
            customer_type = 'company' if self.customer_type_combo.get() == 'شركة' else 'individual'

            # تحضير البيانات
            customer_data = {
                'full_name': self.name_entry.get().strip(),
                'national_id': self.national_id_entry.get().strip(),
                'phone': self.phone_entry.get().strip(),
                'email': self.email_entry.get().strip(),
                'customer_type': customer_type,
                'registration_date': datetime.now().strftime("%Y-%m-%d")
            }

            self.result = customer_data
            messagebox.showinfo("نجح", "تم حفظ بيانات العميل بنجاح!")
            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

class UnitDialog:
    """نافذة حوار إضافة/تعديل وحدة - مصلحة"""

    def __init__(self, parent, title, unit_data=None):
        self.parent = parent
        self.unit_data = unit_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x700")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (700 // 2)
        self.dialog.geometry(f"500x700+{x}+{y}")

        self.create_widgets()

        if unit_data:
            self.load_unit_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""

        # العنوان
        title_label = tk.Label(
            self.dialog,
            text="إدارة الوحدات",
            font=('Arial', 18, 'bold'),
            bg='#f5f5f5',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # إطار المحتوى
        content_frame = tk.Frame(self.dialog, bg='white', relief='raised', bd=2)
        content_frame.pack(fill='both', expand=True, padx=20, pady=(0, 130))

        # رقم الوحدة
        tk.Label(content_frame, text="رقم الوحدة:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(20, 5), padx=20)
        self.unit_number_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.unit_number_entry.pack(pady=(0, 15), padx=20)

        # رقم الطابق
        tk.Label(content_frame, text="رقم الطابق:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.floor_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.floor_entry.pack(pady=(0, 15), padx=20)

        # نوع الوحدة
        tk.Label(content_frame, text="نوع الوحدة:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.unit_type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.unit_type_combo['values'] = ('شقة', 'فيلا', 'محل تجاري', 'مكتب', 'مستودع')
        self.unit_type_combo.set('شقة')
        self.unit_type_combo.pack(pady=(0, 15), padx=20)

        # المساحة
        tk.Label(content_frame, text="المساحة (متر مربع):", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.area_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.area_entry.pack(pady=(0, 15), padx=20)

        # السعر
        tk.Label(content_frame, text="السعر:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.price_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.price_entry.pack(pady=(0, 20), padx=20)

        # إطار الأزرار
        buttons_frame = tk.Frame(self.dialog, bg='#f5f5f5', height=100)
        buttons_frame.pack(fill='x', side='bottom', padx=20, pady=20)
        buttons_frame.pack_propagate(False)

        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ الوحدة",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.save_unit
        )
        save_btn.pack(side='right', padx=(10, 0), pady=10)

        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.cancel
        )
        cancel_btn.pack(side='right', padx=(10, 10), pady=10)

        # التأكد من ظهور الأزرار
        self.dialog.update()
        buttons_frame.update()
        save_btn.update()
        cancel_btn.update()

        print("✅ تم إنشاء أزرار الوحدات بنجاح!")
        # إجبار ظهور الأزرار
        self.dialog.update_idletasks()
        buttons_frame.update_idletasks()
        save_btn.update_idletasks()
        cancel_btn.update_idletasks()
        
        # التأكد من أن الأزرار في المقدمة
        buttons_frame.lift()
        save_btn.lift()
        cancel_btn.lift()

    def load_unit_data(self):
        """تحميل بيانات الوحدة للتعديل"""
        if self.unit_data:
            self.unit_number_entry.insert(0, self.unit_data.get('unit_number', ''))
            self.floor_entry.insert(0, str(self.unit_data.get('floor_number', '')))
            self.unit_type_combo.set(self.unit_data.get('unit_type', 'شقة'))
            self.area_entry.insert(0, str(self.unit_data.get('area', '')))
            self.price_entry.insert(0, str(self.unit_data.get('price', '')))

    def save_unit(self):
        """حفظ بيانات الوحدة"""
        # التحقق من صحة البيانات
        if not self.unit_number_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال رقم الوحدة")
            return

        try:
            # تحضير البيانات
            unit_data = {
                'unit_number': self.unit_number_entry.get().strip(),
                'floor_number': int(self.floor_entry.get()) if self.floor_entry.get().strip() else 1,
                'unit_type': self.unit_type_combo.get(),
                'area': float(self.area_entry.get()) if self.area_entry.get().strip() else 0.0,
                'price': float(self.price_entry.get()) if self.price_entry.get().strip() else 0.0,
                'status': 'available',
                'project_id': 1  # افتراضي
            }

            self.result = unit_data
            messagebox.showinfo("نجح", "تم حفظ بيانات الوحدة بنجاح!")
            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للأرقام")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

class ContractorDialog:
    """نافذة حوار إضافة/تعديل مقاول - مصلحة"""

    def __init__(self, parent, title, contractor_data=None):
        self.parent = parent
        self.contractor_data = contractor_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x700")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (700 // 2)
        self.dialog.geometry(f"500x700+{x}+{y}")

        self.create_widgets()

        if contractor_data:
            self.load_contractor_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""

        # العنوان
        title_label = tk.Label(
            self.dialog,
            text="إدارة المقاولين",
            font=('Arial', 18, 'bold'),
            bg='#f5f5f5',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # إطار المحتوى
        content_frame = tk.Frame(self.dialog, bg='white', relief='raised', bd=2)
        content_frame.pack(fill='both', expand=True, padx=20, pady=(0, 130))

        # اسم المقاول
        tk.Label(content_frame, text="اسم المقاول:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(20, 5), padx=20)
        self.name_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.name_entry.pack(pady=(0, 15), padx=20)

        # رقم الهاتف
        tk.Label(content_frame, text="رقم الهاتف:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.phone_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.phone_entry.pack(pady=(0, 15), padx=20)

        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.email_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.email_entry.pack(pady=(0, 15), padx=20)

        # التخصص
        tk.Label(content_frame, text="التخصص:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.specialty_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.specialty_combo['values'] = ('بناء', 'كهرباء', 'سباكة', 'تشطيبات', 'حدادة', 'نجارة', 'دهانات')
        self.specialty_combo.set('بناء')
        self.specialty_combo.pack(pady=(0, 15), padx=20)

        # العنوان
        tk.Label(content_frame, text="العنوان:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.address_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.address_entry.pack(pady=(0, 20), padx=20)

        # إطار الأزرار
        buttons_frame = tk.Frame(self.dialog, bg='#f5f5f5', height=100)
        buttons_frame.pack(fill='x', side='bottom', padx=20, pady=20)
        buttons_frame.pack_propagate(False)

        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ المقاول",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.save_contractor
        )
        save_btn.pack(side='right', padx=(10, 0), pady=10)

        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.cancel
        )
        cancel_btn.pack(side='right', padx=(10, 10), pady=10)

        # التأكد من ظهور الأزرار
        self.dialog.update()
        buttons_frame.update()
        save_btn.update()
        cancel_btn.update()

        print("✅ تم إنشاء أزرار المقاولين بنجاح!")
        # إجبار ظهور الأزرار
        self.dialog.update_idletasks()
        buttons_frame.update_idletasks()
        save_btn.update_idletasks()
        cancel_btn.update_idletasks()
        
        # التأكد من أن الأزرار في المقدمة
        buttons_frame.lift()
        save_btn.lift()
        cancel_btn.lift()

    def load_contractor_data(self):
        """تحميل بيانات المقاول للتعديل"""
        if self.contractor_data:
            self.name_entry.insert(0, self.contractor_data.get('name', ''))
            self.phone_entry.insert(0, self.contractor_data.get('phone', ''))
            self.email_entry.insert(0, self.contractor_data.get('email', ''))
            self.specialty_combo.set(self.contractor_data.get('specialty', 'بناء'))
            self.address_entry.insert(0, self.contractor_data.get('address', ''))

    def save_contractor(self):
        """حفظ بيانات المقاول"""
        # التحقق من صحة البيانات
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المقاول")
            return

        try:
            # تحضير البيانات
            contractor_data = {
                'name': self.name_entry.get().strip(),
                'phone': self.phone_entry.get().strip(),
                'email': self.email_entry.get().strip(),
                'specialty': self.specialty_combo.get(),
                'address': self.address_entry.get().strip(),
                'registration_date': datetime.now().strftime("%Y-%m-%d"),
                'status': 'active'
            }

            self.result = contractor_data
            messagebox.showinfo("نجح", "تم حفظ بيانات المقاول بنجاح!")
            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

class SupplierDialog:
    """نافذة حوار إضافة/تعديل مورد - مصلحة"""

    def __init__(self, parent, title, supplier_data=None):
        self.parent = parent
        self.supplier_data = supplier_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x700")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (700 // 2)
        self.dialog.geometry(f"500x700+{x}+{y}")

        self.create_widgets()

        if supplier_data:
            self.load_supplier_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""

        # العنوان
        title_label = tk.Label(
            self.dialog,
            text="إدارة الموردين",
            font=('Arial', 18, 'bold'),
            bg='#f5f5f5',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # إطار المحتوى
        content_frame = tk.Frame(self.dialog, bg='white', relief='raised', bd=2)
        content_frame.pack(fill='both', expand=True, padx=20, pady=(0, 130))

        # اسم المورد
        tk.Label(content_frame, text="اسم المورد:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(20, 5), padx=20)
        self.name_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.name_entry.pack(pady=(0, 15), padx=20)

        # رقم الهاتف
        tk.Label(content_frame, text="رقم الهاتف:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.phone_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.phone_entry.pack(pady=(0, 15), padx=20)

        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.email_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.email_entry.pack(pady=(0, 15), padx=20)

        # نوع المواد
        tk.Label(content_frame, text="نوع المواد:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.material_type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.material_type_combo['values'] = ('مواد بناء', 'كهربائيات', 'سباكة', 'دهانات', 'أدوات', 'معدات', 'أخرى')
        self.material_type_combo.set('مواد بناء')
        self.material_type_combo.pack(pady=(0, 15), padx=20)

        # العنوان
        tk.Label(content_frame, text="العنوان:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.address_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.address_entry.pack(pady=(0, 20), padx=20)

        # إطار الأزرار
        buttons_frame = tk.Frame(self.dialog, bg='#f5f5f5', height=100)
        buttons_frame.pack(fill='x', side='bottom', padx=20, pady=20)
        buttons_frame.pack_propagate(False)

        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ المورد",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.save_supplier
        )
        save_btn.pack(side='right', padx=(10, 0), pady=10)

        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.cancel
        )
        cancel_btn.pack(side='right', padx=(10, 10), pady=10)

        # التأكد من ظهور الأزرار
        self.dialog.update()
        buttons_frame.update()
        save_btn.update()
        cancel_btn.update()

        print("✅ تم إنشاء أزرار الموردين بنجاح!")
        # إجبار ظهور الأزرار
        self.dialog.update_idletasks()
        buttons_frame.update_idletasks()
        save_btn.update_idletasks()
        cancel_btn.update_idletasks()
        
        # التأكد من أن الأزرار في المقدمة
        buttons_frame.lift()
        save_btn.lift()
        cancel_btn.lift()

    def load_supplier_data(self):
        """تحميل بيانات المورد للتعديل"""
        if self.supplier_data:
            self.name_entry.insert(0, self.supplier_data.get('name', ''))
            self.phone_entry.insert(0, self.supplier_data.get('phone', ''))
            self.email_entry.insert(0, self.supplier_data.get('email', ''))
            self.material_type_combo.set(self.supplier_data.get('material_type', 'مواد بناء'))
            self.address_entry.insert(0, self.supplier_data.get('address', ''))

    def save_supplier(self):
        """حفظ بيانات المورد"""
        # التحقق من صحة البيانات
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المورد")
            return

        try:
            # تحضير البيانات
            supplier_data = {
                'name': self.name_entry.get().strip(),
                'phone': self.phone_entry.get().strip(),
                'email': self.email_entry.get().strip(),
                'material_type': self.material_type_combo.get(),
                'address': self.address_entry.get().strip(),
                'registration_date': datetime.now().strftime("%Y-%m-%d"),
                'status': 'active'
            }

            self.result = supplier_data
            messagebox.showinfo("نجح", "تم حفظ بيانات المورد بنجاح!")
            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

class ExtractDialog:
    """نافذة حوار إضافة/تعديل مستخلص - مصلحة"""

    def __init__(self, parent, title, extract_data=None):
        self.parent = parent
        self.extract_data = extract_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x750")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (750 // 2)
        self.dialog.geometry(f"500x750+{x}+{y}")

        self.create_widgets()

        if extract_data:
            self.load_extract_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""

        # العنوان
        title_label = tk.Label(
            self.dialog,
            text="إدارة المستخلصات",
            font=('Arial', 18, 'bold'),
            bg='#f5f5f5',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # إطار المحتوى
        content_frame = tk.Frame(self.dialog, bg='white', relief='raised', bd=2)
        content_frame.pack(fill='both', expand=True, padx=20, pady=(0, 130))

        # رقم المستخلص
        tk.Label(content_frame, text="رقم المستخلص:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(20, 5), padx=20)
        self.extract_number_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.extract_number_entry.pack(pady=(0, 15), padx=20)

        # اسم المقاول
        tk.Label(content_frame, text="اسم المقاول:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.contractor_name_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.contractor_name_entry.pack(pady=(0, 15), padx=20)

        # قيمة المستخلص
        tk.Label(content_frame, text="قيمة المستخلص:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.amount_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.amount_entry.pack(pady=(0, 15), padx=20)

        # تاريخ المستخلص
        tk.Label(content_frame, text="تاريخ المستخلص:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.date_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.date_entry.pack(pady=(0, 15), padx=20)

        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.status_combo['values'] = ('قيد المراجعة', 'معتمد', 'مدفوع', 'مرفوض')
        self.status_combo.set('قيد المراجعة')
        self.status_combo.pack(pady=(0, 15), padx=20)

        # الوصف
        tk.Label(content_frame, text="الوصف:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.description_text = tk.Text(content_frame, font=('Arial', 12), width=40, height=4)
        self.description_text.pack(pady=(0, 20), padx=20)

        # إطار الأزرار
        buttons_frame = tk.Frame(self.dialog, bg='#f5f5f5', height=100)
        buttons_frame.pack(fill='x', side='bottom', padx=20, pady=20)
        buttons_frame.pack_propagate(False)

        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ المستخلص",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.save_extract
        )
        save_btn.pack(side='right', padx=(10, 0), pady=10)

        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.cancel
        )
        cancel_btn.pack(side='right', padx=(10, 10), pady=10)

        # التأكد من ظهور الأزرار
        self.dialog.update()
        buttons_frame.update()
        save_btn.update()
        cancel_btn.update()

        print("✅ تم إنشاء أزرار المستخلصات بنجاح!")
        # إجبار ظهور الأزرار
        self.dialog.update_idletasks()
        buttons_frame.update_idletasks()
        save_btn.update_idletasks()
        cancel_btn.update_idletasks()
        
        # التأكد من أن الأزرار في المقدمة
        buttons_frame.lift()
        save_btn.lift()
        cancel_btn.lift()

        # تعيين التاريخ الحالي
        self.date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))

    def load_extract_data(self):
        """تحميل بيانات المستخلص للتعديل"""
        if self.extract_data:
            self.extract_number_entry.insert(0, self.extract_data.get('extract_number', ''))
            self.contractor_name_entry.insert(0, self.extract_data.get('contractor_name', ''))
            self.amount_entry.insert(0, str(self.extract_data.get('amount', '')))
            self.date_entry.delete(0, tk.END)
            self.date_entry.insert(0, self.extract_data.get('extract_date', ''))
            self.status_combo.set(self.extract_data.get('status', 'قيد المراجعة'))
            self.description_text.delete('1.0', tk.END)
            self.description_text.insert('1.0', self.extract_data.get('description', ''))

    def save_extract(self):
        """حفظ بيانات المستخلص"""
        # التحقق من صحة البيانات
        if not self.extract_number_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال رقم المستخلص")
            return

        if not self.contractor_name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المقاول")
            return

        try:
            # تحضير البيانات
            extract_data = {
                'extract_number': self.extract_number_entry.get().strip(),
                'contractor_name': self.contractor_name_entry.get().strip(),
                'amount': float(self.amount_entry.get()) if self.amount_entry.get().strip() else 0.0,
                'extract_date': self.date_entry.get().strip(),
                'status': self.status_combo.get(),
                'description': self.description_text.get('1.0', tk.END).strip(),
                'created_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            self.result = extract_data
            messagebox.showinfo("نجح", "تم حفظ بيانات المستخلص بنجاح!")
            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيمة صحيحة للمبلغ")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

class InvoiceDialog:
    """نافذة حوار إضافة/تعديل فاتورة - مصلحة"""

    def __init__(self, parent, title, invoice_data=None):
        self.parent = parent
        self.invoice_data = invoice_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x700")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (700 // 2)
        self.dialog.geometry(f"500x700+{x}+{y}")

        self.create_widgets()

        if invoice_data:
            self.load_invoice_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""

        # العنوان
        title_label = tk.Label(
            self.dialog,
            text="إدارة الفواتير",
            font=('Arial', 18, 'bold'),
            bg='#f5f5f5',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # إطار المحتوى
        content_frame = tk.Frame(self.dialog, bg='white', relief='raised', bd=2)
        content_frame.pack(fill='both', expand=True, padx=20, pady=(0, 130))

        # رقم الفاتورة
        tk.Label(content_frame, text="رقم الفاتورة:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(20, 5), padx=20)
        self.invoice_number_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.invoice_number_entry.pack(pady=(0, 15), padx=20)

        # اسم المورد
        tk.Label(content_frame, text="اسم المورد:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.supplier_name_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.supplier_name_entry.pack(pady=(0, 15), padx=20)

        # قيمة الفاتورة
        tk.Label(content_frame, text="قيمة الفاتورة:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.amount_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.amount_entry.pack(pady=(0, 15), padx=20)

        # تاريخ الفاتورة
        tk.Label(content_frame, text="تاريخ الفاتورة:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.date_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.date_entry.pack(pady=(0, 15), padx=20)

        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.status_combo['values'] = ('غير مدفوعة', 'مدفوعة جزئياً', 'مدفوعة بالكامل')
        self.status_combo.set('غير مدفوعة')
        self.status_combo.pack(pady=(0, 20), padx=20)

        # إطار الأزرار
        buttons_frame = tk.Frame(self.dialog, bg='#f5f5f5', height=100)
        buttons_frame.pack(fill='x', side='bottom', padx=20, pady=20)
        buttons_frame.pack_propagate(False)

        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ الفاتورة",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.save_invoice
        )
        save_btn.pack(side='right', padx=(10, 0), pady=10)

        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=20,
            height=2,
            relief='raised',
            bd=3,
            command=self.cancel
        )
        cancel_btn.pack(side='right', padx=(10, 10), pady=10)

        # التأكد من ظهور الأزرار
        self.dialog.update()
        buttons_frame.update()
        save_btn.update()
        cancel_btn.update()

        print("✅ تم إنشاء أزرار الفواتير بنجاح!")
        # إجبار ظهور الأزرار
        self.dialog.update_idletasks()
        buttons_frame.update_idletasks()
        save_btn.update_idletasks()
        cancel_btn.update_idletasks()
        
        # التأكد من أن الأزرار في المقدمة
        buttons_frame.lift()
        save_btn.lift()
        cancel_btn.lift()

        # تعيين التاريخ الحالي
        self.date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))

    def load_invoice_data(self):
        """تحميل بيانات الفاتورة للتعديل"""
        if self.invoice_data:
            self.invoice_number_entry.insert(0, self.invoice_data.get('invoice_number', ''))
            self.supplier_name_entry.insert(0, self.invoice_data.get('supplier_name', ''))
            self.amount_entry.insert(0, str(self.invoice_data.get('amount', '')))
            self.date_entry.delete(0, tk.END)
            self.date_entry.insert(0, self.invoice_data.get('invoice_date', ''))
            self.status_combo.set(self.invoice_data.get('status', 'غير مدفوعة'))

    def save_invoice(self):
        """حفظ بيانات الفاتورة"""
        # التحقق من صحة البيانات
        if not self.invoice_number_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال رقم الفاتورة")
            return

        try:
            # تحضير البيانات
            invoice_data = {
                'invoice_number': self.invoice_number_entry.get().strip(),
                'supplier_name': self.supplier_name_entry.get().strip(),
                'amount': float(self.amount_entry.get()) if self.amount_entry.get().strip() else 0.0,
                'invoice_date': self.date_entry.get().strip(),
                'status': self.status_combo.get(),
                'created_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            self.result = invoice_data
            messagebox.showinfo("نجح", "تم حفظ بيانات الفاتورة بنجاح!")
            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيمة صحيحة للمبلغ")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

# النوافذ المتبقية - مبسطة مع خاصية dialog
class PurchaseRequestDialog:
    """نافذة حوار المشتريات - مبسطة"""
    def __init__(self, parent, title, purchase_data=None):
        self.dialog = None  # إضافة خاصية dialog
        self.result = {'item_name': 'مواد بناء', 'quantity': 10, 'unit_price': 100.0, 'total_amount': 1000.0, 'status': 'pending'}
        messagebox.showinfo("تم", "تم إضافة طلب شراء تجريبي")

class MaintenanceTaskDialog:
    """نافذة حوار الصيانة - مبسطة"""
    def __init__(self, parent, title, task_data=None):
        self.dialog = None  # إضافة خاصية dialog
        self.result = {'task_description': 'صيانة دورية', 'priority': 'متوسط', 'status': 'قيد التنفيذ', 'assigned_to': 'فريق الصيانة'}
        messagebox.showinfo("تم", "تم إضافة مهمة صيانة تجريبية")

class DailyTaskDialog:
    """نافذة حوار المهام اليومية - مبسطة"""
    def __init__(self, parent, title, task_data=None):
        self.dialog = None  # إضافة خاصية dialog
        self.result = {'task_title': 'مهمة يومية', 'description': 'متابعة المشاريع', 'priority': 'عالي', 'status': 'جديد'}
        messagebox.showinfo("تم", "تم إضافة مهمة يومية تجريبية")
