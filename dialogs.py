# -*- coding: utf-8 -*-
"""
نوافذ الحوار لتطبيق شركة رافع للتطوير العقاري
Dialog Windows for Rafea Real Estate Development Application
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date

class ProjectDialog:
    """نافذة حوار إضافة/تعديل مشروع"""
    
    def __init__(self, parent, title, project_data=None):
        self.parent = parent
        self.project_data = project_data
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x600")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"500x600+{x}+{y}")
        
        self.create_widgets()
        
        if project_data:
            self.load_project_data()
    
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات المشروع",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))
        
        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # اسم المشروع
        tk.Label(content_frame, text="اسم المشروع:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.name_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.name_entry.pack(pady=(0, 15))
        
        # الموقع
        tk.Label(content_frame, text="الموقع:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.location_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.location_entry.pack(pady=(0, 15))
        
        # نوع المشروع
        tk.Label(content_frame, text="نوع المشروع:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.type_combo['values'] = ('سكني', 'تجاري', 'إداري', 'مختلط', 'صناعي')
        self.type_combo.pack(pady=(0, 15))
        
        # التكلفة الإجمالية
        tk.Label(content_frame, text="التكلفة الإجمالية (ريال):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.cost_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.cost_entry.pack(pady=(0, 15))
        
        # نسبة الإنجاز
        tk.Label(content_frame, text="نسبة الإنجاز (%):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.completion_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.completion_entry.pack(pady=(0, 15))
        
        # تاريخ البداية
        tk.Label(content_frame, text="تاريخ البداية (YYYY-MM-DD):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.start_date_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.start_date_entry.pack(pady=(0, 15))
        
        # تاريخ الانتهاء المتوقع
        tk.Label(content_frame, text="تاريخ الانتهاء المتوقع (YYYY-MM-DD):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.end_date_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.end_date_entry.pack(pady=(0, 15))
        
        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.status_combo['values'] = ('تخطيط', 'قيد التنفيذ', 'مكتمل', 'متوقف مؤقتاً')
        self.status_combo.pack(pady=(0, 15))
        
        # الوصف
        tk.Label(content_frame, text="الوصف:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.description_text = tk.Text(content_frame, font=('Arial', 11), width=40, height=4)
        self.description_text.pack(pady=(0, 15))
        
        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x')
        
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=12,
            command=self.save_project
        )
        save_btn.pack(side='left', padx=(0, 10))
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=12,
            command=self.cancel
        )
        cancel_btn.pack(side='left')
        
        # تعيين القيم الافتراضية
        if not self.project_data:
            self.start_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
            self.end_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
            self.completion_entry.insert(0, "0")
            self.type_combo.set("سكني")
            self.status_combo.set("تخطيط")
    
    def load_project_data(self):
        """تحميل بيانات المشروع للتعديل"""
        if self.project_data:
            self.name_entry.insert(0, self.project_data.get('name', ''))
            self.location_entry.insert(0, self.project_data.get('location', ''))
            self.type_combo.set(self.project_data.get('project_type', 'سكني'))
            self.cost_entry.insert(0, str(self.project_data.get('total_cost', '')))
            self.completion_entry.insert(0, str(self.project_data.get('completion_percentage', '0')))
            self.start_date_entry.insert(0, str(self.project_data.get('start_date', '')))
            self.end_date_entry.insert(0, str(self.project_data.get('expected_end_date', '')))
            
            # تحويل الحالة
            status_map = {
                'planning': 'تخطيط',
                'in_progress': 'قيد التنفيذ',
                'completed': 'مكتمل',
                'on_hold': 'متوقف مؤقتاً'
            }
            status_text = status_map.get(self.project_data.get('status', 'planning'), 'تخطيط')
            self.status_combo.set(status_text)
            
            self.description_text.insert('1.0', self.project_data.get('description', ''))
    
    def save_project(self):
        """حفظ بيانات المشروع"""
        # التحقق من صحة البيانات
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المشروع")
            return
        
        try:
            # تحويل البيانات
            cost = float(self.cost_entry.get()) if self.cost_entry.get().strip() else 0.0
            completion = float(self.completion_entry.get()) if self.completion_entry.get().strip() else 0.0
            
            # تحويل الحالة
            status_map = {
                'تخطيط': 'planning',
                'قيد التنفيذ': 'in_progress',
                'مكتمل': 'completed',
                'متوقف مؤقتاً': 'on_hold'
            }
            status = status_map.get(self.status_combo.get(), 'planning')
            
            # إعداد النتيجة
            self.result = {
                'name': self.name_entry.get().strip(),
                'location': self.location_entry.get().strip(),
                'project_type': self.type_combo.get(),
                'total_cost': cost,
                'completion_percentage': completion,
                'start_date': self.start_date_entry.get().strip() or None,
                'expected_end_date': self.end_date_entry.get().strip() or None,
                'status': status,
                'description': self.description_text.get('1.0', 'end-1c').strip()
            }
            
            self.dialog.destroy()
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للتكلفة ونسبة الإنجاز")
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()
    
    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result

class CustomerDialog:
    """نافذة حوار إضافة/تعديل عميل"""
    
    def __init__(self, parent, title, customer_data=None):
        self.parent = parent
        self.customer_data = customer_data
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x500")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"450x500+{x}+{y}")
        
        self.create_widgets()
        
        if customer_data:
            self.load_customer_data()
    
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات العميل",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))
        
        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # الاسم الكامل
        tk.Label(content_frame, text="الاسم الكامل:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.name_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.name_entry.pack(pady=(0, 15))
        
        # رقم الهوية
        tk.Label(content_frame, text="رقم الهوية:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.national_id_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.national_id_entry.pack(pady=(0, 15))
        
        # رقم الهاتف
        tk.Label(content_frame, text="رقم الهاتف:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.phone_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.phone_entry.pack(pady=(0, 15))
        
        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.email_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.email_entry.pack(pady=(0, 15))
        
        # العنوان
        tk.Label(content_frame, text="العنوان:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.address_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.address_entry.pack(pady=(0, 15))
        
        # نوع العميل
        tk.Label(content_frame, text="نوع العميل:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.type_combo['values'] = ('فرد', 'شركة')
        self.type_combo.set('فرد')
        self.type_combo.pack(pady=(0, 15))
        
        # ملاحظات
        tk.Label(content_frame, text="ملاحظات:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.notes_text = tk.Text(content_frame, font=('Arial', 11), width=35, height=4)
        self.notes_text.pack(pady=(0, 15))
        
        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x')
        
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=12,
            command=self.save_customer
        )
        save_btn.pack(side='left', padx=(0, 10))
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=12,
            command=self.cancel
        )
        cancel_btn.pack(side='left')
    
    def load_customer_data(self):
        """تحميل بيانات العميل للتعديل"""
        if self.customer_data:
            self.name_entry.insert(0, self.customer_data.get('full_name', ''))
            self.national_id_entry.insert(0, self.customer_data.get('national_id', ''))
            self.phone_entry.insert(0, self.customer_data.get('phone', ''))
            self.email_entry.insert(0, self.customer_data.get('email', ''))
            self.address_entry.insert(0, self.customer_data.get('address', ''))
            
            # تحويل نوع العميل
            customer_type = 'شركة' if self.customer_data.get('customer_type') == 'company' else 'فرد'
            self.type_combo.set(customer_type)
            
            self.notes_text.insert('1.0', self.customer_data.get('notes', ''))
    
    def save_customer(self):
        """حفظ بيانات العميل"""
        # التحقق من صحة البيانات
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
            return
        
        # تحويل نوع العميل
        customer_type = 'company' if self.type_combo.get() == 'شركة' else 'individual'
        
        # إعداد النتيجة
        self.result = {
            'full_name': self.name_entry.get().strip(),
            'national_id': self.national_id_entry.get().strip(),
            'phone': self.phone_entry.get().strip(),
            'email': self.email_entry.get().strip(),
            'address': self.address_entry.get().strip(),
            'customer_type': customer_type,
            'notes': self.notes_text.get('1.0', 'end-1c').strip()
        }
        
        self.dialog.destroy()
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()
    
    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result
