# -*- coding: utf-8 -*-
"""
نوافذ الحوار لتطبيق شركة رافع للتطوير العقاري
Dialog Windows for Rafea Real Estate Development Application
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, date

class ProjectDialog:
    """نافذة حوار إضافة/تعديل مشروع"""
    
    def __init__(self, parent, title, project_data=None):
        self.parent = parent
        self.project_data = project_data
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x600")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"500x600+{x}+{y}")
        
        self.create_widgets()
        
        if project_data:
            self.load_project_data()
    
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات المشروع",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))
        
        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # اسم المشروع
        tk.Label(content_frame, text="اسم المشروع:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.name_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.name_entry.pack(pady=(0, 15))
        
        # الموقع
        tk.Label(content_frame, text="الموقع:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.location_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.location_entry.pack(pady=(0, 15))
        
        # نوع المشروع
        tk.Label(content_frame, text="نوع المشروع:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.type_combo['values'] = ('سكني', 'تجاري', 'إداري', 'مختلط', 'صناعي')
        self.type_combo.pack(pady=(0, 15))
        
        # التكلفة الإجمالية
        tk.Label(content_frame, text="التكلفة الإجمالية (ريال):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.cost_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.cost_entry.pack(pady=(0, 15))
        
        # نسبة الإنجاز
        tk.Label(content_frame, text="نسبة الإنجاز (%):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.completion_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.completion_entry.pack(pady=(0, 15))
        
        # تاريخ البداية
        tk.Label(content_frame, text="تاريخ البداية (YYYY-MM-DD):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.start_date_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.start_date_entry.pack(pady=(0, 15))
        
        # تاريخ الانتهاء المتوقع
        tk.Label(content_frame, text="تاريخ الانتهاء المتوقع (YYYY-MM-DD):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.end_date_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.end_date_entry.pack(pady=(0, 15))
        
        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.status_combo['values'] = ('تخطيط', 'قيد التنفيذ', 'مكتمل', 'متوقف مؤقتاً')
        self.status_combo.pack(pady=(0, 15))
        
        # الوصف
        tk.Label(content_frame, text="الوصف:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.description_text = tk.Text(content_frame, font=('Arial', 11), width=40, height=4)
        self.description_text.pack(pady=(0, 15))
        
        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x')
        
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=12,
            command=self.save_project
        )
        save_btn.pack(side='left', padx=(0, 10))
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=12,
            command=self.cancel
        )
        cancel_btn.pack(side='left')
        
        # تعيين القيم الافتراضية
        if not self.project_data:
            self.start_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
            self.end_date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
            self.completion_entry.insert(0, "0")
            self.type_combo.set("سكني")
            self.status_combo.set("تخطيط")
    
    def load_project_data(self):
        """تحميل بيانات المشروع للتعديل"""
        if self.project_data:
            self.name_entry.insert(0, self.project_data.get('name', ''))
            self.location_entry.insert(0, self.project_data.get('location', ''))
            self.type_combo.set(self.project_data.get('project_type', 'سكني'))
            self.cost_entry.insert(0, str(self.project_data.get('total_cost', '')))
            self.completion_entry.insert(0, str(self.project_data.get('completion_percentage', '0')))
            self.start_date_entry.insert(0, str(self.project_data.get('start_date', '')))
            self.end_date_entry.insert(0, str(self.project_data.get('expected_end_date', '')))
            
            # تحويل الحالة
            status_map = {
                'planning': 'تخطيط',
                'in_progress': 'قيد التنفيذ',
                'completed': 'مكتمل',
                'on_hold': 'متوقف مؤقتاً'
            }
            status_text = status_map.get(self.project_data.get('status', 'planning'), 'تخطيط')
            self.status_combo.set(status_text)
            
            self.description_text.insert('1.0', self.project_data.get('description', ''))
    
    def save_project(self):
        """حفظ بيانات المشروع"""
        # التحقق من صحة البيانات
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المشروع")
            return
        
        try:
            # تحويل البيانات
            cost = float(self.cost_entry.get()) if self.cost_entry.get().strip() else 0.0
            completion = float(self.completion_entry.get()) if self.completion_entry.get().strip() else 0.0
            
            # تحويل الحالة
            status_map = {
                'تخطيط': 'planning',
                'قيد التنفيذ': 'in_progress',
                'مكتمل': 'completed',
                'متوقف مؤقتاً': 'on_hold'
            }
            status = status_map.get(self.status_combo.get(), 'planning')
            
            # إعداد النتيجة
            self.result = {
                'name': self.name_entry.get().strip(),
                'location': self.location_entry.get().strip(),
                'project_type': self.type_combo.get(),
                'total_cost': cost,
                'completion_percentage': completion,
                'start_date': self.start_date_entry.get().strip() or None,
                'expected_end_date': self.end_date_entry.get().strip() or None,
                'status': status,
                'description': self.description_text.get('1.0', 'end-1c').strip()
            }
            
            self.dialog.destroy()
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للتكلفة ونسبة الإنجاز")
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()
    
    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result

class CustomerDialog:
    """نافذة حوار إضافة/تعديل عميل"""
    
    def __init__(self, parent, title, customer_data=None):
        self.parent = parent
        self.customer_data = customer_data
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x500")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"450x500+{x}+{y}")
        
        self.create_widgets()
        
        if customer_data:
            self.load_customer_data()
    
    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات العميل",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))
        
        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))
        
        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # الاسم الكامل
        tk.Label(content_frame, text="الاسم الكامل:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.name_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.name_entry.pack(pady=(0, 15))
        
        # رقم الهوية
        tk.Label(content_frame, text="رقم الهوية:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.national_id_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.national_id_entry.pack(pady=(0, 15))
        
        # رقم الهاتف
        tk.Label(content_frame, text="رقم الهاتف:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.phone_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.phone_entry.pack(pady=(0, 15))
        
        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.email_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.email_entry.pack(pady=(0, 15))
        
        # العنوان
        tk.Label(content_frame, text="العنوان:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.address_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.address_entry.pack(pady=(0, 15))
        
        # نوع العميل
        tk.Label(content_frame, text="نوع العميل:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.type_combo['values'] = ('فرد', 'شركة')
        self.type_combo.set('فرد')
        self.type_combo.pack(pady=(0, 15))
        
        # ملاحظات
        tk.Label(content_frame, text="ملاحظات:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.notes_text = tk.Text(content_frame, font=('Arial', 11), width=35, height=4)
        self.notes_text.pack(pady=(0, 15))
        
        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x')
        
        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=12,
            command=self.save_customer
        )
        save_btn.pack(side='left', padx=(0, 10))
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=12,
            command=self.cancel
        )
        cancel_btn.pack(side='left')
    
    def load_customer_data(self):
        """تحميل بيانات العميل للتعديل"""
        if self.customer_data:
            self.name_entry.insert(0, self.customer_data.get('full_name', ''))
            self.national_id_entry.insert(0, self.customer_data.get('national_id', ''))
            self.phone_entry.insert(0, self.customer_data.get('phone', ''))
            self.email_entry.insert(0, self.customer_data.get('email', ''))
            self.address_entry.insert(0, self.customer_data.get('address', ''))
            
            # تحويل نوع العميل
            customer_type = 'شركة' if self.customer_data.get('customer_type') == 'company' else 'فرد'
            self.type_combo.set(customer_type)
            
            self.notes_text.insert('1.0', self.customer_data.get('notes', ''))
    
    def save_customer(self):
        """حفظ بيانات العميل"""
        # التحقق من صحة البيانات
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
            return
        
        # تحويل نوع العميل
        customer_type = 'company' if self.type_combo.get() == 'شركة' else 'individual'
        
        # إعداد النتيجة
        self.result = {
            'full_name': self.name_entry.get().strip(),
            'national_id': self.national_id_entry.get().strip(),
            'phone': self.phone_entry.get().strip(),
            'email': self.email_entry.get().strip(),
            'address': self.address_entry.get().strip(),
            'customer_type': customer_type,
            'notes': self.notes_text.get('1.0', 'end-1c').strip()
        }
        
        self.dialog.destroy()
    
    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()
    
    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result

class UnitDialog:
    """نافذة حوار إضافة/تعديل وحدة"""

    def __init__(self, parent, title, unit_data=None):
        self.parent = parent
        self.unit_data = unit_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x600")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"500x600+{x}+{y}")

        self.create_widgets()

        if unit_data:
            self.load_unit_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات الوحدة",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))

        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))

        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # رقم الوحدة
        tk.Label(content_frame, text="رقم الوحدة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.unit_number_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.unit_number_entry.pack(pady=(0, 15))

        # المشروع
        tk.Label(content_frame, text="المشروع:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.project_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.project_combo['values'] = ('مشروع الواحة السكني', 'برج الأعمال التجاري', 'مجمع الفلل الراقية')
        self.project_combo.pack(pady=(0, 15))

        # رقم الطابق
        tk.Label(content_frame, text="رقم الطابق:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.floor_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.floor_entry.pack(pady=(0, 15))

        # نوع الوحدة
        tk.Label(content_frame, text="نوع الوحدة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.unit_type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.unit_type_combo['values'] = ('شقة', 'فيلا', 'دوبلكس', 'استوديو', 'مكتب', 'محل تجاري')
        self.unit_type_combo.pack(pady=(0, 15))

        # المساحة
        tk.Label(content_frame, text="المساحة (متر مربع):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.area_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.area_entry.pack(pady=(0, 15))

        # السعر
        tk.Label(content_frame, text="السعر (ريال):", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.price_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.price_entry.pack(pady=(0, 15))

        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.status_combo['values'] = ('متاح', 'محجوز', 'مباع', 'تحت الصيانة')
        self.status_combo.set('متاح')
        self.status_combo.pack(pady=(0, 15))

        # الوصف
        tk.Label(content_frame, text="الوصف:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.description_text = tk.Text(content_frame, font=('Arial', 11), width=35, height=4)
        self.description_text.pack(pady=(0, 15))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x')

        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=12,
            command=self.save_unit
        )
        save_btn.pack(side='left', padx=(0, 10))

        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=12,
            command=self.cancel
        )
        cancel_btn.pack(side='left')

        # تعيين القيم الافتراضية
        if not self.unit_data:
            self.project_combo.set('مشروع الواحة السكني')
            self.unit_type_combo.set('شقة')
            self.floor_entry.insert(0, "1")
            self.area_entry.insert(0, "120")
            self.price_entry.insert(0, "450000")

    def load_unit_data(self):
        """تحميل بيانات الوحدة للتعديل"""
        if self.unit_data:
            self.unit_number_entry.insert(0, self.unit_data.get('unit_number', ''))
            self.floor_entry.insert(0, str(self.unit_data.get('floor_number', '')))
            self.unit_type_combo.set(self.unit_data.get('unit_type', 'شقة'))
            self.area_entry.insert(0, str(self.unit_data.get('area', '')))
            self.price_entry.insert(0, str(self.unit_data.get('price', '')))

            # تحويل الحالة
            status_map = {
                'available': 'متاح',
                'reserved': 'محجوز',
                'sold': 'مباع',
                'maintenance': 'تحت الصيانة'
            }
            status_text = status_map.get(self.unit_data.get('status', 'available'), 'متاح')
            self.status_combo.set(status_text)

            self.description_text.insert('1.0', self.unit_data.get('description', ''))

    def save_unit(self):
        """حفظ بيانات الوحدة"""
        # التحقق من صحة البيانات
        if not self.unit_number_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال رقم الوحدة")
            return

        try:
            # تحويل البيانات
            area = float(self.area_entry.get()) if self.area_entry.get().strip() else 0.0
            price = float(self.price_entry.get()) if self.price_entry.get().strip() else 0.0
            floor_number = int(self.floor_entry.get()) if self.floor_entry.get().strip() else 1

            # تحويل الحالة
            status_map = {
                'متاح': 'available',
                'محجوز': 'reserved',
                'مباع': 'sold',
                'تحت الصيانة': 'maintenance'
            }
            status = status_map.get(self.status_combo.get(), 'available')

            # إعداد النتيجة
            self.result = {
                'unit_number': self.unit_number_entry.get().strip(),
                'project_id': 1,  # سيتم تحديثه لاحقاً حسب المشروع المختار
                'floor_number': floor_number,
                'unit_type': self.unit_type_combo.get(),
                'area': area,
                'price': price,
                'status': status,
                'description': self.description_text.get('1.0', 'end-1c').strip()
            }

            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للمساحة والسعر ورقم الطابق")

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result

class ContractorDialog:
    """نافذة حوار إضافة/تعديل مقاول"""

    def __init__(self, parent, title, contractor_data=None):
        self.parent = parent
        self.contractor_data = contractor_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x500")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"450x500+{x}+{y}")

        self.create_widgets()

        if contractor_data:
            self.load_contractor_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات المقاول",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))

        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))

        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # اسم المقاول
        tk.Label(content_frame, text="اسم المقاول:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.name_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.name_entry.pack(pady=(0, 15))

        # التخصص
        tk.Label(content_frame, text="التخصص:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.specialty_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.specialty_combo['values'] = ('مقاولات عامة', 'كهرباء', 'سباكة', 'تكييف', 'دهانات', 'بلاط وسيراميك', 'نجارة', 'حدادة')
        self.specialty_combo.pack(pady=(0, 15))

        # رقم الهاتف
        tk.Label(content_frame, text="رقم الهاتف:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.phone_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.phone_entry.pack(pady=(0, 15))

        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.email_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.email_entry.pack(pady=(0, 15))

        # العنوان
        tk.Label(content_frame, text="العنوان:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.address_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.address_entry.pack(pady=(0, 15))

        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.status_combo['values'] = ('نشط', 'غير نشط', 'معلق')
        self.status_combo.set('نشط')
        self.status_combo.pack(pady=(0, 15))

        # ملاحظات
        tk.Label(content_frame, text="ملاحظات:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.notes_text = tk.Text(content_frame, font=('Arial', 11), width=35, height=4)
        self.notes_text.pack(pady=(0, 15))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x')

        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=12,
            command=self.save_contractor
        )
        save_btn.pack(side='left', padx=(0, 10))

        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=12,
            command=self.cancel
        )
        cancel_btn.pack(side='left')

        # تعيين القيم الافتراضية
        if not self.contractor_data:
            self.specialty_combo.set('مقاولات عامة')

    def load_contractor_data(self):
        """تحميل بيانات المقاول للتعديل"""
        if self.contractor_data:
            self.name_entry.insert(0, self.contractor_data.get('name', ''))
            self.specialty_combo.set(self.contractor_data.get('specialty', 'مقاولات عامة'))
            self.phone_entry.insert(0, self.contractor_data.get('phone', ''))
            self.email_entry.insert(0, self.contractor_data.get('email', ''))
            self.address_entry.insert(0, self.contractor_data.get('address', ''))

            # تحويل الحالة
            status_map = {
                'active': 'نشط',
                'inactive': 'غير نشط',
                'suspended': 'معلق'
            }
            status_text = status_map.get(self.contractor_data.get('status', 'active'), 'نشط')
            self.status_combo.set(status_text)

            self.notes_text.insert('1.0', self.contractor_data.get('notes', ''))

    def save_contractor(self):
        """حفظ بيانات المقاول"""
        # التحقق من صحة البيانات
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المقاول")
            return

        # تحويل الحالة
        status_map = {
            'نشط': 'active',
            'غير نشط': 'inactive',
            'معلق': 'suspended'
        }
        status = status_map.get(self.status_combo.get(), 'active')

        # إعداد النتيجة
        self.result = {
            'name': self.name_entry.get().strip(),
            'specialty': self.specialty_combo.get(),
            'phone': self.phone_entry.get().strip(),
            'email': self.email_entry.get().strip(),
            'address': self.address_entry.get().strip(),
            'status': status,
            'notes': self.notes_text.get('1.0', 'end-1c').strip()
        }

        self.dialog.destroy()

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result

class SupplierDialog:
    """نافذة حوار إضافة/تعديل مورد"""

    def __init__(self, parent, title, supplier_data=None):
        self.parent = parent
        self.supplier_data = supplier_data
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x500")
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (500 // 2)
        self.dialog.geometry(f"450x500+{x}+{y}")

        self.create_widgets()

        if supplier_data:
            self.load_supplier_data()

    def create_widgets(self):
        """إنشاء عناصر النافذة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # عنوان النافذة
        title_label = tk.Label(
            main_frame,
            text="بيانات المورد",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))

        # إطار النموذج
        form_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        form_frame.pack(fill='both', expand=True, pady=(0, 20))

        # محتوى النموذج
        content_frame = tk.Frame(form_frame, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # اسم المورد
        tk.Label(content_frame, text="اسم المورد:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.name_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.name_entry.pack(pady=(0, 15))

        # نوع المواد
        tk.Label(content_frame, text="نوع المواد:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.material_type_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.material_type_combo['values'] = ('حديد وصلب', 'أسمنت ومواد بناء', 'كهربائيات', 'سباكة', 'دهانات', 'بلاط وسيراميك', 'أخشاب', 'مواد عزل')
        self.material_type_combo.pack(pady=(0, 15))

        # رقم الهاتف
        tk.Label(content_frame, text="رقم الهاتف:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.phone_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.phone_entry.pack(pady=(0, 15))

        # البريد الإلكتروني
        tk.Label(content_frame, text="البريد الإلكتروني:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.email_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.email_entry.pack(pady=(0, 15))

        # العنوان
        tk.Label(content_frame, text="العنوان:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.address_entry = tk.Entry(content_frame, font=('Arial', 12), width=35)
        self.address_entry.pack(pady=(0, 15))

        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=32, state='readonly')
        self.status_combo['values'] = ('نشط', 'غير نشط', 'معلق')
        self.status_combo.set('نشط')
        self.status_combo.pack(pady=(0, 15))

        # ملاحظات
        tk.Label(content_frame, text="ملاحظات:", font=('Arial', 10, 'bold'), bg='white').pack(anchor='e', pady=(0, 5))
        self.notes_text = tk.Text(content_frame, font=('Arial', 11), width=35, height=4)
        self.notes_text.pack(pady=(0, 15))

        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack(fill='x')

        save_btn = tk.Button(
            buttons_frame,
            text="حفظ",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=12,
            command=self.save_supplier
        )
        save_btn.pack(side='left', padx=(0, 10))

        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=12,
            command=self.cancel
        )
        cancel_btn.pack(side='left')

        # تعيين القيم الافتراضية
        if not self.supplier_data:
            self.material_type_combo.set('حديد وصلب')

    def load_supplier_data(self):
        """تحميل بيانات المورد للتعديل"""
        if self.supplier_data:
            self.name_entry.insert(0, self.supplier_data.get('name', ''))
            self.material_type_combo.set(self.supplier_data.get('material_type', 'حديد وصلب'))
            self.phone_entry.insert(0, self.supplier_data.get('phone', ''))
            self.email_entry.insert(0, self.supplier_data.get('email', ''))
            self.address_entry.insert(0, self.supplier_data.get('address', ''))

            # تحويل الحالة
            status_map = {
                'active': 'نشط',
                'inactive': 'غير نشط',
                'suspended': 'معلق'
            }
            status_text = status_map.get(self.supplier_data.get('status', 'active'), 'نشط')
            self.status_combo.set(status_text)

            self.notes_text.insert('1.0', self.supplier_data.get('notes', ''))

    def save_supplier(self):
        """حفظ بيانات المورد"""
        # التحقق من صحة البيانات
        if not self.name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المورد")
            return

        # تحويل الحالة
        status_map = {
            'نشط': 'active',
            'غير نشط': 'inactive',
            'معلق': 'suspended'
        }
        status = status_map.get(self.status_combo.get(), 'active')

        # إعداد النتيجة
        self.result = {
            'name': self.name_entry.get().strip(),
            'material_type': self.material_type_combo.get(),
            'phone': self.phone_entry.get().strip(),
            'email': self.email_entry.get().strip(),
            'address': self.address_entry.get().strip(),
            'status': status,
            'notes': self.notes_text.get('1.0', 'end-1c').strip()
        }

        self.dialog.destroy()

    def cancel(self):
        """إلغاء العملية"""
        self.result = None
        self.dialog.destroy()

    def run(self):
        """تشغيل النافذة وإرجاع النتيجة"""
        self.dialog.wait_window()
        return self.result
