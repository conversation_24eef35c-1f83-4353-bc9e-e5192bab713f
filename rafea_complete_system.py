#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النظام الشامل لإدارة شركة رافع للتطوير العقاري
Complete Rafea Real Estate Management System
"""

import sys
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from pathlib import Path
import bcrypt
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from database_sqlite import sqlite_manager
from utils.logger_setup import setup_logger
from loguru import logger
from dialogs import (ProjectDialog, CustomerDialog, UnitDialog, ContractorDialog, SupplierDialog,
                     ExtractDialog, InvoiceDialog, PurchaseRequestDialog, MaintenanceTaskDialog, DailyTaskDialog, UserDialog)

class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.user_data = None
        self.setup_window()
        self.create_widgets()
    
    def setup_window(self):
        """إعداد نافذة تسجيل الدخول"""
        self.root.title("تسجيل الدخول - نظام شركة رافع للتطوير العقاري")
        self.root.geometry("400x300")
        self.root.configure(bg='#f5f5f5')
        self.root.resizable(False, False)
        
        # توسيط النافذة
        self.root.eval('tk::PlaceWindow . center')
    
    def create_widgets(self):
        """إنشاء عناصر واجهة تسجيل الدخول"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg='#f5f5f5')
        main_frame.pack(expand=True, fill='both', padx=20, pady=20)
        
        # شعار الشركة
        logo_frame = tk.Frame(main_frame, bg='#2c3e50', height=80)
        logo_frame.pack(fill='x', pady=(0, 20))
        logo_frame.pack_propagate(False)
        
        logo_label = tk.Label(
            logo_frame,
            text="شركة رافع للتطوير العقاري",
            font=('Arial', 16, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        logo_label.pack(expand=True)
        
        # عنوان تسجيل الدخول
        title_label = tk.Label(
            main_frame,
            text="تسجيل الدخول",
            font=('Arial', 14, 'bold'),
            fg='#2c3e50',
            bg='#f5f5f5'
        )
        title_label.pack(pady=(0, 20))
        
        # حقل اسم المستخدم
        tk.Label(main_frame, text="اسم المستخدم:", font=('Arial', 10), bg='#f5f5f5').pack(anchor='e', pady=(0, 5))
        self.username_entry = tk.Entry(main_frame, font=('Arial', 12), width=25)
        self.username_entry.pack(pady=(0, 10))
        self.username_entry.insert(0, "admin")
        
        # حقل كلمة المرور
        tk.Label(main_frame, text="كلمة المرور:", font=('Arial', 10), bg='#f5f5f5').pack(anchor='e', pady=(0, 5))
        self.password_entry = tk.Entry(main_frame, font=('Arial', 12), width=25, show='*')
        self.password_entry.pack(pady=(0, 20))
        self.password_entry.insert(0, "admin123")
        
        # أزرار التحكم
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5')
        buttons_frame.pack()
        
        login_btn = tk.Button(
            buttons_frame,
            text="تسجيل الدخول",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            width=12,
            command=self.login
        )
        login_btn.pack(side='left', padx=(0, 10))
        
        cancel_btn = tk.Button(
            buttons_frame,
            text="إلغاء",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=12,
            command=self.root.quit
        )
        cancel_btn.pack(side='left')
        
        # ربط Enter بتسجيل الدخول
        self.root.bind('<Return>', lambda e: self.login())
        self.username_entry.focus()
    
    def login(self):
        """معالج تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        try:
            query = "SELECT id, username, password_hash, full_name, user_type, is_active FROM users WHERE username = ?"
            result = sqlite_manager.execute_query(query, (username,))
            
            if not result:
                messagebox.showerror("خطأ", "اسم المستخدم غير صحيح")
                return
            
            user = result[0]
            
            if not user['is_active']:
                messagebox.showerror("خطأ", "الحساب غير مفعل")
                return
            
            if bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
                self.user_data = {
                    'id': user['id'],
                    'username': user['username'],
                    'full_name': user['full_name'],
                    'user_type': user['user_type']
                }
                
                logger.info(f"تم تسجيل دخول المستخدم: {username}")
                self.root.destroy()
            else:
                messagebox.showerror("خطأ", "كلمة المرور غير صحيحة")
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل الدخول: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في تسجيل الدخول:\n{str(e)}")
    
    def run(self):
        """تشغيل نافذة تسجيل الدخول"""
        self.root.mainloop()
        return self.user_data

class RafeaCompleteSystem:
    """النظام الشامل لإدارة شركة رافع للتطوير العقاري"""
    
    def __init__(self, user_data):
        self.user_data = user_data
        self.root = tk.Tk()
        self.setup_window()
        self.create_widgets()
        self.load_initial_data()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title(f"نظام إدارة شركة رافع للتطوير العقاري - {self.user_data['full_name']}")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f5f5f5')
        self.root.minsize(1200, 700)
        
        # تعيين أيقونة النافذة (اختياري)
        try:
            self.root.iconbitmap('assets/icon.ico')
        except:
            pass
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # شريط العنوان
        self.create_header()
        
        # التبويبات الرئيسية
        self.create_main_tabs()
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_header(self):
        """إنشاء شريط العنوان"""
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=90)
        header_frame.pack(fill='x', padx=5, pady=5)
        header_frame.pack_propagate(False)
        
        # عنوان النظام
        title_label = tk.Label(
            header_frame,
            text="نظام إدارة شركة رافع للتطوير العقاري",
            font=('Arial', 20, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(side='left', padx=20, pady=25)
        
        # معلومات المستخدم
        user_frame = tk.Frame(header_frame, bg='#2c3e50')
        user_frame.pack(side='right', padx=20, pady=20)
        
        user_label = tk.Label(
            user_frame,
            text=f"مرحباً، {self.user_data['full_name']}",
            font=('Arial', 14, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        user_label.pack()
        
        user_type_label = tk.Label(
            user_frame,
            text=f"نوع المستخدم: {self.user_data['user_type']}",
            font=('Arial', 10),
            fg='#bdc3c7',
            bg='#2c3e50'
        )
        user_type_label.pack()
        
        logout_btn = tk.Button(
            user_frame,
            text="تسجيل الخروج",
            font=('Arial', 10),
            bg='#e74c3c',
            fg='white',
            command=self.logout
        )
        logout_btn.pack(pady=(5, 0))
    
    def create_main_tabs(self):
        """إنشاء التبويبات الرئيسية"""
        # إطار التبويبات
        tabs_frame = tk.Frame(self.root, bg='#ecf0f1')
        tabs_frame.pack(fill='both', expand=True, padx=5, pady=(0, 5))
        
        # إنشاء التبويبات
        self.notebook = ttk.Notebook(tabs_frame)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # 1. لوحة التحكم
        self.dashboard_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(self.dashboard_frame, text='🏠 لوحة التحكم')
        
        # 2. إدارة المشاريع
        self.projects_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(self.projects_frame, text='🏗️ إدارة المشاريع')
        
        # 3. مبيعات الشقق
        self.sales_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(self.sales_frame, text='🏠 مبيعات الشقق')
        
        # 4. المقاولون والمستخلصات
        self.contractors_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(self.contractors_frame, text='👷 المقاولون والمستخلصات')
        
        # 5. الموردون والفواتير
        self.suppliers_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(self.suppliers_frame, text='📦 الموردون والفواتير')
        
        # 6. المشتريات
        self.purchases_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(self.purchases_frame, text='🛒 المشتريات')
        
        # 7. الصيانة والتشغيل
        self.maintenance_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(self.maintenance_frame, text='🔧 الصيانة والتشغيل')
        
        # 8. المهام اليومية
        self.tasks_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(self.tasks_frame, text='📋 المهام اليومية')
        
        # 9. التقارير
        self.reports_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(self.reports_frame, text='📊 التقارير')

        # 10. إدارة المستخدمين
        self.users_frame = tk.Frame(self.notebook, bg='white')
        self.notebook.add(self.users_frame, text='👥 إدارة المستخدمين')

        # إنشاء محتوى كل تبويب
        self.create_dashboard_content()
        self.create_projects_content()
        self.create_sales_content()
        self.create_contractors_content()
        self.create_suppliers_content()
        self.create_purchases_content()
        self.create_maintenance_content()
        self.create_tasks_content()
        self.create_reports_content()
        self.create_users_content()
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = tk.Frame(self.root, bg='#95a5a6', height=30)
        self.status_frame.pack(fill='x', padx=5, pady=(0, 5))
        self.status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(
            self.status_frame,
            text="جاهز",
            font=('Arial', 10),
            fg='white',
            bg='#95a5a6'
        )
        self.status_label.pack(side='left', padx=10, pady=5)
        
        # معلومات قاعدة البيانات
        db_label = tk.Label(
            self.status_frame,
            text="قاعدة البيانات: SQLite متصلة",
            font=('Arial', 10),
            fg='white',
            bg='#95a5a6'
        )
        db_label.pack(side='right', padx=10, pady=5)
        
        # الوقت الحالي
        time_label = tk.Label(
            self.status_frame,
            text=datetime.now().strftime("%Y-%m-%d %H:%M"),
            font=('Arial', 10),
            fg='white',
            bg='#95a5a6'
        )
        time_label.pack(side='right', padx=10, pady=5)

    def create_dashboard_content(self):
        """إنشاء محتوى لوحة التحكم"""
        # عنوان لوحة التحكم
        title_label = tk.Label(
            self.dashboard_frame,
            text="لوحة التحكم الرئيسية",
            font=('Arial', 18, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        title_label.pack(pady=20)

        # إطار الإحصائيات السريعة
        stats_frame = tk.Frame(self.dashboard_frame, bg='white')
        stats_frame.pack(fill='x', padx=20, pady=10)

        # بطاقات الإحصائيات
        self.create_stats_cards(stats_frame)

        # إطار الرسوم البيانية والملخصات
        charts_frame = tk.Frame(self.dashboard_frame, bg='white')
        charts_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # قسم الأنشطة الحديثة
        recent_frame = tk.LabelFrame(
            charts_frame,
            text="الأنشطة الحديثة",
            font=('Arial', 12, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        recent_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))

        # قائمة الأنشطة
        self.activities_listbox = tk.Listbox(
            recent_frame,
            font=('Arial', 10),
            bg='#f8f9fa',
            selectbackground='#3498db'
        )
        self.activities_listbox.pack(fill='both', expand=True, padx=10, pady=10)

        # قسم التنبيهات
        alerts_frame = tk.LabelFrame(
            charts_frame,
            text="التنبيهات والإشعارات",
            font=('Arial', 12, 'bold'),
            fg='#e74c3c',
            bg='white'
        )
        alerts_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))

        # قائمة التنبيهات
        self.alerts_listbox = tk.Listbox(
            alerts_frame,
            font=('Arial', 10),
            bg='#fff5f5',
            selectbackground='#e74c3c'
        )
        self.alerts_listbox.pack(fill='both', expand=True, padx=10, pady=10)

    def create_stats_cards(self, parent):
        """إنشاء بطاقات الإحصائيات"""
        # إحصائيات سريعة
        stats_data = [
            ("إجمالي المشاريع", "0", "#e74c3c", "🏗️"),
            ("المشاريع النشطة", "0", "#27ae60", "⚡"),
            ("إجمالي الوحدات", "0", "#3498db", "🏠"),
            ("الوحدات المباعة", "0", "#f39c12", "💰"),
            ("إجمالي العملاء", "0", "#9b59b6", "👥"),
            ("المقاولون النشطون", "0", "#1abc9c", "👷"),
            ("الفواتير المعلقة", "0", "#e67e22", "📄"),
            ("المهام المعلقة", "0", "#34495e", "📋")
        ]

        # إنشاء شبكة البطاقات
        for i, (title, value, color, icon) in enumerate(stats_data):
            row = i // 4
            col = i % 4

            card_frame = tk.Frame(parent, bg=color, relief='raised', bd=2)
            card_frame.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
            parent.grid_columnconfigure(col, weight=1)

            # أيقونة
            icon_label = tk.Label(
                card_frame,
                text=icon,
                font=('Arial', 24),
                fg='white',
                bg=color
            )
            icon_label.pack(pady=(10, 5))

            # القيمة
            value_label = tk.Label(
                card_frame,
                text=value,
                font=('Arial', 20, 'bold'),
                fg='white',
                bg=color
            )
            value_label.pack(pady=2)

            # العنوان
            title_label = tk.Label(
                card_frame,
                text=title,
                font=('Arial', 10),
                fg='white',
                bg=color
            )
            title_label.pack(pady=(0, 10))

    def create_projects_content(self):
        """إنشاء محتوى إدارة المشاريع"""
        # عنوان
        title_label = tk.Label(
            self.projects_frame,
            text="إدارة المشاريع العقارية",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        title_label.pack(pady=20)

        # شريط الأدوات
        toolbar_frame = tk.Frame(self.projects_frame, bg='white')
        toolbar_frame.pack(fill='x', padx=20, pady=10)

        # أزرار الإدارة
        buttons_data = [
            ("إضافة مشروع جديد", "#27ae60", self.add_project),
            ("تعديل المشروع", "#3498db", self.edit_project),
            ("حذف المشروع", "#e74c3c", self.delete_project),
            ("عرض التفاصيل", "#9b59b6", self.view_project_details),
            ("تحديث القائمة", "#95a5a6", self.refresh_projects)
        ]

        for i, (text, color, command) in enumerate(buttons_data):
            btn = tk.Button(
                toolbar_frame,
                text=text,
                font=('Arial', 10, 'bold'),
                bg=color,
                fg='white',
                relief='flat',
                padx=15,
                pady=8,
                command=command
            )
            btn.pack(side='left', padx=(0, 10) if i < len(buttons_data)-1 else (0, 0))

        # جدول المشاريع
        columns = ('الرقم', 'اسم المشروع', 'الموقع', 'النوع', 'التكلفة', 'نسبة الإنجاز', 'الحالة', 'تاريخ البداية')
        self.projects_tree = ttk.Treeview(self.projects_frame, columns=columns, show='headings', height=18)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.projects_tree.heading(col, text=col)
            self.projects_tree.column(col, width=140)

        # شريط التمرير
        projects_scrollbar = ttk.Scrollbar(self.projects_frame, orient='vertical', command=self.projects_tree.yview)
        self.projects_tree.configure(yscrollcommand=projects_scrollbar.set)

        # تعبئة الجدول
        self.projects_tree.pack(side='left', fill='both', expand=True, padx=(20, 0), pady=10)
        projects_scrollbar.pack(side='right', fill='y', padx=(0, 20), pady=10)

    def create_sales_content(self):
        """إنشاء محتوى مبيعات الشقق"""
        # عنوان
        title_label = tk.Label(
            self.sales_frame,
            text="إدارة مبيعات الشقق والوحدات",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        title_label.pack(pady=20)

        # إطار فرعي للتبويبات
        sales_notebook = ttk.Notebook(self.sales_frame)
        sales_notebook.pack(fill='both', expand=True, padx=20, pady=10)

        # تبويب العملاء
        customers_frame = tk.Frame(sales_notebook, bg='white')
        sales_notebook.add(customers_frame, text='👥 العملاء')

        # تبويب الوحدات
        units_frame = tk.Frame(sales_notebook, bg='white')
        sales_notebook.add(units_frame, text='🏠 الوحدات')

        # تبويب العقود
        contracts_frame = tk.Frame(sales_notebook, bg='white')
        sales_notebook.add(contracts_frame, text='📄 العقود')

        # تبويب المدفوعات
        payments_frame = tk.Frame(sales_notebook, bg='white')
        sales_notebook.add(payments_frame, text='💰 المدفوعات')

        # إنشاء محتوى كل تبويب فرعي
        self.create_customers_content(customers_frame)
        self.create_units_content(units_frame)
        self.create_contracts_content(contracts_frame)
        self.create_payments_content(payments_frame)

    def create_customers_content(self, parent):
        """إنشاء محتوى إدارة العملاء"""
        # شريط الأدوات
        toolbar = tk.Frame(parent, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(toolbar, text="إضافة عميل", bg='#27ae60', fg='white', command=self.add_customer).pack(side='left', padx=5)
        tk.Button(toolbar, text="تعديل", bg='#3498db', fg='white', command=self.edit_customer).pack(side='left', padx=5)
        tk.Button(toolbar, text="حذف", bg='#e74c3c', fg='white', command=self.delete_customer).pack(side='left', padx=5)

        # جدول العملاء
        columns = ('الرقم', 'الاسم', 'الهاتف', 'البريد الإلكتروني', 'النوع')
        self.customers_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)

        for col in columns:
            self.customers_tree.heading(col, text=col)
            self.customers_tree.column(col, width=150)

        self.customers_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_units_content(self, parent):
        """إنشاء محتوى إدارة الوحدات"""
        # شريط الأدوات
        toolbar = tk.Frame(parent, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(toolbar, text="إضافة وحدة", bg='#27ae60', fg='white', command=self.add_unit).pack(side='left', padx=5)
        tk.Button(toolbar, text="تعديل", bg='#3498db', fg='white', command=self.edit_unit).pack(side='left', padx=5)
        tk.Button(toolbar, text="حجز وحدة", bg='#f39c12', fg='white', command=self.reserve_unit).pack(side='left', padx=5)
        tk.Button(toolbar, text="بيع وحدة", bg='#e74c3c', fg='white', command=self.sell_unit).pack(side='left', padx=5)
        tk.Button(toolbar, text="حذف", bg='#c0392b', fg='white', command=self.delete_unit).pack(side='left', padx=5)
        tk.Button(toolbar, text="عرض التفاصيل", bg='#9b59b6', fg='white', command=self.view_unit_details).pack(side='left', padx=5)
        tk.Button(toolbar, text="تحديث", bg='#95a5a6', fg='white', command=self.refresh_units).pack(side='right', padx=5)

        # جدول الوحدات
        columns = ('الرقم', 'رقم الوحدة', 'المشروع', 'النوع', 'المساحة', 'السعر', 'الحالة')
        self.units_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)

        for col in columns:
            self.units_tree.heading(col, text=col)
            self.units_tree.column(col, width=120)

        self.units_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_contracts_content(self, parent):
        """إنشاء محتوى إدارة العقود"""
        # شريط الأدوات
        toolbar = tk.Frame(parent, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(toolbar, text="عقد جديد", bg='#27ae60', fg='white', command=self.new_contract).pack(side='left', padx=5)
        tk.Button(toolbar, text="عرض العقد", bg='#3498db', fg='white', command=self.view_contract).pack(side='left', padx=5)
        tk.Button(toolbar, text="طباعة", bg='#9b59b6', fg='white', command=self.print_contract).pack(side='left', padx=5)

        # جدول العقود
        columns = ('رقم العقد', 'العميل', 'الوحدة', 'قيمة العقد', 'تاريخ العقد', 'الحالة')
        self.contracts_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)

        for col in columns:
            self.contracts_tree.heading(col, text=col)
            self.contracts_tree.column(col, width=140)

        self.contracts_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_payments_content(self, parent):
        """إنشاء محتوى إدارة المدفوعات"""
        # شريط الأدوات
        toolbar = tk.Frame(parent, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(toolbar, text="دفعة جديدة", bg='#27ae60', fg='white', command=self.new_payment).pack(side='left', padx=5)
        tk.Button(toolbar, text="عرض الإيصال", bg='#3498db', fg='white', command=self.view_receipt).pack(side='left', padx=5)
        tk.Button(toolbar, text="طباعة إيصال", bg='#9b59b6', fg='white', command=self.print_receipt).pack(side='left', padx=5)

        # جدول المدفوعات
        columns = ('رقم الدفعة', 'العميل', 'المبلغ', 'طريقة الدفع', 'التاريخ', 'الحالة')
        self.payments_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)

        for col in columns:
            self.payments_tree.heading(col, text=col)
            self.payments_tree.column(col, width=130)

        self.payments_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_contractors_content(self):
        """إنشاء محتوى المقاولون والمستخلصات"""
        # عنوان
        title_label = tk.Label(
            self.contractors_frame,
            text="إدارة المقاولين والمستخلصات",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        title_label.pack(pady=20)

        # تبويبات فرعية
        contractors_notebook = ttk.Notebook(self.contractors_frame)
        contractors_notebook.pack(fill='both', expand=True, padx=20, pady=10)

        # تبويب المقاولين
        contractors_tab = tk.Frame(contractors_notebook, bg='white')
        contractors_notebook.add(contractors_tab, text='👷 المقاولون')

        # تبويب المستخلصات
        extracts_tab = tk.Frame(contractors_notebook, bg='white')
        contractors_notebook.add(extracts_tab, text='📋 المستخلصات')

        # محتوى المقاولين
        self.create_contractors_tab_content(contractors_tab)
        self.create_extracts_tab_content(extracts_tab)

    def create_contractors_tab_content(self, parent):
        """إنشاء محتوى تبويب المقاولين"""
        # شريط الأدوات
        toolbar = tk.Frame(parent, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(toolbar, text="إضافة مقاول", bg='#27ae60', fg='white', command=self.add_contractor).pack(side='left', padx=5)
        tk.Button(toolbar, text="تعديل", bg='#3498db', fg='white', command=self.edit_contractor).pack(side='left', padx=5)
        tk.Button(toolbar, text="حذف", bg='#e74c3c', fg='white', command=self.delete_contractor).pack(side='left', padx=5)

        # جدول المقاولين
        columns = ('الرقم', 'اسم المقاول', 'التخصص', 'الهاتف', 'البريد الإلكتروني', 'الحالة')
        self.contractors_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)

        for col in columns:
            self.contractors_tree.heading(col, text=col)
            self.contractors_tree.column(col, width=140)

        self.contractors_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_extracts_tab_content(self, parent):
        """إنشاء محتوى تبويب المستخلصات"""
        # شريط الأدوات
        toolbar = tk.Frame(parent, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(toolbar, text="مستخلص جديد", bg='#27ae60', fg='white', command=self.new_extract).pack(side='left', padx=5)
        tk.Button(toolbar, text="موافقة", bg='#3498db', fg='white', command=self.approve_extract).pack(side='left', padx=5)
        tk.Button(toolbar, text="دفع", bg='#f39c12', fg='white', command=self.pay_extract).pack(side='left', padx=5)

        # جدول المستخلصات
        columns = ('رقم المستخلص', 'المقاول', 'المشروع', 'المبلغ', 'التاريخ', 'الحالة')
        self.extracts_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)

        for col in columns:
            self.extracts_tree.heading(col, text=col)
            self.extracts_tree.column(col, width=140)

        self.extracts_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_suppliers_content(self):
        """إنشاء محتوى الموردون والفواتير"""
        title_label = tk.Label(
            self.suppliers_frame,
            text="إدارة الموردين والفواتير",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        title_label.pack(pady=20)

        # تبويبات فرعية
        suppliers_notebook = ttk.Notebook(self.suppliers_frame)
        suppliers_notebook.pack(fill='both', expand=True, padx=20, pady=10)

        # تبويب الموردين
        suppliers_tab = tk.Frame(suppliers_notebook, bg='white')
        suppliers_notebook.add(suppliers_tab, text='📦 الموردون')

        # تبويب الفواتير
        invoices_tab = tk.Frame(suppliers_notebook, bg='white')
        suppliers_notebook.add(invoices_tab, text='📄 الفواتير')

        self.create_suppliers_tab_content(suppliers_tab)
        self.create_invoices_tab_content(invoices_tab)

    def create_suppliers_tab_content(self, parent):
        """إنشاء محتوى تبويب الموردين"""
        toolbar = tk.Frame(parent, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(toolbar, text="إضافة مورد", bg='#27ae60', fg='white', command=self.add_supplier).pack(side='left', padx=5)
        tk.Button(toolbar, text="تعديل", bg='#3498db', fg='white', command=self.edit_supplier).pack(side='left', padx=5)
        tk.Button(toolbar, text="حذف", bg='#e74c3c', fg='white', command=self.delete_supplier).pack(side='left', padx=5)

        columns = ('الرقم', 'اسم المورد', 'نوع المواد', 'الهاتف', 'البريد الإلكتروني', 'الحالة')
        self.suppliers_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)

        for col in columns:
            self.suppliers_tree.heading(col, text=col)
            self.suppliers_tree.column(col, width=140)

        self.suppliers_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_invoices_tab_content(self, parent):
        """إنشاء محتوى تبويب الفواتير"""
        toolbar = tk.Frame(parent, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(toolbar, text="فاتورة جديدة", bg='#27ae60', fg='white', command=self.new_invoice).pack(side='left', padx=5)
        tk.Button(toolbar, text="موافقة", bg='#3498db', fg='white', command=self.approve_invoice).pack(side='left', padx=5)
        tk.Button(toolbar, text="دفع", bg='#f39c12', fg='white', command=self.pay_invoice).pack(side='left', padx=5)

        columns = ('رقم الفاتورة', 'المورد', 'المبلغ', 'التاريخ', 'تاريخ الاستحقاق', 'الحالة')
        self.invoices_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15)

        for col in columns:
            self.invoices_tree.heading(col, text=col)
            self.invoices_tree.column(col, width=140)

        self.invoices_tree.pack(fill='both', expand=True, padx=10, pady=10)

    def create_purchases_content(self):
        """إنشاء محتوى المشتريات"""
        title_label = tk.Label(
            self.purchases_frame,
            text="إدارة المشتريات وطلبات الشراء",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        title_label.pack(pady=20)

        # شريط الأدوات
        toolbar = tk.Frame(self.purchases_frame, bg='white')
        toolbar.pack(fill='x', padx=20, pady=10)

        tk.Button(toolbar, text="طلب شراء جديد", bg='#27ae60', fg='white', command=self.new_purchase_request).pack(side='left', padx=5)
        tk.Button(toolbar, text="موافقة", bg='#3498db', fg='white', command=self.approve_purchase).pack(side='left', padx=5)
        tk.Button(toolbar, text="رفض", bg='#e74c3c', fg='white', command=self.reject_purchase).pack(side='left', padx=5)
        tk.Button(toolbar, text="تنفيذ", bg='#f39c12', fg='white', command=self.execute_purchase).pack(side='left', padx=5)

        # جدول طلبات الشراء
        columns = ('رقم الطلب', 'المشروع', 'المواد المطلوبة', 'الكمية', 'التكلفة المقدرة', 'تاريخ الطلب', 'الحالة')
        self.purchases_tree = ttk.Treeview(self.purchases_frame, columns=columns, show='headings', height=18)

        for col in columns:
            self.purchases_tree.heading(col, text=col)
            self.purchases_tree.column(col, width=140)

        purchases_scrollbar = ttk.Scrollbar(self.purchases_frame, orient='vertical', command=self.purchases_tree.yview)
        self.purchases_tree.configure(yscrollcommand=purchases_scrollbar.set)

        self.purchases_tree.pack(side='left', fill='both', expand=True, padx=(20, 0), pady=10)
        purchases_scrollbar.pack(side='right', fill='y', padx=(0, 20), pady=10)

    def create_maintenance_content(self):
        """إنشاء محتوى الصيانة والتشغيل"""
        title_label = tk.Label(
            self.maintenance_frame,
            text="إدارة الصيانة والتشغيل",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        title_label.pack(pady=20)

        # شريط الأدوات
        toolbar = tk.Frame(self.maintenance_frame, bg='white')
        toolbar.pack(fill='x', padx=20, pady=10)

        tk.Button(toolbar, text="مهمة صيانة جديدة", bg='#27ae60', fg='white', command=self.new_maintenance_task).pack(side='left', padx=5)
        tk.Button(toolbar, text="تعيين فني", bg='#3498db', fg='white', command=self.assign_technician).pack(side='left', padx=5)
        tk.Button(toolbar, text="إنجاز المهمة", bg='#f39c12', fg='white', command=self.complete_maintenance).pack(side='left', padx=5)
        tk.Button(toolbar, text="تقرير التكاليف", bg='#9b59b6', fg='white', command=self.maintenance_cost_report).pack(side='left', padx=5)

        # جدول مهام الصيانة
        columns = ('رقم المهمة', 'نوع الصيانة', 'الموقع/الوحدة', 'الفني المسؤول', 'التاريخ', 'التكلفة', 'الحالة')
        self.maintenance_tree = ttk.Treeview(self.maintenance_frame, columns=columns, show='headings', height=18)

        for col in columns:
            self.maintenance_tree.heading(col, text=col)
            self.maintenance_tree.column(col, width=140)

        maintenance_scrollbar = ttk.Scrollbar(self.maintenance_frame, orient='vertical', command=self.maintenance_tree.yview)
        self.maintenance_tree.configure(yscrollcommand=maintenance_scrollbar.set)

        self.maintenance_tree.pack(side='left', fill='both', expand=True, padx=(20, 0), pady=10)
        maintenance_scrollbar.pack(side='right', fill='y', padx=(0, 20), pady=10)

    def create_tasks_content(self):
        """إنشاء محتوى المهام اليومية"""
        title_label = tk.Label(
            self.tasks_frame,
            text="إدارة المهام اليومية",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        title_label.pack(pady=20)

        # شريط الأدوات
        toolbar = tk.Frame(self.tasks_frame, bg='white')
        toolbar.pack(fill='x', padx=20, pady=10)

        tk.Button(toolbar, text="مهمة جديدة", bg='#27ae60', fg='white', command=self.new_daily_task).pack(side='left', padx=5)
        tk.Button(toolbar, text="تعديل", bg='#3498db', fg='white', command=self.edit_daily_task).pack(side='left', padx=5)
        tk.Button(toolbar, text="إنجاز", bg='#f39c12', fg='white', command=self.complete_daily_task).pack(side='left', padx=5)
        tk.Button(toolbar, text="حذف", bg='#e74c3c', fg='white', command=self.delete_daily_task).pack(side='left', padx=5)

        # فلتر المهام
        filter_frame = tk.Frame(self.tasks_frame, bg='white')
        filter_frame.pack(fill='x', padx=20, pady=5)

        tk.Label(filter_frame, text="فلترة حسب:", bg='white').pack(side='left', padx=5)

        self.task_filter = ttk.Combobox(filter_frame, values=['جميع المهام', 'مهامي', 'مهام اليوم', 'المهام المعلقة', 'المهام المكتملة'])
        self.task_filter.set('جميع المهام')
        self.task_filter.pack(side='left', padx=5)

        tk.Button(filter_frame, text="تطبيق الفلتر", bg='#95a5a6', fg='white', command=self.apply_task_filter).pack(side='left', padx=5)

        # جدول المهام
        columns = ('رقم المهمة', 'العنوان', 'الوصف', 'المسؤول', 'الأولوية', 'تاريخ الإنشاء', 'تاريخ الاستحقاق', 'الحالة')
        self.daily_tasks_tree = ttk.Treeview(self.tasks_frame, columns=columns, show='headings', height=16)

        for col in columns:
            self.daily_tasks_tree.heading(col, text=col)
            self.daily_tasks_tree.column(col, width=130)

        tasks_scrollbar = ttk.Scrollbar(self.tasks_frame, orient='vertical', command=self.daily_tasks_tree.yview)
        self.daily_tasks_tree.configure(yscrollcommand=tasks_scrollbar.set)

        self.daily_tasks_tree.pack(side='left', fill='both', expand=True, padx=(20, 0), pady=10)
        tasks_scrollbar.pack(side='right', fill='y', padx=(0, 20), pady=10)

    def create_reports_content(self):
        """إنشاء محتوى التقارير"""
        title_label = tk.Label(
            self.reports_frame,
            text="التقارير والإحصائيات المتقدمة",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        title_label.pack(pady=20)

        # إطار التقارير
        reports_main_frame = tk.Frame(self.reports_frame, bg='white')
        reports_main_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # قسم أنواع التقارير
        reports_types_frame = tk.LabelFrame(
            reports_main_frame,
            text="أنواع التقارير",
            font=('Arial', 12, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        reports_types_frame.pack(fill='x', pady=(0, 20))

        # أزرار التقارير
        reports_buttons_frame = tk.Frame(reports_types_frame, bg='white')
        reports_buttons_frame.pack(fill='x', padx=10, pady=10)

        # الصف الأول من التقارير
        row1_frame = tk.Frame(reports_buttons_frame, bg='white')
        row1_frame.pack(fill='x', pady=5)

        reports_row1 = [
            ("تقرير المشاريع", "#3498db", self.generate_projects_report),
            ("تقرير المبيعات", "#27ae60", self.generate_sales_report),
            ("تقرير المقاولين", "#e74c3c", self.generate_contractors_report),
            ("تقرير الموردين", "#f39c12", self.generate_suppliers_report)
        ]

        for text, color, command in reports_row1:
            btn = tk.Button(
                row1_frame,
                text=text,
                font=('Arial', 10, 'bold'),
                bg=color,
                fg='white',
                width=18,
                command=command
            )
            btn.pack(side='left', padx=5)

        # الصف الثاني من التقارير
        row2_frame = tk.Frame(reports_buttons_frame, bg='white')
        row2_frame.pack(fill='x', pady=5)

        reports_row2 = [
            ("تقرير المشتريات", "#9b59b6", self.generate_purchases_report),
            ("تقرير الصيانة", "#1abc9c", self.generate_maintenance_report),
            ("تقرير المهام", "#34495e", self.generate_tasks_report),
            ("التقرير الشامل", "#2c3e50", self.generate_comprehensive_report)
        ]

        for text, color, command in reports_row2:
            btn = tk.Button(
                row2_frame,
                text=text,
                font=('Arial', 10, 'bold'),
                bg=color,
                fg='white',
                width=18,
                command=command
            )
            btn.pack(side='left', padx=5)

        # قسم فلاتر التقارير
        filters_frame = tk.LabelFrame(
            reports_main_frame,
            text="فلاتر التقارير",
            font=('Arial', 12, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        filters_frame.pack(fill='x', pady=(0, 20))

        filters_content = tk.Frame(filters_frame, bg='white')
        filters_content.pack(fill='x', padx=10, pady=10)

        # فلتر التاريخ
        tk.Label(filters_content, text="من تاريخ:", bg='white').grid(row=0, column=0, padx=5, sticky='e')
        self.date_from_entry = tk.Entry(filters_content, width=12)
        self.date_from_entry.grid(row=0, column=1, padx=5)
        self.date_from_entry.insert(0, "2024-01-01")

        tk.Label(filters_content, text="إلى تاريخ:", bg='white').grid(row=0, column=2, padx=5, sticky='e')
        self.date_to_entry = tk.Entry(filters_content, width=12)
        self.date_to_entry.grid(row=0, column=3, padx=5)
        self.date_to_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))

        # فلتر المشروع
        tk.Label(filters_content, text="المشروع:", bg='white').grid(row=0, column=4, padx=5, sticky='e')
        self.project_filter = ttk.Combobox(filters_content, width=15, values=['جميع المشاريع'])
        self.project_filter.set('جميع المشاريع')
        self.project_filter.grid(row=0, column=5, padx=5)

        # منطقة عرض التقرير
        self.report_display_frame = tk.LabelFrame(
            reports_main_frame,
            text="عرض التقرير",
            font=('Arial', 12, 'bold'),
            fg='#2c3e50',
            bg='white'
        )
        self.report_display_frame.pack(fill='both', expand=True)

        # شريط أدوات التقرير
        report_toolbar = tk.Frame(self.report_display_frame, bg='white')
        report_toolbar.pack(fill='x', padx=10, pady=5)

        tk.Button(report_toolbar, text="طباعة", bg='#3498db', fg='white', command=self.print_report).pack(side='left', padx=5)
        tk.Button(report_toolbar, text="تصدير PDF", bg='#e74c3c', fg='white', command=self.export_pdf).pack(side='left', padx=5)
        tk.Button(report_toolbar, text="تصدير Excel", bg='#27ae60', fg='white', command=self.export_excel).pack(side='left', padx=5)
        tk.Button(report_toolbar, text="مسح", bg='#95a5a6', fg='white', command=self.clear_report).pack(side='right', padx=5)

        # منطقة النص لعرض التقرير
        report_text_frame = tk.Frame(self.report_display_frame, bg='white')
        report_text_frame.pack(fill='both', expand=True, padx=10, pady=10)

        self.report_text = tk.Text(
            report_text_frame,
            font=('Arial', 11),
            wrap=tk.WORD,
            bg='#f8f9fa',
            fg='#2c3e50'
        )

        # شريط التمرير للتقرير
        report_scrollbar = ttk.Scrollbar(report_text_frame, orient='vertical', command=self.report_text.yview)
        self.report_text.configure(yscrollcommand=report_scrollbar.set)

        self.report_text.pack(side='left', fill='both', expand=True)
        report_scrollbar.pack(side='right', fill='y')

        # عرض تقرير افتراضي
        self.show_default_report()

    def show_default_report(self):
        """عرض التقرير الافتراضي"""
        welcome_report = f"""
═══════════════════════════════════════════════════════════════════
                    مرحباً بك في نظام التقارير المتقدم
                    شركة رافع للتطوير العقاري
═══════════════════════════════════════════════════════════════════

📅 التاريخ: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
👤 المستخدم: {self.user_data['full_name']}

🎯 أنواع التقارير المتاحة:

📊 التقارير الأساسية:
   • تقرير المشاريع - معلومات شاملة عن جميع المشاريع
   • تقرير المبيعات - إحصائيات المبيعات والعملاء
   • تقرير المقاولين - بيانات المقاولين والمستخلصات
   • تقرير الموردين - معلومات الموردين والفواتير

📈 التقارير المتخصصة:
   • تقرير المشتريات - طلبات الشراء والموافقات
   • تقرير الصيانة - أعمال الصيانة والتكاليف
   • تقرير المهام - المهام اليومية والإنجازات
   • التقرير الشامل - ملخص كامل للنظام

🔧 ميزات التقارير:
   ✅ فلترة حسب التاريخ والمشروع
   ✅ طباعة مباشرة
   ✅ تصدير PDF و Excel
   ✅ تحديث فوري للبيانات

💡 لإنشاء تقرير، اختر نوع التقرير من الأزرار أعلاه
   ويمكنك استخدام الفلاتر لتخصيص النتائج.

═══════════════════════════════════════════════════════════════════
        """

        self.report_text.delete('1.0', tk.END)
        self.report_text.insert('1.0', welcome_report)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            self.update_status("جاري تحميل البيانات...")
            # تحديث الإحصائيات في لوحة التحكم
            self.update_dashboard_stats()
            self.load_recent_activities()

            # تحميل بيانات الجداول
            self.load_projects_data()
            self.load_customers_data()
            self.load_units_data()
            self.load_contractors_data()
            self.load_extracts_data()
            self.load_suppliers_data()
            self.load_invoices_data()
            self.load_purchase_requests_data()
            self.load_maintenance_tasks_data()
            self.load_daily_tasks_data()
            self.load_users_data()

            self.update_status("تم تحميل البيانات بنجاح")
        except Exception as e:
            logger.error(f"خطأ في تحميل البيانات الأولية: {e}")
            self.update_status(f"خطأ في تحميل البيانات: {str(e)}")

    def update_status(self, message):
        """تحديث رسالة شريط الحالة"""
        if hasattr(self, 'status_label'):
            self.status_label.config(text=message)

    def update_dashboard_stats(self):
        """تحديث إحصائيات لوحة التحكم"""
        try:
            # جمع الإحصائيات من قاعدة البيانات
            stats = {}

            # إحصائيات المشاريع
            stats['projects_total'] = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM projects")[0]['count']
            stats['projects_active'] = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM projects WHERE status = 'in_progress'")[0]['count']

            # إحصائيات الوحدات
            stats['units_total'] = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM units")[0]['count']
            stats['units_sold'] = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM units WHERE status = 'sold'")[0]['count']

            # إحصائيات العملاء
            stats['customers_total'] = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM customers")[0]['count']

            # إحصائيات المقاولين
            try:
                stats['contractors_active'] = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM contractors")[0]['count']
            except:
                stats['contractors_active'] = 0

            # إحصائيات الفواتير المعلقة
            try:
                stats['invoices_pending'] = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM invoices")[0]['count']
            except:
                stats['invoices_pending'] = 0

            # إحصائيات المهام المعلقة
            try:
                stats['tasks_pending'] = sqlite_manager.execute_query("SELECT COUNT(*) as count FROM daily_tasks")[0]['count']
            except:
                stats['tasks_pending'] = 0

            # تحديث البطاقات (سيتم تطويرها لاحقاً)
            logger.info("تم تحديث إحصائيات لوحة التحكم")

        except Exception as e:
            logger.error(f"خطأ في تحديث إحصائيات لوحة التحكم: {e}")

    def load_recent_activities(self):
        """تحميل الأنشطة الحديثة"""
        try:
            # مسح القائمة الحالية
            self.activities_listbox.delete(0, tk.END)

            # إضافة أنشطة تجريبية
            activities = [
                "تم إضافة مشروع جديد: مشروع الواحة السكني",
                "تم بيع وحدة رقم A15 في مشروع برج الأعمال",
                "تم استلام مستخلص من المقاول أحمد محمد",
                "تم إنشاء فاتورة جديدة من مورد الحديد والصلب",
                "تم إنجاز مهمة صيانة في الوحدة B22"
            ]

            for activity in activities:
                self.activities_listbox.insert(tk.END, activity)

        except Exception as e:
            logger.error(f"خطأ في تحميل الأنشطة الحديثة: {e}")

    # دوال إدارة المشاريع
    def add_project(self):
        """إضافة مشروع جديد"""
        dialog = ProjectDialog(self.root, "إضافة مشروع جديد")
        result = dialog.run()

        if result:
            try:
                query = """
                    INSERT INTO projects (name, location, project_type, total_cost,
                                        completion_percentage, start_date, expected_end_date,
                                        status, description, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result['name'],
                    result['location'],
                    result['project_type'],
                    result['total_cost'],
                    result['completion_percentage'],
                    result['start_date'],
                    result['expected_end_date'],
                    result['status'],
                    result['description'],
                    self.user_data['id']
                ))

                messagebox.showinfo("نجح", "تم إضافة المشروع بنجاح")
                self.load_projects_data()
                self.update_dashboard_stats()

            except Exception as e:
                logger.error(f"خطأ في إضافة المشروع: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة المشروع:\n{str(e)}")

    def edit_project(self):
        """تعديل مشروع"""
        selection = self.projects_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للتعديل")
            return

        item = self.projects_tree.item(selection[0])
        project_id = item['values'][0]

        try:
            query = """
                SELECT name, location, project_type, total_cost, completion_percentage,
                       start_date, expected_end_date, status, description
                FROM projects WHERE id = ?
            """
            result = sqlite_manager.execute_query(query, (project_id,))

            if result:
                project_data = dict(result[0])
                dialog = ProjectDialog(self.root, "تعديل المشروع", project_data)
                updated_data = dialog.run()

                if updated_data:
                    update_query = """
                        UPDATE projects
                        SET name=?, location=?, project_type=?, total_cost=?,
                            completion_percentage=?, start_date=?, expected_end_date=?,
                            status=?, description=?, updated_at=CURRENT_TIMESTAMP
                        WHERE id=?
                    """
                    sqlite_manager.execute_query(update_query, (
                        updated_data['name'],
                        updated_data['location'],
                        updated_data['project_type'],
                        updated_data['total_cost'],
                        updated_data['completion_percentage'],
                        updated_data['start_date'],
                        updated_data['expected_end_date'],
                        updated_data['status'],
                        updated_data['description'],
                        project_id
                    ))

                    messagebox.showinfo("نجح", "تم تحديث المشروع بنجاح")
                    self.load_projects_data()
                    self.update_dashboard_stats()

        except Exception as e:
            logger.error(f"خطأ في تعديل المشروع: {e}")
            messagebox.showerror("خطأ", f"فشل في تعديل المشروع:\n{str(e)}")

    def delete_project(self):
        """حذف مشروع"""
        selection = self.projects_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للحذف")
            return

        item = self.projects_tree.item(selection[0])
        project_id = item['values'][0]
        project_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المشروع '{project_name}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM projects WHERE id = ?", (project_id,))
                messagebox.showinfo("نجح", "تم حذف المشروع بنجاح")
                self.load_projects_data()
                self.update_dashboard_stats()

            except Exception as e:
                logger.error(f"خطأ في حذف المشروع: {e}")
                messagebox.showerror("خطأ", f"فشل في حذف المشروع:\n{str(e)}")

    def view_project_details(self):
        """عرض تفاصيل المشروع"""
        selection = self.projects_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع لعرض التفاصيل")
            return

        item = self.projects_tree.item(selection[0])
        project_id = item['values'][0]

        try:
            query = """
                SELECT * FROM projects WHERE id = ?
            """
            result = sqlite_manager.execute_query(query, (project_id,))

            if result:
                project = result[0]
                details = f"""
تفاصيل المشروع:

الاسم: {project['name']}
الموقع: {project['location']}
النوع: {project['project_type']}
التكلفة الإجمالية: {project['total_cost']:,.0f} ريال
نسبة الإنجاز: {project['completion_percentage']:.1f}%
تاريخ البداية: {project['start_date']}
تاريخ الانتهاء المتوقع: {project['expected_end_date']}
الحالة: {project['status']}
الوصف: {project['description']}
تاريخ الإنشاء: {project['created_at']}
                """
                messagebox.showinfo("تفاصيل المشروع", details)

        except Exception as e:
            logger.error(f"خطأ في عرض تفاصيل المشروع: {e}")
            messagebox.showerror("خطأ", f"فشل في عرض التفاصيل:\n{str(e)}")

    def refresh_projects(self):
        """تحديث قائمة المشاريع"""
        self.load_projects_data()

    def load_projects_data(self):
        """تحميل بيانات المشاريع"""
        try:
            # مسح البيانات القديمة
            for item in self.projects_tree.get_children():
                self.projects_tree.delete(item)

            # تحميل المشاريع
            query = """
                SELECT id, name, location, project_type, total_cost,
                       completion_percentage, status, start_date
                FROM projects
                ORDER BY id DESC
            """
            projects = sqlite_manager.execute_query(query)

            # إضافة المشاريع إلى الجدول
            for project in projects:
                status_text = {
                    'planning': 'تخطيط',
                    'in_progress': 'قيد التنفيذ',
                    'completed': 'مكتمل',
                    'on_hold': 'متوقف مؤقتاً'
                }.get(project['status'], project['status'])

                cost_text = f"{project['total_cost']:,.0f} ريال" if project['total_cost'] else "غير محدد"
                completion_text = f"{project['completion_percentage']:.1f}%" if project['completion_percentage'] else "0%"

                self.projects_tree.insert('', 'end', values=(
                    project['id'],
                    project['name'] or '',
                    project['location'] or '',
                    project['project_type'] or '',
                    cost_text,
                    completion_text,
                    status_text,
                    project['start_date'] or ''
                ))

            if hasattr(self, 'status_label'):
                self.update_status(f"تم تحميل {len(projects)} مشروع")

        except Exception as e:
            logger.error(f"خطأ في تحميل المشاريع: {e}")
            if hasattr(self, 'status_label'):
                self.update_status(f"خطأ في تحميل المشاريع: {str(e)}")

    # دوال إدارة العملاء
    def add_customer(self):
        """إضافة عميل جديد"""
        dialog = CustomerDialog(self.root, "إضافة عميل جديد")
        result = dialog.run()

        if result:
            try:
                query = """
                    INSERT INTO customers (full_name, national_id, phone, email, address, customer_type, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result['full_name'],
                    result['national_id'],
                    result['phone'],
                    result['email'],
                    result['address'],
                    result['customer_type'],
                    result['notes']
                ))

                messagebox.showinfo("نجح", "تم إضافة العميل بنجاح")
                self.load_customers_data()
                self.update_dashboard_stats()

            except Exception as e:
                logger.error(f"خطأ في إضافة العميل: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة العميل:\n{str(e)}")

    def edit_customer(self):
        """تعديل عميل"""
        selection = self.customers_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للتعديل")
            return

        item = self.customers_tree.item(selection[0])
        customer_id = item['values'][0]

        try:
            query = """
                SELECT full_name, national_id, phone, email, address, customer_type, notes
                FROM customers WHERE id = ?
            """
            result = sqlite_manager.execute_query(query, (customer_id,))

            if result:
                customer_data = dict(result[0])
                dialog = CustomerDialog(self.root, "تعديل العميل", customer_data)
                updated_data = dialog.run()

                if updated_data:
                    update_query = """
                        UPDATE customers
                        SET full_name=?, national_id=?, phone=?, email=?, address=?, customer_type=?, notes=?, updated_at=CURRENT_TIMESTAMP
                        WHERE id=?
                    """
                    sqlite_manager.execute_query(update_query, (
                        updated_data['full_name'],
                        updated_data['national_id'],
                        updated_data['phone'],
                        updated_data['email'],
                        updated_data['address'],
                        updated_data['customer_type'],
                        updated_data['notes'],
                        customer_id
                    ))

                    messagebox.showinfo("نجح", "تم تحديث العميل بنجاح")
                    self.load_customers_data()

        except Exception as e:
            logger.error(f"خطأ في تعديل العميل: {e}")
            messagebox.showerror("خطأ", f"فشل في تعديل العميل:\n{str(e)}")

    def delete_customer(self):
        """حذف عميل"""
        selection = self.customers_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل للحذف")
            return

        item = self.customers_tree.item(selection[0])
        customer_id = item['values'][0]
        customer_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف العميل '{customer_name}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM customers WHERE id = ?", (customer_id,))
                messagebox.showinfo("نجح", "تم حذف العميل بنجاح")
                self.load_customers_data()
                self.update_dashboard_stats()

            except Exception as e:
                logger.error(f"خطأ في حذف العميل: {e}")
                messagebox.showerror("خطأ", f"فشل في حذف العميل:\n{str(e)}")

    def load_customers_data(self):
        """تحميل بيانات العملاء"""
        try:
            # مسح البيانات القديمة
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            # تحميل العملاء
            query = """
                SELECT id, full_name, national_id, phone, email, customer_type
                FROM customers
                ORDER BY id DESC
            """
            customers = sqlite_manager.execute_query(query)

            # إضافة العملاء إلى الجدول
            for customer in customers:
                customer_type_text = 'شركة' if customer['customer_type'] == 'company' else 'فرد'

                self.customers_tree.insert('', 'end', values=(
                    customer['id'],
                    customer['full_name'] or '',
                    customer['phone'] or '',
                    customer['email'] or '',
                    customer_type_text
                ))

            if hasattr(self, 'status_label'):
                self.update_status(f"تم تحميل {len(customers)} عميل")

        except Exception as e:
            logger.error(f"خطأ في تحميل العملاء: {e}")
            if hasattr(self, 'status_label'):
                self.update_status(f"خطأ في تحميل العملاء: {str(e)}")

    # دوال إدارة الوحدات
    def add_unit(self):
        """إضافة وحدة جديدة"""
        dialog = UnitDialog(self.root, "إضافة وحدة جديدة")
        result = dialog.run()

        if result:
            try:
                query = """
                    INSERT INTO units (project_id, unit_number, floor_number, unit_type,
                                     area, price, status, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result['project_id'],
                    result['unit_number'],
                    result['floor_number'],
                    result['unit_type'],
                    result['area'],
                    result['price'],
                    result['status'],
                    result['description']
                ))

                messagebox.showinfo("نجح", "تم إضافة الوحدة بنجاح")
                self.load_units_data()
                self.update_dashboard_stats()

            except Exception as e:
                logger.error(f"خطأ في إضافة الوحدة: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة الوحدة:\n{str(e)}")

    def edit_unit(self):
        """تعديل وحدة"""
        selection = self.units_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار وحدة للتعديل")
            return

        item = self.units_tree.item(selection[0])
        unit_id = item['values'][0]

        try:
            query = """
                SELECT unit_number, project_id, floor_number, unit_type,
                       area, price, status, description
                FROM units WHERE id = ?
            """
            result = sqlite_manager.execute_query(query, (unit_id,))

            if result:
                unit_data = dict(result[0])
                dialog = UnitDialog(self.root, "تعديل الوحدة", unit_data)
                updated_data = dialog.run()

                if updated_data:
                    update_query = """
                        UPDATE units
                        SET unit_number=?, project_id=?, floor_number=?, unit_type=?,
                            area=?, price=?, status=?, description=?, updated_at=CURRENT_TIMESTAMP
                        WHERE id=?
                    """
                    sqlite_manager.execute_query(update_query, (
                        updated_data['unit_number'],
                        updated_data['project_id'],
                        updated_data['floor_number'],
                        updated_data['unit_type'],
                        updated_data['area'],
                        updated_data['price'],
                        updated_data['status'],
                        updated_data['description'],
                        unit_id
                    ))

                    messagebox.showinfo("نجح", "تم تحديث الوحدة بنجاح")
                    self.load_units_data()
                    self.update_dashboard_stats()

        except Exception as e:
            logger.error(f"خطأ في تعديل الوحدة: {e}")
            messagebox.showerror("خطأ", f"فشل في تعديل الوحدة:\n{str(e)}")

    def reserve_unit(self):
        """حجز وحدة"""
        selection = self.units_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار وحدة للحجز")
            return

        item = self.units_tree.item(selection[0])
        unit_id = item['values'][0]
        unit_number = item['values'][1]
        current_status = item['values'][6]

        # التحقق من حالة الوحدة
        if current_status != 'متاح':
            messagebox.showwarning("تحذير", f"لا يمكن حجز الوحدة {unit_number} - الحالة الحالية: {current_status}")
            return

        # نافذة اختيار العميل للحجز
        customer_name = simpledialog.askstring("حجز الوحدة", f"أدخل اسم العميل لحجز الوحدة {unit_number}:")

        if customer_name:
            try:
                # تحديث حالة الوحدة إلى محجوز
                update_query = """
                    UPDATE units
                    SET status='reserved', updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """
                sqlite_manager.execute_query(update_query, (unit_id,))

                # إضافة سجل الحجز (يمكن تطويره لاحقاً)
                messagebox.showinfo("نجح", f"تم حجز الوحدة {unit_number} للعميل {customer_name}")
                self.load_units_data()
                self.update_dashboard_stats()

            except Exception as e:
                logger.error(f"خطأ في حجز الوحدة: {e}")
                messagebox.showerror("خطأ", f"فشل في حجز الوحدة:\n{str(e)}")

    def sell_unit(self):
        """بيع وحدة"""
        selection = self.units_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار وحدة للبيع")
            return

        item = self.units_tree.item(selection[0])
        unit_id = item['values'][0]
        unit_number = item['values'][1]
        current_status = item['values'][6]

        # التحقق من حالة الوحدة
        if current_status == 'مباع':
            messagebox.showwarning("تحذير", f"الوحدة {unit_number} مباعة بالفعل")
            return

        # تأكيد البيع
        if messagebox.askyesno("تأكيد البيع", f"هل أنت متأكد من بيع الوحدة {unit_number}؟"):
            try:
                # تحديث حالة الوحدة إلى مباع
                update_query = """
                    UPDATE units
                    SET status='sold', updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """
                sqlite_manager.execute_query(update_query, (unit_id,))

                messagebox.showinfo("نجح", f"تم بيع الوحدة {unit_number} بنجاح")
                self.load_units_data()
                self.update_dashboard_stats()

            except Exception as e:
                logger.error(f"خطأ في بيع الوحدة: {e}")
                messagebox.showerror("خطأ", f"فشل في بيع الوحدة:\n{str(e)}")

    def delete_unit(self):
        """حذف وحدة"""
        selection = self.units_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار وحدة للحذف")
            return

        item = self.units_tree.item(selection[0])
        unit_id = item['values'][0]
        unit_number = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف الوحدة {unit_number}؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM units WHERE id = ?", (unit_id,))
                messagebox.showinfo("نجح", "تم حذف الوحدة بنجاح")
                self.load_units_data()
                self.update_dashboard_stats()

            except Exception as e:
                logger.error(f"خطأ في حذف الوحدة: {e}")
                messagebox.showerror("خطأ", f"فشل في حذف الوحدة:\n{str(e)}")

    def view_unit_details(self):
        """عرض تفاصيل الوحدة"""
        selection = self.units_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار وحدة لعرض التفاصيل")
            return

        item = self.units_tree.item(selection[0])
        unit_id = item['values'][0]

        try:
            query = """
                SELECT u.*, p.name as project_name
                FROM units u
                LEFT JOIN projects p ON u.project_id = p.id
                WHERE u.id = ?
            """
            result = sqlite_manager.execute_query(query, (unit_id,))

            if result:
                unit = result[0]
                status_text = {
                    'available': 'متاح',
                    'reserved': 'محجوز',
                    'sold': 'مباع',
                    'maintenance': 'تحت الصيانة'
                }.get(unit['status'], unit['status'])

                details = f"""
تفاصيل الوحدة:

رقم الوحدة: {unit['unit_number']}
المشروع: {unit['project_name'] or 'غير محدد'}
رقم الطابق: {unit['floor_number']}
نوع الوحدة: {unit['unit_type']}
المساحة: {unit['area']:.1f} متر مربع
السعر: {unit['price']:,.0f} ريال
الحالة: {status_text}
الوصف: {unit['description'] or 'لا يوجد'}
تاريخ الإنشاء: {unit['created_at']}
آخر تحديث: {unit['updated_at'] or 'لم يتم التحديث'}
                """
                messagebox.showinfo("تفاصيل الوحدة", details)

        except Exception as e:
            logger.error(f"خطأ في عرض تفاصيل الوحدة: {e}")
            messagebox.showerror("خطأ", f"فشل في عرض التفاصيل:\n{str(e)}")

    def load_units_data(self):
        """تحميل بيانات الوحدات"""
        try:
            # مسح البيانات القديمة
            for item in self.units_tree.get_children():
                self.units_tree.delete(item)

            # تحميل الوحدات مع بيانات المشروع
            query = """
                SELECT u.id, u.unit_number, p.name as project_name, u.floor_number,
                       u.unit_type, u.area, u.price, u.status
                FROM units u
                LEFT JOIN projects p ON u.project_id = p.id
                ORDER BY u.id DESC
            """
            units = sqlite_manager.execute_query(query)

            # إضافة الوحدات إلى الجدول
            for unit in units:
                status_text = {
                    'available': 'متاح',
                    'reserved': 'محجوز',
                    'sold': 'مباع',
                    'maintenance': 'تحت الصيانة'
                }.get(unit['status'], unit['status'])

                area_text = f"{unit['area']:.1f} م²" if unit['area'] else "غير محدد"
                price_text = f"{unit['price']:,.0f} ريال" if unit['price'] else "غير محدد"

                self.units_tree.insert('', 'end', values=(
                    unit['id'],
                    unit['unit_number'] or '',
                    unit['project_name'] or 'غير محدد',
                    unit['unit_type'] or '',
                    area_text,
                    price_text,
                    status_text
                ))

            if hasattr(self, 'status_label'):
                self.update_status(f"تم تحميل {len(units)} وحدة")

        except Exception as e:
            logger.error(f"خطأ في تحميل الوحدات: {e}")
            if hasattr(self, 'status_label'):
                self.update_status(f"خطأ في تحميل الوحدات: {str(e)}")

    def refresh_units(self):
        """تحديث قائمة الوحدات"""
        self.load_units_data()

    # دوال إدارة العقود
    def new_contract(self):
        """عقد جديد"""
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")

    def view_contract(self):
        """عرض العقد"""
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")

    def print_contract(self):
        """طباعة العقد"""
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")

    # دوال إدارة المدفوعات
    def new_payment(self):
        """دفعة جديدة"""
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")

    def view_receipt(self):
        """عرض الإيصال"""
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")

    def print_receipt(self):
        """طباعة إيصال"""
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")

    # دوال المقاولين والمستخلصات
    def add_contractor(self):
        """إضافة مقاول جديد"""
        dialog = ContractorDialog(self.root, "إضافة مقاول جديد")
        result = dialog.run()

        if result:
            try:
                query = """
                    INSERT INTO contractors (name, specialty, phone, email, address, status, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result['name'],
                    result['specialty'],
                    result['phone'],
                    result['email'],
                    result['address'],
                    result['status'],
                    result['notes']
                ))

                messagebox.showinfo("نجح", "تم إضافة المقاول بنجاح")
                self.load_contractors_data()
                self.update_dashboard_stats()

            except Exception as e:
                logger.error(f"خطأ في إضافة المقاول: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة المقاول:\n{str(e)}")

    def edit_contractor(self):
        """تعديل مقاول"""
        selection = self.contractors_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مقاول للتعديل")
            return

        item = self.contractors_tree.item(selection[0])
        contractor_id = item['values'][0]

        try:
            query = """
                SELECT name, specialty, phone, email, address, status, notes
                FROM contractors WHERE id = ?
            """
            result = sqlite_manager.execute_query(query, (contractor_id,))

            if result:
                contractor_data = dict(result[0])
                dialog = ContractorDialog(self.root, "تعديل المقاول", contractor_data)
                updated_data = dialog.run()

                if updated_data:
                    update_query = """
                        UPDATE contractors
                        SET name=?, specialty=?, phone=?, email=?, address=?, status=?, notes=?, updated_at=CURRENT_TIMESTAMP
                        WHERE id=?
                    """
                    sqlite_manager.execute_query(update_query, (
                        updated_data['name'],
                        updated_data['specialty'],
                        updated_data['phone'],
                        updated_data['email'],
                        updated_data['address'],
                        updated_data['status'],
                        updated_data['notes'],
                        contractor_id
                    ))

                    messagebox.showinfo("نجح", "تم تحديث المقاول بنجاح")
                    self.load_contractors_data()

        except Exception as e:
            logger.error(f"خطأ في تعديل المقاول: {e}")
            messagebox.showerror("خطأ", f"فشل في تعديل المقاول:\n{str(e)}")

    def delete_contractor(self):
        """حذف مقاول"""
        selection = self.contractors_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مقاول للحذف")
            return

        item = self.contractors_tree.item(selection[0])
        contractor_id = item['values'][0]
        contractor_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المقاول '{contractor_name}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM contractors WHERE id = ?", (contractor_id,))
                messagebox.showinfo("نجح", "تم حذف المقاول بنجاح")
                self.load_contractors_data()
                self.update_dashboard_stats()

            except Exception as e:
                logger.error(f"خطأ في حذف المقاول: {e}")
                messagebox.showerror("خطأ", f"فشل في حذف المقاول:\n{str(e)}")

    def new_extract(self):
        """إضافة مستخلص جديد"""
        dialog = ExtractDialog(self.root, "إضافة مستخلص جديد")
        result = dialog.run()

        if result:
            try:
                query = """
                    INSERT INTO extracts (extract_number, contractor, project, amount, date, status, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result['extract_number'],
                    result['contractor'],
                    result['project'],
                    result['amount'],
                    result['date'],
                    result['status'],
                    result['description']
                ))

                messagebox.showinfo("نجح", "تم إضافة المستخلص بنجاح")
                self.load_extracts_data()

            except Exception as e:
                logger.error(f"خطأ في إضافة المستخلص: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة المستخلص:\n{str(e)}")

    def approve_extract(self):
        """موافقة على مستخلص"""
        selection = self.extracts_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخلص للموافقة عليه")
            return

        item = self.extracts_tree.item(selection[0])
        extract_id = item['values'][0]
        extract_number = item['values'][1]

        if messagebox.askyesno("تأكيد الموافقة", f"هل أنت متأكد من الموافقة على المستخلص رقم {extract_number}؟"):
            try:
                update_query = """
                    UPDATE extracts
                    SET status='approved', updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """
                sqlite_manager.execute_query(update_query, (extract_id,))

                messagebox.showinfo("نجح", f"تمت الموافقة على المستخلص رقم {extract_number}")
                self.load_extracts_data()

            except Exception as e:
                logger.error(f"خطأ في الموافقة على المستخلص: {e}")
                messagebox.showerror("خطأ", f"فشل في الموافقة على المستخلص:\n{str(e)}")

    def pay_extract(self):
        """دفع مستخلص"""
        selection = self.extracts_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخلص للدفع")
            return

        item = self.extracts_tree.item(selection[0])
        extract_id = item['values'][0]
        extract_number = item['values'][1]
        current_status = item['values'][5]

        if current_status != 'تمت الموافقة':
            messagebox.showwarning("تحذير", "يجب الموافقة على المستخلص أولاً قبل الدفع")
            return

        if messagebox.askyesno("تأكيد الدفع", f"هل أنت متأكد من دفع المستخلص رقم {extract_number}؟"):
            try:
                update_query = """
                    UPDATE extracts
                    SET status='paid', updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """
                sqlite_manager.execute_query(update_query, (extract_id,))

                messagebox.showinfo("نجح", f"تم دفع المستخلص رقم {extract_number}")
                self.load_extracts_data()

            except Exception as e:
                logger.error(f"خطأ في دفع المستخلص: {e}")
                messagebox.showerror("خطأ", f"فشل في دفع المستخلص:\n{str(e)}")

    def load_contractors_data(self):
        """تحميل بيانات المقاولين"""
        try:
            # مسح البيانات القديمة
            for item in self.contractors_tree.get_children():
                self.contractors_tree.delete(item)

            # تحميل المقاولين
            query = """
                SELECT id, name, specialty, phone, email, status
                FROM contractors
                ORDER BY id DESC
            """
            contractors = sqlite_manager.execute_query(query)

            # إضافة المقاولين إلى الجدول
            for contractor in contractors:
                status_text = {
                    'active': 'نشط',
                    'inactive': 'غير نشط',
                    'suspended': 'معلق'
                }.get(contractor['status'], contractor['status'])

                self.contractors_tree.insert('', 'end', values=(
                    contractor['id'],
                    contractor['name'] or '',
                    contractor['specialty'] or '',
                    contractor['phone'] or '',
                    contractor['email'] or '',
                    status_text
                ))

            if hasattr(self, 'status_label'):
                self.update_status(f"تم تحميل {len(contractors)} مقاول")

        except Exception as e:
            logger.error(f"خطأ في تحميل المقاولين: {e}")
            if hasattr(self, 'status_label'):
                self.update_status(f"خطأ في تحميل المقاولين: {str(e)}")

    def load_extracts_data(self):
        """تحميل بيانات المستخلصات"""
        try:
            # مسح البيانات القديمة
            for item in self.extracts_tree.get_children():
                self.extracts_tree.delete(item)

            # تحميل المستخلصات
            query = """
                SELECT id, extract_number, contractor, project, amount, date, status
                FROM extracts
                ORDER BY id DESC
            """
            extracts = sqlite_manager.execute_query(query)

            # إضافة المستخلصات إلى الجدول
            for extract in extracts:
                status_text = {
                    'pending': 'معلق',
                    'approved': 'تمت الموافقة',
                    'paid': 'مدفوع',
                    'rejected': 'مرفوض'
                }.get(extract['status'], extract['status'])

                amount_text = f"{extract['amount']:,.0f} ريال" if extract['amount'] else "غير محدد"

                self.extracts_tree.insert('', 'end', values=(
                    extract['id'],
                    extract['extract_number'] or '',
                    extract['contractor'] or '',
                    extract['project'] or '',
                    amount_text,
                    extract['date'] or '',
                    status_text
                ))

            if hasattr(self, 'status_label'):
                self.update_status(f"تم تحميل {len(extracts)} مستخلص")

        except Exception as e:
            logger.error(f"خطأ في تحميل المستخلصات: {e}")
            if hasattr(self, 'status_label'):
                self.update_status(f"خطأ في تحميل المستخلصات: {str(e)}")

    # دوال الموردين والفواتير
    def add_supplier(self):
        """إضافة مورد جديد"""
        dialog = SupplierDialog(self.root, "إضافة مورد جديد")
        result = dialog.run()

        if result:
            try:
                query = """
                    INSERT INTO suppliers (name, material_type, phone, email, address, status, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result['name'],
                    result['material_type'],
                    result['phone'],
                    result['email'],
                    result['address'],
                    result['status'],
                    result['notes']
                ))

                messagebox.showinfo("نجح", "تم إضافة المورد بنجاح")
                self.load_suppliers_data()
                self.update_dashboard_stats()

            except Exception as e:
                logger.error(f"خطأ في إضافة المورد: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة المورد:\n{str(e)}")

    def edit_supplier(self):
        """تعديل مورد"""
        selection = self.suppliers_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد للتعديل")
            return

        item = self.suppliers_tree.item(selection[0])
        supplier_id = item['values'][0]

        try:
            query = """
                SELECT name, material_type, phone, email, address, status, notes
                FROM suppliers WHERE id = ?
            """
            result = sqlite_manager.execute_query(query, (supplier_id,))

            if result:
                supplier_data = dict(result[0])
                dialog = SupplierDialog(self.root, "تعديل المورد", supplier_data)
                updated_data = dialog.run()

                if updated_data:
                    update_query = """
                        UPDATE suppliers
                        SET name=?, material_type=?, phone=?, email=?, address=?, status=?, notes=?, updated_at=CURRENT_TIMESTAMP
                        WHERE id=?
                    """
                    sqlite_manager.execute_query(update_query, (
                        updated_data['name'],
                        updated_data['material_type'],
                        updated_data['phone'],
                        updated_data['email'],
                        updated_data['address'],
                        updated_data['status'],
                        updated_data['notes'],
                        supplier_id
                    ))

                    messagebox.showinfo("نجح", "تم تحديث المورد بنجاح")
                    self.load_suppliers_data()

        except Exception as e:
            logger.error(f"خطأ في تعديل المورد: {e}")
            messagebox.showerror("خطأ", f"فشل في تعديل المورد:\n{str(e)}")

    def delete_supplier(self):
        """حذف مورد"""
        selection = self.suppliers_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مورد للحذف")
            return

        item = self.suppliers_tree.item(selection[0])
        supplier_id = item['values'][0]
        supplier_name = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المورد '{supplier_name}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM suppliers WHERE id = ?", (supplier_id,))
                messagebox.showinfo("نجح", "تم حذف المورد بنجاح")
                self.load_suppliers_data()
                self.update_dashboard_stats()

            except Exception as e:
                logger.error(f"خطأ في حذف المورد: {e}")
                messagebox.showerror("خطأ", f"فشل في حذف المورد:\n{str(e)}")

    def new_invoice(self):
        """إضافة فاتورة جديدة"""
        dialog = InvoiceDialog(self.root, "إضافة فاتورة جديدة")
        result = dialog.run()

        if result:
            try:
                query = """
                    INSERT INTO invoices (invoice_number, supplier, amount, date, due_date, status, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result['invoice_number'],
                    result['supplier'],
                    result['amount'],
                    result['date'],
                    result['due_date'],
                    result['status'],
                    result['description']
                ))

                messagebox.showinfo("نجح", "تم إضافة الفاتورة بنجاح")
                self.load_invoices_data()

            except Exception as e:
                logger.error(f"خطأ في إضافة الفاتورة: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة الفاتورة:\n{str(e)}")

    def approve_invoice(self):
        """موافقة على فاتورة"""
        selection = self.invoices_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار فاتورة للموافقة عليها")
            return

        item = self.invoices_tree.item(selection[0])
        invoice_id = item['values'][0]
        invoice_number = item['values'][1]

        if messagebox.askyesno("تأكيد الموافقة", f"هل أنت متأكد من الموافقة على الفاتورة رقم {invoice_number}؟"):
            try:
                update_query = """
                    UPDATE invoices
                    SET status='approved', updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """
                sqlite_manager.execute_query(update_query, (invoice_id,))

                messagebox.showinfo("نجح", f"تمت الموافقة على الفاتورة رقم {invoice_number}")
                self.load_invoices_data()

            except Exception as e:
                logger.error(f"خطأ في الموافقة على الفاتورة: {e}")
                messagebox.showerror("خطأ", f"فشل في الموافقة على الفاتورة:\n{str(e)}")

    def pay_invoice(self):
        """دفع فاتورة"""
        selection = self.invoices_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار فاتورة للدفع")
            return

        item = self.invoices_tree.item(selection[0])
        invoice_id = item['values'][0]
        invoice_number = item['values'][1]
        current_status = item['values'][5]

        if current_status != 'تمت الموافقة':
            messagebox.showwarning("تحذير", "يجب الموافقة على الفاتورة أولاً قبل الدفع")
            return

        if messagebox.askyesno("تأكيد الدفع", f"هل أنت متأكد من دفع الفاتورة رقم {invoice_number}؟"):
            try:
                update_query = """
                    UPDATE invoices
                    SET status='paid', updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """
                sqlite_manager.execute_query(update_query, (invoice_id,))

                messagebox.showinfo("نجح", f"تم دفع الفاتورة رقم {invoice_number}")
                self.load_invoices_data()

            except Exception as e:
                logger.error(f"خطأ في دفع الفاتورة: {e}")
                messagebox.showerror("خطأ", f"فشل في دفع الفاتورة:\n{str(e)}")

    def load_suppliers_data(self):
        """تحميل بيانات الموردين"""
        try:
            # مسح البيانات القديمة
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)

            # تحميل الموردين
            query = """
                SELECT id, name, material_type, phone, email, status
                FROM suppliers
                ORDER BY id DESC
            """
            suppliers = sqlite_manager.execute_query(query)

            # إضافة الموردين إلى الجدول
            for supplier in suppliers:
                status_text = {
                    'active': 'نشط',
                    'inactive': 'غير نشط',
                    'suspended': 'معلق'
                }.get(supplier['status'], supplier['status'])

                self.suppliers_tree.insert('', 'end', values=(
                    supplier['id'],
                    supplier['name'] or '',
                    supplier['material_type'] or '',
                    supplier['phone'] or '',
                    supplier['email'] or '',
                    status_text
                ))

            if hasattr(self, 'status_label'):
                self.update_status(f"تم تحميل {len(suppliers)} مورد")

        except Exception as e:
            logger.error(f"خطأ في تحميل الموردين: {e}")
            if hasattr(self, 'status_label'):
                self.update_status(f"خطأ في تحميل الموردين: {str(e)}")

    def load_invoices_data(self):
        """تحميل بيانات الفواتير"""
        try:
            # مسح البيانات القديمة
            for item in self.invoices_tree.get_children():
                self.invoices_tree.delete(item)

            # تحميل الفواتير
            query = """
                SELECT id, invoice_number, supplier, amount, date, due_date, status
                FROM invoices
                ORDER BY id DESC
            """
            invoices = sqlite_manager.execute_query(query)

            # إضافة الفواتير إلى الجدول
            for invoice in invoices:
                status_text = {
                    'pending': 'معلقة',
                    'approved': 'تمت الموافقة',
                    'paid': 'مدفوعة',
                    'rejected': 'مرفوضة'
                }.get(invoice['status'], invoice['status'])

                amount_text = f"{invoice['amount']:,.0f} ريال" if invoice['amount'] else "غير محدد"

                self.invoices_tree.insert('', 'end', values=(
                    invoice['id'],
                    invoice['invoice_number'] or '',
                    invoice['supplier'] or '',
                    amount_text,
                    invoice['date'] or '',
                    invoice['due_date'] or '',
                    status_text
                ))

            if hasattr(self, 'status_label'):
                self.update_status(f"تم تحميل {len(invoices)} فاتورة")

        except Exception as e:
            logger.error(f"خطأ في تحميل الفواتير: {e}")
            if hasattr(self, 'status_label'):
                self.update_status(f"خطأ في تحميل الفواتير: {str(e)}")

    # دوال المشتريات
    def new_purchase_request(self):
        """إضافة طلب شراء جديد"""
        dialog = PurchaseRequestDialog(self.root, "إضافة طلب شراء جديد")
        result = dialog.run()

        if result:
            try:
                query = """
                    INSERT INTO purchase_requests (request_number, project, materials, quantity,
                                                 estimated_cost, request_date, status, notes, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result['request_number'],
                    result['project'],
                    result['materials'],
                    result['quantity'],
                    result['estimated_cost'],
                    result['request_date'],
                    result['status'],
                    result['notes'],
                    self.user_data['id']  # المستخدم الحالي
                ))

                messagebox.showinfo("نجح", "تم إضافة طلب الشراء بنجاح")
                self.load_purchase_requests_data()

            except Exception as e:
                logger.error(f"خطأ في إضافة طلب الشراء: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة طلب الشراء:\n{str(e)}")

    def approve_purchase(self):
        """موافقة على طلب شراء"""
        selection = self.purchases_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار طلب شراء للموافقة عليه")
            return

        item = self.purchases_tree.item(selection[0])
        request_id = item['values'][0]
        request_number = item['values'][1]

        if messagebox.askyesno("تأكيد الموافقة", f"هل أنت متأكد من الموافقة على طلب الشراء رقم {request_number}؟"):
            try:
                update_query = """
                    UPDATE purchase_requests
                    SET status='approved', updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """
                sqlite_manager.execute_query(update_query, (request_id,))

                messagebox.showinfo("نجح", f"تمت الموافقة على طلب الشراء رقم {request_number}")
                self.load_purchase_requests_data()

            except Exception as e:
                logger.error(f"خطأ في الموافقة على طلب الشراء: {e}")
                messagebox.showerror("خطأ", f"فشل في الموافقة على طلب الشراء:\n{str(e)}")

    def reject_purchase(self):
        """رفض طلب شراء"""
        selection = self.purchases_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار طلب شراء لرفضه")
            return

        item = self.purchases_tree.item(selection[0])
        request_id = item['values'][0]
        request_number = item['values'][1]

        if messagebox.askyesno("تأكيد الرفض", f"هل أنت متأكد من رفض طلب الشراء رقم {request_number}؟"):
            try:
                update_query = """
                    UPDATE purchase_requests
                    SET status='rejected', updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """
                sqlite_manager.execute_query(update_query, (request_id,))

                messagebox.showinfo("نجح", f"تم رفض طلب الشراء رقم {request_number}")
                self.load_purchase_requests_data()

            except Exception as e:
                logger.error(f"خطأ في رفض طلب الشراء: {e}")
                messagebox.showerror("خطأ", f"فشل في رفض طلب الشراء:\n{str(e)}")

    def execute_purchase(self):
        """تنفيذ طلب شراء"""
        selection = self.purchases_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار طلب شراء للتنفيذ")
            return

        item = self.purchases_tree.item(selection[0])
        request_id = item['values'][0]
        request_number = item['values'][1]
        current_status = item['values'][6]

        if current_status != 'تمت الموافقة':
            messagebox.showwarning("تحذير", "يجب الموافقة على طلب الشراء أولاً قبل التنفيذ")
            return

        if messagebox.askyesno("تأكيد التنفيذ", f"هل أنت متأكد من تنفيذ طلب الشراء رقم {request_number}؟"):
            try:
                update_query = """
                    UPDATE purchase_requests
                    SET status='executed', updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """
                sqlite_manager.execute_query(update_query, (request_id,))

                messagebox.showinfo("نجح", f"تم تنفيذ طلب الشراء رقم {request_number}")
                self.load_purchase_requests_data()

            except Exception as e:
                logger.error(f"خطأ في تنفيذ طلب الشراء: {e}")
                messagebox.showerror("خطأ", f"فشل في تنفيذ طلب الشراء:\n{str(e)}")

    def edit_purchase_request(self):
        """تعديل طلب شراء"""
        selection = self.purchases_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار طلب شراء للتعديل")
            return

        item = self.purchases_tree.item(selection[0])
        request_id = item['values'][0]

        try:
            query = """
                SELECT request_number, project, materials, quantity, estimated_cost,
                       request_date, status, notes
                FROM purchase_requests WHERE id = ?
            """
            result = sqlite_manager.execute_query(query, (request_id,))

            if result:
                request_data = dict(result[0])
                dialog = PurchaseRequestDialog(self.root, "تعديل طلب الشراء", request_data)
                updated_data = dialog.run()

                if updated_data:
                    update_query = """
                        UPDATE purchase_requests
                        SET request_number=?, project=?, materials=?, quantity=?,
                            estimated_cost=?, request_date=?, status=?, notes=?, updated_at=CURRENT_TIMESTAMP
                        WHERE id=?
                    """
                    sqlite_manager.execute_query(update_query, (
                        updated_data['request_number'],
                        updated_data['project'],
                        updated_data['materials'],
                        updated_data['quantity'],
                        updated_data['estimated_cost'],
                        updated_data['request_date'],
                        updated_data['status'],
                        updated_data['notes'],
                        request_id
                    ))

                    messagebox.showinfo("نجح", "تم تحديث طلب الشراء بنجاح")
                    self.load_purchase_requests_data()

        except Exception as e:
            logger.error(f"خطأ في تعديل طلب الشراء: {e}")
            messagebox.showerror("خطأ", f"فشل في تعديل طلب الشراء:\n{str(e)}")

    def load_purchase_requests_data(self):
        """تحميل بيانات طلبات الشراء"""
        try:
            # مسح البيانات القديمة
            for item in self.purchases_tree.get_children():
                self.purchases_tree.delete(item)

            # تحميل طلبات الشراء
            query = """
                SELECT id, request_number, project, materials, estimated_cost, request_date, status
                FROM purchase_requests
                ORDER BY id DESC
            """
            requests = sqlite_manager.execute_query(query)

            # إضافة الطلبات إلى الجدول
            for request in requests:
                status_text = {
                    'pending': 'معلق',
                    'approved': 'تمت الموافقة',
                    'rejected': 'مرفوض',
                    'executed': 'تم التنفيذ'
                }.get(request['status'], request['status'])

                cost_text = f"{request['estimated_cost']:,.0f} ريال" if request['estimated_cost'] else "غير محدد"

                self.purchases_tree.insert('', 'end', values=(
                    request['id'],
                    request['request_number'] or '',
                    request['project'] or '',
                    request['materials'] or '',
                    cost_text,
                    request['request_date'] or '',
                    status_text
                ))

            if hasattr(self, 'status_label'):
                self.update_status(f"تم تحميل {len(requests)} طلب شراء")

        except Exception as e:
            logger.error(f"خطأ في تحميل طلبات الشراء: {e}")
            if hasattr(self, 'status_label'):
                self.update_status(f"خطأ في تحميل طلبات الشراء: {str(e)}")

    # دوال الصيانة والتشغيل
    def new_maintenance_task(self):
        """إضافة مهمة صيانة جديدة"""
        dialog = MaintenanceTaskDialog(self.root, "إضافة مهمة صيانة جديدة")
        result = dialog.run()

        if result:
            try:
                query = """
                    INSERT INTO maintenance_tasks (task_number, maintenance_type, location,
                                                 technician, date, cost, status, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result['task_number'],
                    result['maintenance_type'],
                    result['location'],
                    result['technician'],
                    result['date'],
                    result['cost'],
                    result['status'],
                    result['description']
                ))

                messagebox.showinfo("نجح", "تم إضافة مهمة الصيانة بنجاح")
                self.load_maintenance_tasks_data()

            except Exception as e:
                logger.error(f"خطأ في إضافة مهمة الصيانة: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة مهمة الصيانة:\n{str(e)}")

    def assign_technician(self):
        """تعيين فني لمهمة صيانة"""
        selection = self.maintenance_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة صيانة لتعيين فني لها")
            return

        item = self.maintenance_tree.item(selection[0])
        task_id = item['values'][0]
        task_number = item['values'][1]

        # نافذة إدخال اسم الفني
        technician_name = simpledialog.askstring("تعيين فني", f"أدخل اسم الفني لمهمة الصيانة {task_number}:")

        if technician_name:
            try:
                update_query = """
                    UPDATE maintenance_tasks
                    SET technician=?, status='in_progress', updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """
                sqlite_manager.execute_query(update_query, (technician_name, task_id))

                messagebox.showinfo("نجح", f"تم تعيين الفني {technician_name} لمهمة الصيانة {task_number}")
                self.load_maintenance_tasks_data()

            except Exception as e:
                logger.error(f"خطأ في تعيين الفني: {e}")
                messagebox.showerror("خطأ", f"فشل في تعيين الفني:\n{str(e)}")

    def complete_maintenance(self):
        """إكمال مهمة صيانة"""
        selection = self.maintenance_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة صيانة لإكمالها")
            return

        item = self.maintenance_tree.item(selection[0])
        task_id = item['values'][0]
        task_number = item['values'][1]
        current_status = item['values'][5]

        if current_status == 'مكتملة':
            messagebox.showinfo("معلومة", f"مهمة الصيانة {task_number} مكتملة بالفعل")
            return

        if messagebox.askyesno("تأكيد الإكمال", f"هل أنت متأكد من إكمال مهمة الصيانة {task_number}؟"):
            try:
                update_query = """
                    UPDATE maintenance_tasks
                    SET status='completed', updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """
                sqlite_manager.execute_query(update_query, (task_id,))

                messagebox.showinfo("نجح", f"تم إكمال مهمة الصيانة {task_number}")
                self.load_maintenance_tasks_data()

            except Exception as e:
                logger.error(f"خطأ في إكمال مهمة الصيانة: {e}")
                messagebox.showerror("خطأ", f"فشل في إكمال مهمة الصيانة:\n{str(e)}")

    def edit_maintenance_task(self):
        """تعديل مهمة صيانة"""
        selection = self.maintenance_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة صيانة للتعديل")
            return

        item = self.maintenance_tree.item(selection[0])
        task_id = item['values'][0]

        try:
            query = """
                SELECT task_number, maintenance_type, location, technician,
                       date, cost, status, description
                FROM maintenance_tasks WHERE id = ?
            """
            result = sqlite_manager.execute_query(query, (task_id,))

            if result:
                task_data = dict(result[0])
                dialog = MaintenanceTaskDialog(self.root, "تعديل مهمة الصيانة", task_data)
                updated_data = dialog.run()

                if updated_data:
                    update_query = """
                        UPDATE maintenance_tasks
                        SET task_number=?, maintenance_type=?, location=?, technician=?,
                            date=?, cost=?, status=?, description=?, updated_at=CURRENT_TIMESTAMP
                        WHERE id=?
                    """
                    sqlite_manager.execute_query(update_query, (
                        updated_data['task_number'],
                        updated_data['maintenance_type'],
                        updated_data['location'],
                        updated_data['technician'],
                        updated_data['date'],
                        updated_data['cost'],
                        updated_data['status'],
                        updated_data['description'],
                        task_id
                    ))

                    messagebox.showinfo("نجح", "تم تحديث مهمة الصيانة بنجاح")
                    self.load_maintenance_tasks_data()

        except Exception as e:
            logger.error(f"خطأ في تعديل مهمة الصيانة: {e}")
            messagebox.showerror("خطأ", f"فشل في تعديل مهمة الصيانة:\n{str(e)}")

    def delete_maintenance_task(self):
        """حذف مهمة صيانة"""
        selection = self.maintenance_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة صيانة للحذف")
            return

        item = self.maintenance_tree.item(selection[0])
        task_id = item['values'][0]
        task_number = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف مهمة الصيانة '{task_number}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM maintenance_tasks WHERE id = ?", (task_id,))
                messagebox.showinfo("نجح", "تم حذف مهمة الصيانة بنجاح")
                self.load_maintenance_tasks_data()

            except Exception as e:
                logger.error(f"خطأ في حذف مهمة الصيانة: {e}")
                messagebox.showerror("خطأ", f"فشل في حذف مهمة الصيانة:\n{str(e)}")

    def load_maintenance_tasks_data(self):
        """تحميل بيانات مهام الصيانة"""
        try:
            # مسح البيانات القديمة
            for item in self.maintenance_tree.get_children():
                self.maintenance_tree.delete(item)

            # تحميل مهام الصيانة
            query = """
                SELECT id, task_number, maintenance_type, location, technician, date, status
                FROM maintenance_tasks
                ORDER BY id DESC
            """
            tasks = sqlite_manager.execute_query(query)

            # إضافة المهام إلى الجدول
            for task in tasks:
                status_text = {
                    'pending': 'معلقة',
                    'in_progress': 'قيد التنفيذ',
                    'completed': 'مكتملة',
                    'cancelled': 'ملغاة'
                }.get(task['status'], task['status'])

                self.maintenance_tree.insert('', 'end', values=(
                    task['id'],
                    task['task_number'] or '',
                    task['maintenance_type'] or '',
                    task['location'] or '',
                    task['technician'] or 'غير محدد',
                    task['date'] or '',
                    status_text
                ))

            if hasattr(self, 'status_label'):
                self.update_status(f"تم تحميل {len(tasks)} مهمة صيانة")

        except Exception as e:
            logger.error(f"خطأ في تحميل مهام الصيانة: {e}")
            if hasattr(self, 'status_label'):
                self.update_status(f"خطأ في تحميل مهام الصيانة: {str(e)}")

    def maintenance_cost_report(self):
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")

    # دوال المهام اليومية
    def new_daily_task(self):
        """إضافة مهمة يومية جديدة"""
        dialog = DailyTaskDialog(self.root, "إضافة مهمة يومية جديدة")
        result = dialog.run()

        if result:
            try:
                query = """
                    INSERT INTO daily_tasks (title, description, assigned_to, priority,
                                           due_date, status, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result['title'],
                    result['description'],
                    result['assigned_to'],
                    result['priority'],
                    result['due_date'],
                    result['status'],
                    self.user_data['id']  # المستخدم الحالي
                ))

                messagebox.showinfo("نجح", "تم إضافة المهمة اليومية بنجاح")
                self.load_daily_tasks_data()

            except Exception as e:
                logger.error(f"خطأ في إضافة المهمة اليومية: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة المهمة اليومية:\n{str(e)}")

    def edit_daily_task(self):
        """تعديل مهمة يومية"""
        selection = self.daily_tasks_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة للتعديل")
            return

        item = self.daily_tasks_tree.item(selection[0])
        task_id = item['values'][0]

        try:
            query = """
                SELECT title, description, assigned_to, priority, due_date, status
                FROM daily_tasks WHERE id = ?
            """
            result = sqlite_manager.execute_query(query, (task_id,))

            if result:
                task_data = dict(result[0])
                dialog = DailyTaskDialog(self.root, "تعديل المهمة اليومية", task_data)
                updated_data = dialog.run()

                if updated_data:
                    update_query = """
                        UPDATE daily_tasks
                        SET title=?, description=?, assigned_to=?, priority=?,
                            due_date=?, status=?, updated_at=CURRENT_TIMESTAMP
                        WHERE id=?
                    """
                    sqlite_manager.execute_query(update_query, (
                        updated_data['title'],
                        updated_data['description'],
                        updated_data['assigned_to'],
                        updated_data['priority'],
                        updated_data['due_date'],
                        updated_data['status'],
                        task_id
                    ))

                    messagebox.showinfo("نجح", "تم تحديث المهمة اليومية بنجاح")
                    self.load_daily_tasks_data()

        except Exception as e:
            logger.error(f"خطأ في تعديل المهمة اليومية: {e}")
            messagebox.showerror("خطأ", f"فشل في تعديل المهمة اليومية:\n{str(e)}")

    def complete_daily_task(self):
        """إكمال مهمة يومية"""
        selection = self.daily_tasks_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة لإكمالها")
            return

        item = self.daily_tasks_tree.item(selection[0])
        task_id = item['values'][0]
        task_title = item['values'][1]
        current_status = item['values'][5]

        if current_status == 'مكتملة':
            messagebox.showinfo("معلومة", f"المهمة '{task_title}' مكتملة بالفعل")
            return

        if messagebox.askyesno("تأكيد الإكمال", f"هل أنت متأكد من إكمال المهمة '{task_title}'؟"):
            try:
                update_query = """
                    UPDATE daily_tasks
                    SET status='completed', updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """
                sqlite_manager.execute_query(update_query, (task_id,))

                messagebox.showinfo("نجح", f"تم إكمال المهمة '{task_title}'")
                self.load_daily_tasks_data()

            except Exception as e:
                logger.error(f"خطأ في إكمال المهمة: {e}")
                messagebox.showerror("خطأ", f"فشل في إكمال المهمة:\n{str(e)}")

    def delete_daily_task(self):
        """حذف مهمة يومية"""
        selection = self.daily_tasks_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة للحذف")
            return

        item = self.daily_tasks_tree.item(selection[0])
        task_id = item['values'][0]
        task_title = item['values'][1]

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المهمة '{task_title}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM daily_tasks WHERE id = ?", (task_id,))
                messagebox.showinfo("نجح", "تم حذف المهمة اليومية بنجاح")
                self.load_daily_tasks_data()

            except Exception as e:
                logger.error(f"خطأ في حذف المهمة اليومية: {e}")
                messagebox.showerror("خطأ", f"فشل في حذف المهمة اليومية:\n{str(e)}")

    def apply_task_filter(self):
        """تطبيق فلتر على المهام"""
        messagebox.showinfo("قريباً", "سيتم تطوير هذه الميزة قريباً")

    def load_daily_tasks_data(self):
        """تحميل بيانات المهام اليومية"""
        try:
            # مسح البيانات القديمة
            for item in self.daily_tasks_tree.get_children():
                self.daily_tasks_tree.delete(item)

            # تحميل المهام اليومية
            query = """
                SELECT id, title, assigned_to, priority, due_date, status
                FROM daily_tasks
                ORDER BY id DESC
            """
            tasks = sqlite_manager.execute_query(query)

            # إضافة المهام إلى الجدول
            for task in tasks:
                status_text = {
                    'pending': 'معلقة',
                    'in_progress': 'قيد التنفيذ',
                    'completed': 'مكتملة',
                    'cancelled': 'ملغاة'
                }.get(task['status'], task['status'])

                priority_text = {
                    'low': 'منخفضة',
                    'medium': 'متوسطة',
                    'high': 'عالية',
                    'urgent': 'عاجلة'
                }.get(task['priority'], task['priority'])

                self.daily_tasks_tree.insert('', 'end', values=(
                    task['id'],
                    task['title'] or '',
                    task['assigned_to'] or 'غير محدد',
                    priority_text,
                    task['due_date'] or '',
                    status_text
                ))

            if hasattr(self, 'status_label'):
                self.update_status(f"تم تحميل {len(tasks)} مهمة يومية")

        except Exception as e:
            logger.error(f"خطأ في تحميل المهام اليومية: {e}")
            if hasattr(self, 'status_label'):
                self.update_status(f"خطأ في تحميل المهام اليومية: {str(e)}")

    # دوال التقارير
    def generate_projects_report(self):
        """إنشاء تقرير المشاريع"""
        try:
            report = f"""
═══════════════════════════════════════════════════════════════════
                            تقرير المشاريع العقارية
═══════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
👤 المستخدم: {self.user_data['full_name']}

📊 ملخص المشاريع:
────────────────────────────────────────────────────────────────────

🏗️  إجمالي المشاريع: قيد التطوير
⚡ المشاريع النشطة: قيد التطوير
✅ المشاريع المكتملة: قيد التطوير
⏸️  المشاريع المتوقفة: قيد التطوير

💰 الإحصائيات المالية:
────────────────────────────────────────────────────────────────────
• إجمالي قيمة المشاريع: قيد التطوير
• المبلغ المنفق: قيد التطوير
• المبلغ المتبقي: قيد التطوير

📈 نسب الإنجاز:
────────────────────────────────────────────────────────────────────
• متوسط نسبة الإنجاز: قيد التطوير

💡 ملاحظة: سيتم تطوير التقارير التفصيلية في النسخة القادمة

═══════════════════════════════════════════════════════════════════
            """

            self.report_text.delete('1.0', tk.END)
            self.report_text.insert('1.0', report)

        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير المشاريع: {e}")
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير:\n{str(e)}")

    def generate_sales_report(self):
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير المبيعات قريباً")

    def generate_contractors_report(self):
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير المقاولين قريباً")

    def generate_suppliers_report(self):
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير الموردين قريباً")

    def generate_purchases_report(self):
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير المشتريات قريباً")

    def generate_maintenance_report(self):
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير الصيانة قريباً")

    def generate_tasks_report(self):
        messagebox.showinfo("قريباً", "سيتم تطوير تقرير المهام قريباً")

    def generate_comprehensive_report(self):
        """إنشاء التقرير الشامل"""
        try:
            report = f"""
═══════════════════════════════════════════════════════════════════
                        التقرير الشامل المفصل
                    نظام شركة رافع للتطوير العقاري
═══════════════════════════════════════════════════════════════════

📅 تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
👤 المستخدم: {self.user_data['full_name']}

🎯 نظرة عامة على النظام:
────────────────────────────────────────────────────────────────────

✅ الوحدات المتاحة:
   • 🏠 لوحة التحكم التفاعلية
   • 🏗️ إدارة المشاريع العقارية
   • 🏠 مبيعات الشقق والوحدات
   • 👷 المقاولون والمستخلصات
   • 📦 الموردون والفواتير
   • 🛒 إدارة المشتريات
   • 🔧 الصيانة والتشغيل
   • 📋 المهام اليومية
   • 📊 التقارير المتقدمة

🔧 الميزات المتقدمة:
────────────────────────────────────────────────────────────────────
   ✅ واجهة عربية كاملة مع دعم RTL
   ✅ قاعدة بيانات SQLite محلية
   ✅ نظام أمان متكامل
   ✅ تقارير قابلة للطباعة والتصدير
   ✅ فلترة وبحث متقدم
   ✅ إدارة متعددة المستخدمين

📊 حالة النظام:
────────────────────────────────────────────────────────────────────
   🟢 قاعدة البيانات: متصلة وتعمل
   🟢 نظام الأمان: مفعل
   🟢 السجلات: تعمل بشكل طبيعي
   🟡 التقارير: قيد التطوير
   🟡 الوحدات المتقدمة: قيد التطوير

💡 ملاحظات:
────────────────────────────────────────────────────────────────────
• تم إنشاء الهيكل الأساسي لجميع الوحدات
• جاري تطوير الوظائف التفصيلية لكل وحدة
• النظام جاهز للاستخدام الأساسي

═══════════════════════════════════════════════════════════════════
                            انتهى التقرير
═══════════════════════════════════════════════════════════════════
            """

            self.report_text.delete('1.0', tk.END)
            self.report_text.insert('1.0', report)

        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير الشامل: {e}")
            messagebox.showerror("خطأ", f"فشل في إنشاء التقرير:\n{str(e)}")

    # دوال التقارير المساعدة
    def print_report(self):
        messagebox.showinfo("قريباً", "سيتم تطوير ميزة الطباعة قريباً")

    def export_pdf(self):
        messagebox.showinfo("قريباً", "سيتم تطوير تصدير PDF قريباً")

    def export_excel(self):
        messagebox.showinfo("قريباً", "سيتم تطوير تصدير Excel قريباً")

    def clear_report(self):
        """مسح التقرير"""
        self.report_text.delete('1.0', tk.END)
        self.show_default_report()

    def create_users_content(self):
        """إنشاء محتوى إدارة المستخدمين"""
        # شريط الأدوات
        toolbar = tk.Frame(self.users_frame, bg='white')
        toolbar.pack(fill='x', padx=10, pady=10)

        tk.Button(toolbar, text="إضافة مستخدم", bg='#27ae60', fg='white', command=self.add_user).pack(side='left', padx=5)
        tk.Button(toolbar, text="تعديل", bg='#3498db', fg='white', command=self.edit_user).pack(side='left', padx=5)
        tk.Button(toolbar, text="حذف", bg='#e74c3c', fg='white', command=self.delete_user).pack(side='left', padx=5)
        tk.Button(toolbar, text="تفعيل/إلغاء تفعيل", bg='#f39c12', fg='white', command=self.toggle_user_status).pack(side='left', padx=5)
        tk.Button(toolbar, text="تحديث", bg='#95a5a6', fg='white', command=self.refresh_users).pack(side='right', padx=5)

        # جدول المستخدمين
        columns = ('ID', 'اسم المستخدم', 'الاسم الكامل', 'البريد الإلكتروني', 'النوع', 'الحالة')
        self.users_tree = ttk.Treeview(self.users_frame, columns=columns, show='headings', height=15)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=150)

        # شريط التمرير
        users_scrollbar = ttk.Scrollbar(self.users_frame, orient='vertical', command=self.users_tree.yview)
        self.users_tree.configure(yscrollcommand=users_scrollbar.set)

        # تعبئة الجدول
        self.users_tree.pack(side='left', fill='both', expand=True, padx=(10, 0), pady=10)
        users_scrollbar.pack(side='right', fill='y', padx=(0, 10), pady=10)

    # دوال إدارة المستخدمين
    def add_user(self):
        """إضافة مستخدم جديد"""
        dialog = UserDialog(self.root, "إضافة مستخدم جديد")
        result = dialog.run()

        if result:
            try:
                # تشفير كلمة المرور
                import bcrypt
                password_hash = bcrypt.hashpw(result['password'].encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

                query = """
                    INSERT INTO users (username, password_hash, full_name, email, user_type, is_active)
                    VALUES (?, ?, ?, ?, ?, ?)
                """
                sqlite_manager.execute_query(query, (
                    result['username'],
                    password_hash,
                    result['full_name'],
                    result['email'],
                    result['user_type'],
                    result['is_active']
                ))

                messagebox.showinfo("نجح", "تم إضافة المستخدم بنجاح")
                self.load_users_data()

            except Exception as e:
                logger.error(f"خطأ في إضافة المستخدم: {e}")
                messagebox.showerror("خطأ", f"فشل في إضافة المستخدم:\n{str(e)}")

    def edit_user(self):
        """تعديل مستخدم"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للتعديل")
            return

        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]

        try:
            query = """
                SELECT username, full_name, email, user_type, is_active
                FROM users WHERE id = ?
            """
            result = sqlite_manager.execute_query(query, (user_id,))

            if result:
                user_data = dict(result[0])
                dialog = UserDialog(self.root, "تعديل المستخدم", user_data)
                updated_data = dialog.run()

                if updated_data:
                    # إعداد الاستعلام
                    if updated_data['password']:  # إذا تم إدخال كلمة مرور جديدة
                        import bcrypt
                        password_hash = bcrypt.hashpw(updated_data['password'].encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                        update_query = """
                            UPDATE users
                            SET username=?, password_hash=?, full_name=?, email=?, user_type=?, is_active=?, updated_at=CURRENT_TIMESTAMP
                            WHERE id=?
                        """
                        params = (
                            updated_data['username'],
                            password_hash,
                            updated_data['full_name'],
                            updated_data['email'],
                            updated_data['user_type'],
                            updated_data['is_active'],
                            user_id
                        )
                    else:  # بدون تغيير كلمة المرور
                        update_query = """
                            UPDATE users
                            SET username=?, full_name=?, email=?, user_type=?, is_active=?, updated_at=CURRENT_TIMESTAMP
                            WHERE id=?
                        """
                        params = (
                            updated_data['username'],
                            updated_data['full_name'],
                            updated_data['email'],
                            updated_data['user_type'],
                            updated_data['is_active'],
                            user_id
                        )

                    sqlite_manager.execute_query(update_query, params)
                    messagebox.showinfo("نجح", "تم تحديث المستخدم بنجاح")
                    self.load_users_data()

        except Exception as e:
            logger.error(f"خطأ في تعديل المستخدم: {e}")
            messagebox.showerror("خطأ", f"فشل في تعديل المستخدم:\n{str(e)}")

    def delete_user(self):
        """حذف مستخدم"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للحذف")
            return

        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]
        username = item['values'][1]

        # منع حذف المستخدم الحالي
        if str(user_id) == str(self.user_data['id']):
            messagebox.showwarning("تحذير", "لا يمكن حذف المستخدم الحالي")
            return

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المستخدم '{username}'؟\nهذا الإجراء لا يمكن التراجع عنه."):
            try:
                sqlite_manager.execute_query("DELETE FROM users WHERE id = ?", (user_id,))
                messagebox.showinfo("نجح", "تم حذف المستخدم بنجاح")
                self.load_users_data()

            except Exception as e:
                logger.error(f"خطأ في حذف المستخدم: {e}")
                messagebox.showerror("خطأ", f"فشل في حذف المستخدم:\n{str(e)}")

    def toggle_user_status(self):
        """تفعيل/إلغاء تفعيل مستخدم"""
        selection = self.users_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم لتغيير حالته")
            return

        item = self.users_tree.item(selection[0])
        user_id = item['values'][0]
        username = item['values'][1]
        current_status = item['values'][5]

        # منع إلغاء تفعيل المستخدم الحالي
        if str(user_id) == str(self.user_data['id']):
            messagebox.showwarning("تحذير", "لا يمكن إلغاء تفعيل المستخدم الحالي")
            return

        new_status = not (current_status == 'مفعل')
        status_text = "تفعيل" if new_status else "إلغاء تفعيل"

        if messagebox.askyesno("تأكيد التغيير", f"هل أنت متأكد من {status_text} المستخدم '{username}'؟"):
            try:
                update_query = """
                    UPDATE users
                    SET is_active=?, updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """
                sqlite_manager.execute_query(update_query, (new_status, user_id))

                messagebox.showinfo("نجح", f"تم {status_text} المستخدم '{username}' بنجاح")
                self.load_users_data()

            except Exception as e:
                logger.error(f"خطأ في تغيير حالة المستخدم: {e}")
                messagebox.showerror("خطأ", f"فشل في تغيير حالة المستخدم:\n{str(e)}")

    def refresh_users(self):
        """تحديث قائمة المستخدمين"""
        self.load_users_data()

    def load_users_data(self):
        """تحميل بيانات المستخدمين"""
        try:
            # مسح البيانات القديمة
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            # تحميل المستخدمين
            query = """
                SELECT id, username, full_name, email, user_type, is_active
                FROM users
                ORDER BY id DESC
            """
            users = sqlite_manager.execute_query(query)

            # إضافة المستخدمين إلى الجدول
            for user in users:
                status_text = 'مفعل' if user['is_active'] else 'غير مفعل'
                user_type_text = {
                    'admin': 'مدير',
                    'manager': 'مدير قسم',
                    'employee': 'موظف',
                    'viewer': 'مشاهد'
                }.get(user['user_type'], user['user_type'])

                self.users_tree.insert('', 'end', values=(
                    user['id'],
                    user['username'] or '',
                    user['full_name'] or '',
                    user['email'] or '',
                    user_type_text,
                    status_text
                ))

            if hasattr(self, 'status_label'):
                self.update_status(f"تم تحميل {len(users)} مستخدم")

        except Exception as e:
            logger.error(f"خطأ في تحميل المستخدمين: {e}")
            if hasattr(self, 'status_label'):
                self.update_status(f"خطأ في تحميل المستخدمين: {str(e)}")

    def logout(self):
        """تسجيل الخروج"""
        if messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟"):
            logger.info(f"تم تسجيل خروج المستخدم: {self.user_data['username']}")
            self.root.quit()

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🏢 النظام الشامل لإدارة شركة رافع للتطوير العقاري")
    print("   Complete Rafea Real Estate Management System")
    print("=" * 70)

    # إعداد نظام السجلات
    setup_logger()

    try:
        # اختبار قاعدة البيانات
        if not sqlite_manager.test_connection():
            print("❌ فشل في الاتصال بقاعدة البيانات")
            print("يرجى تشغيل setup_sqlite.py أولاً")
            return 1

        print("✅ تم الاتصال بقاعدة البيانات بنجاح")

        # تشغيل نافذة تسجيل الدخول
        login_window = LoginWindow()
        user_data = login_window.run()

        if not user_data:
            print("تم إلغاء تسجيل الدخول")
            return 0

        # تشغيل النظام الشامل
        complete_system = RafeaCompleteSystem(user_data)
        complete_system.run()

        return 0

    except Exception as e:
        logger.error(f"خطأ في تشغيل النظام: {e}")
        print(f"❌ خطأ في تشغيل النظام: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
