#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حفظ البيانات في جميع النوافذ
Test Data Saving in All Dialogs
"""

import tkinter as tk
from dialogs import (
    UserDialog, ProjectDialog, CustomerDialog, UnitDialog,
    ContractorDialog, SupplierDialog, ExtractDialog, InvoiceDialog
)

class DataSavingTester:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("اختبار حفظ البيانات")
        self.root.geometry("900x700")
        self.root.configure(bg='#34495e')
        
        self.test_results = {}
        self.create_widgets()
        
    def create_widgets(self):
        """إنشاء واجهة الاختبار"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="🧪 اختبار حفظ البيانات في جميع النوافذ",
            font=('Arial', 24, 'bold'),
            bg='#34495e',
            fg='#ecf0f1'
        )
        title_label.pack(pady=30)
        
        # وصف
        desc_label = tk.Label(
            self.root,
            text="اختبار شامل لضمان حفظ البيانات بنجاح في جميع النوافذ",
            font=('Arial', 16),
            bg='#34495e',
            fg='#bdc3c7'
        )
        desc_label.pack(pady=10)
        
        # إطار النتائج
        self.results_frame = tk.Frame(self.root, bg='#2c3e50', relief='raised', bd=2)
        self.results_frame.pack(fill='both', expand=True, padx=40, pady=20)
        
        # عنوان النتائج
        results_title = tk.Label(
            self.results_frame,
            text="📊 نتائج الاختبار",
            font=('Arial', 18, 'bold'),
            bg='#2c3e50',
            fg='#ecf0f1'
        )
        results_title.pack(pady=20)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.root, bg='#34495e')
        buttons_frame.pack(fill='x', padx=40, pady=20)
        
        # قائمة النوافذ للاختبار
        test_dialogs = [
            ("👤 المستخدمين", self.test_user_dialog, '#e74c3c'),
            ("🏗️ المشاريع", self.test_project_dialog, '#3498db'),
            ("👥 العملاء", self.test_customer_dialog, '#9b59b6'),
            ("🏠 الوحدات", self.test_unit_dialog, '#1abc9c'),
            ("👷 المقاولين", self.test_contractor_dialog, '#f39c12'),
            ("📦 الموردين", self.test_supplier_dialog, '#e67e22'),
            ("📋 المستخلصات", self.test_extract_dialog, '#34495e'),
            ("🧾 الفواتير", self.test_invoice_dialog, '#16a085'),
        ]
        
        # إنشاء أزرار الاختبار
        row = 0
        col = 0
        for name, command, color in test_dialogs:
            btn = tk.Button(
                buttons_frame,
                text=f"اختبار {name}",
                font=('Arial', 12, 'bold'),
                bg=color,
                fg='white',
                width=18,
                height=2,
                relief='raised',
                bd=3,
                command=command
            )
            btn.grid(row=row, column=col, padx=10, pady=10, sticky='ew')
            
            col += 1
            if col > 3:  # أربعة أعمدة
                col = 0
                row += 1
        
        # تكوين الأعمدة
        for i in range(4):
            buttons_frame.columnconfigure(i, weight=1)
        
        # زر اختبار شامل
        comprehensive_test_btn = tk.Button(
            self.root,
            text="🚀 اختبار شامل لجميع النوافذ",
            font=('Arial', 16, 'bold'),
            bg='#27ae60',
            fg='white',
            width=40,
            height=2,
            relief='raised',
            bd=4,
            command=self.comprehensive_test
        )
        comprehensive_test_btn.pack(pady=20)
        
    def test_user_dialog(self):
        """اختبار نافذة المستخدمين"""
        print("🧪 اختبار نافذة المستخدمين...")
        dialog = UserDialog(self.root, "اختبار إضافة مستخدم")
        
        # ملء البيانات تلقائياً
        dialog.username_entry.insert(0, "test_user")
        dialog.email_entry.insert(0, "<EMAIL>")
        dialog.password_entry.insert(0, "123456")
        
        # محاكاة الحفظ
        dialog.save_user()
        
        result = dialog.result
        self.show_test_result("المستخدمين", result)
        
    def test_project_dialog(self):
        """اختبار نافذة المشاريع"""
        print("🧪 اختبار نافذة المشاريع...")
        dialog = ProjectDialog(self.root, "اختبار إضافة مشروع")
        
        # ملء البيانات تلقائياً
        dialog.name_entry.insert(0, "مشروع تجريبي")
        dialog.location_entry.insert(0, "الرياض")
        dialog.cost_entry.insert(0, "1000000")
        
        # محاكاة الحفظ
        dialog.save_project()
        
        result = dialog.result
        self.show_test_result("المشاريع", result)
        
    def test_customer_dialog(self):
        """اختبار نافذة العملاء"""
        print("🧪 اختبار نافذة العملاء...")
        dialog = CustomerDialog(self.root, "اختبار إضافة عميل")
        
        # ملء البيانات تلقائياً
        dialog.name_entry.insert(0, "عميل تجريبي")
        dialog.national_id_entry.insert(0, "1234567890")
        dialog.phone_entry.insert(0, "0501234567")
        dialog.email_entry.insert(0, "<EMAIL>")
        
        # محاكاة الحفظ
        dialog.save_customer()
        
        result = dialog.result
        self.show_test_result("العملاء", result)
        
    def test_unit_dialog(self):
        """اختبار نافذة الوحدات"""
        print("🧪 اختبار نافذة الوحدات...")
        dialog = UnitDialog(self.root, "اختبار إضافة وحدة")
        
        # ملء البيانات تلقائياً
        dialog.unit_number_entry.insert(0, "A101")
        dialog.floor_entry.insert(0, "1")
        dialog.area_entry.insert(0, "120")
        dialog.price_entry.insert(0, "500000")
        
        # محاكاة الحفظ
        dialog.save_unit()
        
        result = dialog.result
        self.show_test_result("الوحدات", result)
        
    def test_contractor_dialog(self):
        """اختبار نافذة المقاولين"""
        print("🧪 اختبار نافذة المقاولين...")
        dialog = ContractorDialog(self.root, "اختبار إضافة مقاول")
        
        # ملء البيانات تلقائياً
        dialog.name_entry.insert(0, "مقاول تجريبي")
        dialog.phone_entry.insert(0, "0501234567")
        dialog.email_entry.insert(0, "<EMAIL>")
        dialog.address_entry.insert(0, "الرياض")
        
        # محاكاة الحفظ
        dialog.save_contractor()
        
        result = dialog.result
        self.show_test_result("المقاولين", result)
        
    def test_supplier_dialog(self):
        """اختبار نافذة الموردين"""
        print("🧪 اختبار نافذة الموردين...")
        dialog = SupplierDialog(self.root, "اختبار إضافة مورد")
        
        # ملء البيانات تلقائياً
        dialog.name_entry.insert(0, "مورد تجريبي")
        dialog.phone_entry.insert(0, "0501234567")
        dialog.email_entry.insert(0, "<EMAIL>")
        dialog.address_entry.insert(0, "الرياض")
        
        # محاكاة الحفظ
        dialog.save_supplier()
        
        result = dialog.result
        self.show_test_result("الموردين", result)
        
    def test_extract_dialog(self):
        """اختبار نافذة المستخلصات"""
        print("🧪 اختبار نافذة المستخلصات...")
        dialog = ExtractDialog(self.root, "اختبار إضافة مستخلص")
        
        # ملء البيانات تلقائياً
        dialog.extract_number_entry.insert(0, "EXT001")
        dialog.contractor_name_entry.insert(0, "مقاول تجريبي")
        dialog.amount_entry.insert(0, "50000")
        
        # محاكاة الحفظ
        dialog.save_extract()
        
        result = dialog.result
        self.show_test_result("المستخلصات", result)
        
    def test_invoice_dialog(self):
        """اختبار نافذة الفواتير"""
        print("🧪 اختبار نافذة الفواتير...")
        dialog = InvoiceDialog(self.root, "اختبار إضافة فاتورة")
        
        # ملء البيانات تلقائياً
        dialog.invoice_number_entry.insert(0, "INV001")
        dialog.supplier_name_entry.insert(0, "مورد تجريبي")
        dialog.amount_entry.insert(0, "25000")
        
        # محاكاة الحفظ
        dialog.save_invoice()
        
        result = dialog.result
        self.show_test_result("الفواتير", result)
        
    def show_test_result(self, dialog_name, result):
        """عرض نتيجة الاختبار"""
        self.test_results[dialog_name] = result
        
        # إنشاء تسمية للنتيجة
        if result:
            status = "✅ نجح"
            color = '#27ae60'
            details = f"تم حفظ {len(result)} حقل"
        else:
            status = "❌ فشل"
            color = '#e74c3c'
            details = "لم يتم حفظ البيانات"
        
        result_label = tk.Label(
            self.results_frame,
            text=f"{dialog_name}: {status} - {details}",
            font=('Arial', 12, 'bold'),
            bg='#2c3e50',
            fg=color
        )
        result_label.pack(pady=5)
        
        print(f"{status} نافذة {dialog_name}: {details}")
        
    def comprehensive_test(self):
        """اختبار شامل لجميع النوافذ"""
        print("🚀 بدء الاختبار الشامل...")
        
        # مسح النتائج السابقة
        for widget in self.results_frame.winfo_children():
            if isinstance(widget, tk.Label) and widget.cget('text') != "📊 نتائج الاختبار":
                widget.destroy()
        
        # اختبار جميع النوافذ
        test_functions = [
            self.test_user_dialog,
            self.test_project_dialog,
            self.test_customer_dialog,
            self.test_unit_dialog,
            self.test_contractor_dialog,
            self.test_supplier_dialog,
            self.test_extract_dialog,
            self.test_invoice_dialog,
        ]
        
        for test_func in test_functions:
            try:
                test_func()
            except Exception as e:
                print(f"❌ خطأ في الاختبار: {e}")
        
        # عرض الملخص
        successful_tests = len([r for r in self.test_results.values() if r])
        total_tests = len(self.test_results)
        
        summary_label = tk.Label(
            self.results_frame,
            text=f"📊 الملخص: {successful_tests}/{total_tests} نوافذ تعمل بنجاح",
            font=('Arial', 16, 'bold'),
            bg='#2c3e50',
            fg='#f39c12'
        )
        summary_label.pack(pady=20)
        
        print(f"🎉 انتهى الاختبار الشامل: {successful_tests}/{total_tests} نوافذ تعمل بنجاح")
        
    def run(self):
        """تشغيل الاختبار"""
        print("🧪 بدء اختبار حفظ البيانات...")
        self.root.mainloop()

if __name__ == "__main__":
    tester = DataSavingTester()
    tester.run()
