#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نافذة المستخلصات
Test Extract Dialog
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

class TestExtractDialog:
    """نافذة اختبار المستخلصات - محسنة"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("اختبار نافذة المستخلصات")
        self.root.geometry("600x800")
        self.root.configure(bg='#2c3e50')
        
        # إنشاء زر لفتح نافذة المستخلصات
        test_btn = tk.Button(
            self.root,
            text="🔧 اختبار نافذة المستخلصات",
            font=('Arial', 16, 'bold'),
            bg='#3498db',
            fg='white',
            width=30,
            height=3,
            command=self.open_extract_dialog
        )
        test_btn.pack(expand=True)
        
        # نتائج الاختبار
        self.result_label = tk.Label(
            self.root,
            text="اضغط الزر لاختبار النافذة",
            font=('Arial', 14),
            bg='#2c3e50',
            fg='#ecf0f1'
        )
        self.result_label.pack(pady=20)
    
    def open_extract_dialog(self):
        """فتح نافذة المستخلصات المحسنة"""
        dialog = ImprovedExtractDialog(self.root, "اختبار المستخلصات")
        self.root.wait_window(dialog.dialog)
        
        if dialog.result:
            self.result_label.config(
                text=f"✅ تم الحفظ بنجاح!\nرقم المستخلص: {dialog.result.get('extract_number', 'غير محدد')}",
                fg='#27ae60'
            )
        else:
            self.result_label.config(
                text="❌ تم الإلغاء أو حدث خطأ",
                fg='#e74c3c'
            )
    
    def run(self):
        """تشغيل الاختبار"""
        self.root.mainloop()

class ImprovedExtractDialog:
    """نافذة المستخلصات المحسنة مع ضمان ظهور الأزرار"""
    
    def __init__(self, parent, title, extract_data=None):
        self.parent = parent
        self.extract_data = extract_data
        self.result = None
        
        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("550x700")  # حجم أكبر قليلاً
        self.dialog.configure(bg='#f5f5f5')
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (550 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (700 // 2)
        self.dialog.geometry(f"550x700+{x}+{y}")
        
        self.create_widgets()
        
        if extract_data:
            self.load_extract_data()
    
    def create_widgets(self):
        """إنشاء عناصر النافذة مع ضمان ظهور الأزرار"""
        
        # العنوان
        title_label = tk.Label(
            self.dialog,
            text="📋 إدارة المستخلصات",
            font=('Arial', 20, 'bold'),
            bg='#f5f5f5',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)
        
        # إطار المحتوى الرئيسي
        main_frame = tk.Frame(self.dialog, bg='#f5f5f5')
        main_frame.pack(fill='both', expand=True, padx=20)
        
        # إطار المحتوى
        content_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=2)
        content_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # رقم المستخلص
        tk.Label(content_frame, text="رقم المستخلص:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(20, 5), padx=20)
        self.extract_number_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.extract_number_entry.pack(pady=(0, 15), padx=20)
        
        # اسم المقاول
        tk.Label(content_frame, text="اسم المقاول:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.contractor_name_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.contractor_name_entry.pack(pady=(0, 15), padx=20)
        
        # قيمة المستخلص
        tk.Label(content_frame, text="قيمة المستخلص:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.amount_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.amount_entry.pack(pady=(0, 15), padx=20)
        
        # تاريخ المستخلص
        tk.Label(content_frame, text="تاريخ المستخلص:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.date_entry = tk.Entry(content_frame, font=('Arial', 12), width=40)
        self.date_entry.pack(pady=(0, 15), padx=20)
        # تعبئة التاريخ الحالي
        self.date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))
        
        # الحالة
        tk.Label(content_frame, text="الحالة:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.status_combo = ttk.Combobox(content_frame, font=('Arial', 12), width=37, state='readonly')
        self.status_combo['values'] = ('قيد المراجعة', 'معتمد', 'مدفوع', 'مرفوض')
        self.status_combo.set('قيد المراجعة')
        self.status_combo.pack(pady=(0, 15), padx=20)
        
        # الوصف
        tk.Label(content_frame, text="الوصف:", font=('Arial', 12, 'bold'), bg='white').pack(anchor='e', pady=(0, 5), padx=20)
        self.description_text = tk.Text(content_frame, font=('Arial', 12), width=40, height=3)
        self.description_text.pack(pady=(0, 20), padx=20)
        
        # إطار الأزرار - مثبت في الأسفل
        buttons_frame = tk.Frame(main_frame, bg='#f5f5f5', height=100)
        buttons_frame.pack(fill='x', side='bottom', pady=10)
        buttons_frame.pack_propagate(False)
        
        # زر الحفظ
        save_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ المستخلص",
            font=('Arial', 14, 'bold'),
            bg='#27ae60',
            fg='white',
            width=18,
            height=2,
            relief='raised',
            bd=3,
            command=self.save_extract
        )
        save_btn.pack(side='right', padx=(10, 0), pady=20)
        
        # زر الإلغاء
        cancel_btn = tk.Button(
            buttons_frame,
            text="❌ إلغاء",
            font=('Arial', 14, 'bold'),
            bg='#e74c3c',
            fg='white',
            width=18,
            height=2,
            relief='raised',
            bd=3,
            command=self.cancel
        )
        cancel_btn.pack(side='right', padx=(10, 10), pady=20)
        
        # التأكد من ظهور الأزرار
        self.dialog.update_idletasks()
        buttons_frame.tkraise()
        save_btn.tkraise()
        cancel_btn.tkraise()
        
        print("✅ تم إنشاء نافذة المستخلصات المحسنة!")
        print(f"📍 حجم النافذة: {self.dialog.winfo_width()}x{self.dialog.winfo_height()}")
        print(f"📍 موقع الأزرار: في الأسفل")
        
        # تعبئة بيانات تجريبية
        self.extract_number_entry.insert(0, "EXT-2025-001")
        self.contractor_name_entry.insert(0, "شركة المقاولات المتقدمة")
        self.amount_entry.insert(0, "75000.00")
        self.description_text.insert('1.0', "مستخلص أعمال البناء للشهر الحالي")
    
    def load_extract_data(self):
        """تحميل بيانات المستخلص للتعديل"""
        if self.extract_data:
            self.extract_number_entry.delete(0, tk.END)
            self.extract_number_entry.insert(0, self.extract_data.get('extract_number', ''))
            
            self.contractor_name_entry.delete(0, tk.END)
            self.contractor_name_entry.insert(0, self.extract_data.get('contractor_name', ''))
            
            self.amount_entry.delete(0, tk.END)
            self.amount_entry.insert(0, str(self.extract_data.get('amount', '')))
            
            self.date_entry.delete(0, tk.END)
            self.date_entry.insert(0, self.extract_data.get('extract_date', ''))
            
            self.status_combo.set(self.extract_data.get('status', 'قيد المراجعة'))
            
            self.description_text.delete('1.0', tk.END)
            self.description_text.insert('1.0', self.extract_data.get('description', ''))
    
    def save_extract(self):
        """حفظ بيانات المستخلص"""
        print("🔄 بدء عملية الحفظ...")
        
        # التحقق من صحة البيانات
        if not self.extract_number_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال رقم المستخلص")
            return
        
        if not self.contractor_name_entry.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المقاول")
            return
        
        try:
            # تحضير البيانات
            extract_data = {
                'extract_number': self.extract_number_entry.get().strip(),
                'contractor_name': self.contractor_name_entry.get().strip(),
                'amount': float(self.amount_entry.get()) if self.amount_entry.get().strip() else 0.0,
                'extract_date': self.date_entry.get().strip(),
                'status': self.status_combo.get(),
                'description': self.description_text.get('1.0', tk.END).strip(),
                'created_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'created_by': 1
            }
            
            self.result = extract_data
            print("✅ تم تحضير البيانات بنجاح!")
            messagebox.showinfo("نجح", "تم حفظ بيانات المستخلص بنجاح!")
            self.dialog.destroy()
            
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيمة صحيحة للمبلغ")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات: {str(e)}")
    
    def cancel(self):
        """إلغاء العملية"""
        print("❌ تم إلغاء العملية")
        self.result = None
        self.dialog.destroy()

if __name__ == "__main__":
    print("🔧 بدء اختبار نافذة المستخلصات...")
    tester = TestExtractDialog()
    tester.run()
