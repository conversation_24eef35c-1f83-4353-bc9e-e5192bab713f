#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح الأخطاء المكتشفة
Fix Detected Errors
"""

def fix_invoice_date_error():
    """إصلاح خطأ تاريخ الفاتورة"""
    print("🔧 إصلاح خطأ تاريخ الفاتورة...")
    
    try:
        # قراءة ملف النوافذ
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن نافذة الفواتير وإضافة التاريخ
        # إضافة التاريخ الحالي تلقائياً
        if 'self.date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))' not in content:
            # البحث عن مكان إدراج التاريخ
            content = content.replace(
                'self.date_entry.pack(pady=(0, 15), padx=20)',
                '''self.date_entry.pack(pady=(0, 15), padx=20)
        # تعبئة التاريخ الحالي تلقائياً
        self.date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))'''
            )
        
        # التأكد من أن البيانات تحتوي على invoice_date
        content = content.replace(
            "'invoice_date': self.date_entry.get().strip(),",
            "'invoice_date': self.date_entry.get().strip() or datetime.now().strftime('%Y-%m-%d'),"
        )
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح خطأ تاريخ الفاتورة!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح تاريخ الفاتورة: {e}")
        return False

def fix_purchase_status_error():
    """إصلاح خطأ حالة المشتريات"""
    print("🔧 إصلاح خطأ حالة المشتريات...")
    
    try:
        # قراءة ملف النوافذ
        with open('dialogs.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح قيم حالة المشتريات لتطابق قاعدة البيانات
        content = content.replace(
            "self.status_combo['values'] = ('pending', 'approved', 'ordered', 'received', 'cancelled')",
            "self.status_combo['values'] = ('pending', 'approved', 'rejected', 'completed')"
        )
        
        # كتابة الملف المحدث
        with open('dialogs.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح خطأ حالة المشتريات!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح حالة المشتريات: {e}")
        return False

def fix_maintenance_title_error():
    """إصلاح خطأ عنوان مهمة الصيانة"""
    print("🔧 إصلاح خطأ عنوان مهمة الصيانة...")
    
    try:
        # قراءة الملف الرئيسي
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح استعلام الصيانة
        content = content.replace(
            "INSERT INTO maintenance_tasks (task_number, maintenance_type, location, technician, date, cost, status, description)",
            "INSERT INTO maintenance_tasks (task_number, task_title, maintenance_type, location, technician, date, cost, status, description)"
        )
        
        # إصلاح المعاملات
        old_maintenance_params = """sqlite_manager.execute_query(query, (
                    result.get('task_number', ''),
                    result.get('task_description', ''),  # النافذة ترسل task_description
                    'مكان العمل',  # location ثابت
                    result.get('assigned_to', ''),  # النافذة ترسل assigned_to
                    result.get('created_date', ''),  # النافذة ترسل created_date
                    0,  # cost ثابت
                    result.get('status', ''),
                    result.get('task_description', '')  # النافذة ترسل task_description
                ))"""
        
        new_maintenance_params = """sqlite_manager.execute_query(query, (
                    result.get('task_number', ''),
                    result.get('task_description', ''),  # task_title
                    result.get('task_description', ''),  # maintenance_type
                    'مكان العمل',  # location ثابت
                    result.get('assigned_to', ''),  # technician
                    result.get('created_date', ''),  # date
                    0,  # cost ثابت
                    result.get('status', ''),
                    result.get('task_description', '')  # description
                ))"""
        
        content = content.replace(old_maintenance_params, new_maintenance_params)
        
        # كتابة الملف المحدث
        with open('rafea_complete_system.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح خطأ عنوان مهمة الصيانة!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح عنوان مهمة الصيانة: {e}")
        return False

def fix_daily_task_title_error():
    """إصلاح خطأ عنوان المهمة اليومية"""
    print("🔧 إصلاح خطأ عنوان المهمة اليومية...")
    
    try:
        # قراءة الملف الرئيسي
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح استعلام المهام اليومية
        content = content.replace(
            "INSERT INTO daily_tasks (title, description, assigned_to, priority, due_date, status, created_by)",
            "INSERT INTO daily_tasks (task_title, description, assigned_to, priority, due_date, status, created_by)"
        )
        
        # إصلاح المعاملات
        old_daily_params = """sqlite_manager.execute_query(query, (
                    result.get('title', ''),
                    result.get('description', ''),
                    'المستخدم الحالي',  # assigned_to ثابت
                    result.get('priority', ''),
                    result.get('due_date', ''),
                    result.get('status', ''),
                    self.user_data['id']  # المستخدم الحالي
                ))"""
        
        new_daily_params = """sqlite_manager.execute_query(query, (
                    result.get('title', ''),  # task_title
                    result.get('description', ''),
                    'المستخدم الحالي',  # assigned_to ثابت
                    result.get('priority', ''),
                    result.get('due_date', ''),
                    result.get('status', ''),
                    self.user_data['id']  # المستخدم الحالي
                ))"""
        
        content = content.replace(old_daily_params, new_daily_params)
        
        # كتابة الملف المحدث
        with open('rafea_complete_system.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح خطأ عنوان المهمة اليومية!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح عنوان المهمة اليومية: {e}")
        return False

def fix_invoice_query():
    """إصلاح استعلام الفواتير"""
    print("🔧 إصلاح استعلام الفواتير...")
    
    try:
        # قراءة الملف الرئيسي
        with open('rafea_complete_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إصلاح استعلام الفواتير
        content = content.replace(
            "INSERT INTO invoices (invoice_number, supplier, amount, date, due_date, status, description)",
            "INSERT INTO invoices (invoice_number, supplier, amount, invoice_date, due_date, status, description)"
        )
        
        # كتابة الملف المحدث
        with open('rafea_complete_system.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح استعلام الفواتير!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح استعلام الفواتير: {e}")
        return False

def test_all_fixes():
    """اختبار جميع الإصلاحات"""
    print("🧪 اختبار جميع الإصلاحات...")
    
    try:
        # اختبار استيراد النوافذ
        from dialogs import (
            ExtractDialog, InvoiceDialog, PurchaseRequestDialog,
            MaintenanceTaskDialog, DailyTaskDialog
        )
        
        print("✅ جميع النوافذ تستورد بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإصلاحات: {e}")
        return False

if __name__ == "__main__":
    print("=" * 70)
    print("🔧 إصلاح الأخطاء المكتشفة")
    print("=" * 70)
    
    # تشغيل جميع الإصلاحات
    fixes = [
        ("إصلاح خطأ تاريخ الفاتورة", fix_invoice_date_error),
        ("إصلاح خطأ حالة المشتريات", fix_purchase_status_error),
        ("إصلاح خطأ عنوان مهمة الصيانة", fix_maintenance_title_error),
        ("إصلاح خطأ عنوان المهمة اليومية", fix_daily_task_title_error),
        ("إصلاح استعلام الفواتير", fix_invoice_query),
        ("اختبار جميع الإصلاحات", test_all_fixes),
    ]
    
    success_count = 0
    for name, fix_func in fixes:
        print(f"\\n🔧 {name}...")
        if fix_func():
            success_count += 1
    
    print(f"\\n📊 النتيجة النهائية: {success_count}/{len(fixes)} إصلاحات نجحت")
    
    if success_count == len(fixes):
        print("\\n🎉 تم إصلاح جميع الأخطاء المكتشفة بنجاح!")
        print("يمكنك الآن تشغيل النظام بدون أخطاء.")
    else:
        print("\\n⚠️ بعض الإصلاحات فشلت. راجع الأخطاء أعلاه.")
    
    input("\\nاضغط Enter للخروج...")
